import * as fs from 'fs/promises';
import path from 'path';

// 자동으로 latest.json의 signature를 update 하는 스크립트
const updateSignature = async () => {
	try {
		// 최신 JSON 읽기
		const latestJsonPath = path.join(process.cwd(), 'latest.json');
		const latestData = await fs.readFile(latestJsonPath, 'utf8');
		const latestJson = JSON.parse(latestData);

		const { version } = latestJson;
		const sigFilename = `CnsproWMS_${version}_x64-setup.exe.sig`;
		const sigFilePath = path.join(process.cwd(), 'src-tauri', 'target', 'release', 'bundle', 'nsis', sigFilename);

		// 서명 파일 읽기
		const newSignature = await fs.readFile(sigFilePath, 'utf8');

		// 최신 JSON에 서명 업데이트
		latestJson.signature = newSignature.trim();

		// updated latest.json 파일 작성
		await fs.writeFile(latestJsonPath, JSON.stringify(latestJson, null, 2), 'utf8');

		console.log(`👍 "latest.json"에 서명이 성공적으로 업데이트되었습니다. (version: ${version})`);
	} catch (error) {
		console.error('❌ 서명 업데이트 중 오류가 발생했습니다!', error);
	}
};

// 함수 실행
updateSignature();