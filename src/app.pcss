/* Write your global styles here, in PostCSS syntax */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Customization  */
@layer components {
    .main-section {
        @apply px-1.5 py-3 h-full;
    }

    .aside {
        @apply w-64 top-0 z-40 fixed h-screen bg-base-100 transition-all overflow-y-auto;
    }

    .aside-desktop {
        @apply flex flex-row w-full text-base-content flex-1 px-3 h-14 items-center;
    }

    .aside-mobile {
        @apply w-60 top-0 left-0 z-40 fixed px-3 transition-all;
    }

    /* Aside End  */

    /* TopNav - Dropdown Submenu */
    .top-nav-submenu {
        @apply menu menu-sm dropdown-content mt-3 z-[1] p-2 shadow bg-base-300 rounded-box w-44 border border-base-300;
    }

    /* Print Barcode */
    #barcode {
        width: 35mm;
        height: 12mm;
        margin: auto;
    }

    #barcode_canvas {
        width: 51mm;
        height: 40mm;
        @apply px-1 flex flex-col justify-center items-center bg-white font-bold;
    }

    @media print {
        html, body { -webkit-print-color-adjust:exact; width: 51mm; height: 40mm; }
        body * {
            @apply invisible;
        }

        #barcode_canvas,
        #barcode_canvas * {
            @apply visible;
        }

        #barcode_canvas {
            @apply absolute inset-x-0 inset-y-0;
        }
    }
}