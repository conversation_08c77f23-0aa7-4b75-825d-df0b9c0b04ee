import type { User } from '$lib/User';
import { APP_NAME, DIR_NAME, LABEL_PRINTER_ID_LIST } from '$stores/constant';
import { goto } from '$app/navigation';
import { ask, message } from '@tauri-apps/plugin-dialog';
import { BaseDirectory, exists, remove } from '@tauri-apps/plugin-fs';
import { printers } from 'tauri-plugin-printer-api';
import { toast } from 'svoast';
import { authClient } from '$lib/AxiosBackend';
import { saveAs } from 'file-saver';
import Canvas from 'canvas';
import JsBarcode from 'jsbarcode';
import { WebviewWindow } from '@tauri-apps/api/webviewWindow';

/**
 * Tauri의 dialog API중 message를 짧게 사용하기 위해 사용.
 *
 * @param msg
 * @param kind
 */
export async function executeMessage(
	msg: string,
	kind: 'info' | 'warning' | 'error' | undefined = 'info'
) {
	await message(msg, {
		title: APP_NAME,
		kind: kind
	});
}

export async function executeAsk(
	msg: string,
	kind: 'info' | 'warning' | 'error' | undefined = 'info'
) {
	return await ask(msg, {
		title: APP_NAME,
		kind: kind,
		okLabel: '예',
		cancelLabel: '아니오'
	});
}

/**
 * 리디렉트
 */
export async function redirectUrl(url: string) {
	await goto('/redirect').then(() => goto(url));
}

/**
 * 관리자인지 체크 : 관리자가 아니면 지정된 주소로 보내기
 * @param user
 * @param gotoUrl
 */
export async function checkAdminAndRedirect(user: User, gotoUrl: string) {
	const adminRoles = ['Super-Admin', 'Admin'];

	if (!user || !adminRoles.includes(user.role)) {
		await executeMessage('관리자만 접근할 수 있는 페이지 입니다.', 'error');
		await goto(gotoUrl);
	}
}

const regionMap: Record<string, { locale: string; timezone: string }> = {
	ko: { locale: 'ko-KR', timezone: 'Asia/Seoul' },
	tw: { locale: 'zh-TW', timezone: 'Asia/Taipei' }
};

/**
 * 지역에 따른 날자 변경
 *
 * @param dt
 * @param region
 * @param format
 */
export function dateFormat(
	dt?: string,
	region: string = 'ko', // 기본값: 한국
	format: 'date' | 'datetime' | 'full' = 'full'
) {
	const { locale, timezone } = regionMap[region] || { locale: 'ko-KR', timezone: 'Asia/Seoul' };

	// dt 값이 없을 경우 현재 날짜로 설정
	const date = dt
		? /Z$|[+-]\d{2}:\d{2}$/.test(dt)
			? new Date(dt)       // 이미 시간대 정보가 있는 경우
			: new Date(`${dt}Z`) // 시간대 정보가 없는 경우 Z 추가
		: new Date();

	let options: Intl.DateTimeFormatOptions;

	if (format === 'date') {
		options = { timeZone: timezone, year: 'numeric', month: 'numeric', day: 'numeric' };
	} else if (format === 'datetime') {
		options = { timeZone: timezone, year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric', hour12: false };
	} else {
		options = { timeZone: timezone, year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric', second: 'numeric', hour12: false };
	}

	return new Intl.DateTimeFormat(locale, options).format(date);
}

/**
 * Date 객체를 문자열(YYYY-MM-DD)로 변환하는 함수
 *
 * @param date
 */
export function formatDateToString(date: Date) {
	return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
}

/**
 * Date 객체를 문자열(YYYY-MM-DD)로 변환하는 함수
 *
 * @param date
 */
export function formatDateTimeToString(date: string) {
	if (!date) return '';

	const datetime = new Date(date);

	const YYYY = datetime.getFullYear();
	const MM = ('0' + (datetime.getMonth() + 1)).slice(-2);
	const DD = ('0' + datetime.getDate()).slice(-2);

	return `${YYYY}-${MM}-${DD}`;
}

/**
 * Date 객체를 문자열(YYYY-MM-DD HH:mm)로 변환하는 함수
 *
 * @param date
 */
export function formatDateTimeToFullString(date: string) {
	if (!date) return '';

	const datetime = new Date(date);

	const YYYY = datetime.getFullYear();
	const MM = ('0' + (datetime.getMonth() + 1)).slice(-2);
	const DD = ('0' + datetime.getDate()).slice(-2);
	const hh = ('0' + datetime.getHours()).slice(-2);
	const mm = ('0' + datetime.getMinutes()).slice(-2);

	return `${YYYY}-${MM}-${DD} ${hh}:${mm}`;
}

/**
 * 숫자에 , 를 넣는다.
 *
 * @param number
 * @param region
 */
export function getNumberFormat(number: number, region: string = 'ko') {
	let locale = 'ko-KR';
	if (region === 'tw') {
		locale = 'zh-TW';
	}

	return new Intl.NumberFormat(locale).format(Number(number));
}

/**
 * SVG 아이콘 클릭시 클릭한 위치에 따라 태그 찾아 href를 가져와야 하는 경우가 있음
 *
 * @param target
 */
export function getHrefFromEventTarget(target: HTMLElement): string | undefined {
	const tagName = target.tagName.toLowerCase();

	switch (tagName) {
		case 'svg':
			return (target.parentElement as HTMLAnchorElement)?.href;
		case 'path':
			return (target.parentElement?.parentElement as HTMLAnchorElement)?.href;
		case 'a':
			return (target as HTMLAnchorElement).href;
		default:
			return undefined;
	}
}

export function getOneYearAgo() {
	const oneYearAgo = new Date();
	oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);
	oneYearAgo.setDate(1);

	return formatDateToString(oneYearAgo);
}

export function getYesterday() {
	const yesterday = new Date();
	yesterday.setDate(yesterday.getDate() - 1);

	return formatDateToString(yesterday);
}

/**
 * 시작 날짜가 없을 경우 3개월 전 1일자 날짜를 가져온다.
 */
export function getBeginAt(month: number = 3) {
	const date = new Date();
	date.setMonth(date.getMonth() - month);
	date.setDate(1);

	return formatDateToString(date);
}

/**
 * id에 해당하는 곳으로 페이지를 이동시킨다.
 *
 * @param id
 */
export function scrollToElement(id: string) {
	const elem = document.getElementById(id);
	if (elem) {
		elem.scrollIntoView();
	} else {
		console.log(`[${id}]엘리먼트를 찾을 수 없습니다.`);
	}
}

/**
 * 클립보드로 복사
 *
 * @param text
 */
export async function copyToClipboard(text: string) {
	try {
		await window.navigator.clipboard.writeText(text);
		toast.success('복사되었습니다.');
	} catch (error) {
		toast.error(`복사 실패: ${error}`);
	}
}

/**
 * 팔레트 번호 가져오기 (전체 버전)
 */
export function makeLocationInfo(loc) {
	const location = {
		country: 'KR',
		city: 'CJ',
		store: '',
		line: '',
		rack: '',
		level: '',
		column: '',
		location_name: '',
		pallet_code: ''
	};

	const locationCode = loc.code;
	const locationPlace = loc.place;
	const locationName = loc.name;

	const locationCdArr = locationCode.split('-');
	const locationPlaceArr = locationPlace.split('-');

	if (locationPlaceArr.length === 2) {
		location.country = locationPlaceArr[0];
		location.city = locationPlaceArr[1];
	}

	if (locationCdArr.length === 1) {
		location.store = locationCdArr[0];
		location.line = '';
		location.rack = '';
		location.level = '';
		location.column = '';
		location.location_name = '한국 충주 ' + locationName;
	} else if (locationCdArr.length === 2) {
		location.store = locationCdArr[0];
		location.line = locationCdArr[1];
		location.rack = '';
		location.level = '';
		location.column = '';
		location.location_name = '한국 충주 ' + locationName;
	} else if (locationCdArr.length === 3) {
		location.store = locationCdArr[0];
		location.line = locationCdArr[1];
		location.rack = locationCdArr[2];
		location.level = '';
		location.column = '';
		location.location_name = '한국 충주 ' + locationName;
	} else if (locationCdArr.length === 4) {
		location.store = locationCdArr[0];
		location.line = locationCdArr[1];
		location.rack = locationCdArr[2];
		location.level = locationCdArr[3];
		location.column = '';
		location.location_name = '한국 충주 ' + locationName;
	} else if (locationCdArr.length === 5) {
		location.store = locationCdArr[0];
		location.line = locationCdArr[1];
		location.rack = locationCdArr[2];
		location.level = locationCdArr[3];
		location.column = locationCdArr[4];
	} else if (locationCdArr.length >= 7) {
		location.country = locationCdArr[0];
		location.city = locationCdArr[1];
		location.store = locationCdArr[2];
		location.line = locationCdArr[3];
		location.rack = locationCdArr[4];
		location.level = locationCdArr[5];
		location.column = locationCdArr[6];
	}

	location.pallet_code = location.level + '-' + location.column;

	return location;
}

export function generateRandomNumber() {
	return Math.floor(1000000000 + Math.random() * 9000000000);
}

/**
 * 엑셀로 다운로드
 */
export async function handleDownload(url: string, payload: object) {
	try {
		const response = await authClient.post(url, payload, {
			responseType: 'arraybuffer',
			headers: {
				Accept:
					'application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
			}
		});

		const blob = new Blob([response.data], {
			type: response.headers['content-type']
		});

		let filename = '';
		const disposition = response.headers['content-disposition'];
		if (disposition && disposition.indexOf('attachment') !== -1) {
			const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
			const matches = filenameRegex.exec(disposition);
			if (matches && matches[1]) {
				filename = decodeURIComponent(matches[1].replace(/['"]/g, ''));
			}
		}

		saveAs(blob, filename);
	} catch (error) {
		await executeMessage(
			'다운로드 에러 (다시 시도해 보세요)\n만약 그래도 안 되면 프로그래머에게 문의 해 주세요.\n\n' + error,
			'error'
		);
	}
}

export function getPayload(data: string): { [key: string]: string | number[] } {
	return data.split('&').reduce((result: { [key: string]: string }, item) => {
		const parts = item.split('=');
		result[parts[0]] = parts[1];

		return result;
	}, {});
}

/**
 * Generates a barcode image from the given barcode string using the specified font.
 *
 * @param {string} barcode - The barcode string.
 * @param options
 * @returns {string} - The data URL of the generated barcode image in PNG format.
 */
export const makeBarcodeImage = async (
	barcode: string,
	options: object | null = null
): Promise<string> => {
	const canvasElement: Canvas.Canvas = Canvas.createCanvas(150, 50);

	const defaultOptions = {
		format: 'CODE128',
		width: 2,
		height: 50,
		displayValue: true,
		text: undefined,
		fontOptions: '',
		font: 'sans-serif',
		textAlign: 'center',
		textPosition: 'bottom',
		textMargin: 2,
		fontSize: 25,
		background: '#ffffff',
		lineColor: '#000000',
		margin: 2,
		marginTop: undefined,
		marginBottom: undefined,
		marginLeft: undefined,
		marginRight: undefined,
		flat: true
	};

	JsBarcode(canvasElement, barcode, Object.assign(defaultOptions, options));

	return canvasElement.toDataURL('image/png');
};

export const deleteFile = async (barcode: string) => {
	const filePath = `${DIR_NAME}/${barcode}.pdf`;

	return await exists(filePath, { baseDir: BaseDirectory.Document })
		.then(async () => {
			await remove(filePath, { baseDir: BaseDirectory.Document });
			return true;
		})
		.catch(() => {
			return false;
		});
};

export function setDefaultPrinter(print: object) {
	window.localStorage.setItem('defaultPrint', JSON.stringify(print));
}

export function getDefaultPrinter() {
	const printer: string | null = window.localStorage.getItem('defaultPrint');

	return printer ? JSON.parse(printer) : null;
}

export async function getLocalLabelPrintList() {
	// 기본으로 설정된 프린트
	const defaultPrint = getDefaultPrinter();

	// 현재 컴퓨터에 연결된 프린트 리스트
	const printerList = await printers();

	// 사용 가능한 라벨 프린터 리스트
	let myPrinters;
	if (printerList && printerList.length > 0) {
		myPrinters = printerList.filter((item) =>
			LABEL_PRINTER_ID_LIST.some((id) => item.id.includes(id))
		);

		// 설정된 기본 프린트가 없으면 첫 번째 프린트를 기본 프린트로 설정
		if (!defaultPrint && myPrinters.length > 0) {
			setDefaultPrinter(myPrinters[0]);
		}

		return myPrinters;
	}
}

// Aging 상품 표시(RG: 30일, 일반: 60일)
export function isOverDueDate(reqDateStr: string, isRg: string = 'N') {
	const reqDate: Date = new Date(reqDateStr);
	const currentDate: Date = new Date();

	const diffTime = Math.abs(currentDate - reqDate);
	const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

	const dueDateLimit = isRg === 'Y' ? 30 : 60;

	return diffDays > dueDateLimit;
}

/**
 * 에러를 처리하는 공용 함수
 * @param {any} e - Axios 에러 객체
 */
export async function handleCatch(e: any): Promise<void> {
	if (e.response) {
		const { status, data } = e.response;

		if (status === 422 && data && data.message) {
			// 422 Unprocessable Entity
			await executeMessage(data.message, 'error');
		} else if (status >= 400 && status < 500) {
			// 4xx Client-side error
			await executeMessage('잘못된 요청입니다. 다시 확인해 주세요.', 'error');
		} else if (status >= 500) {
			// 5xx Server-side error
			await executeMessage('서버 오류가 발생했습니다. 잠시 후 다시 시도해 주세요.', 'error');
		}
	} else {
		// 네트워크 이슈 또는 기타 알 수 없는 에러
		await executeMessage('알 수 없는 오류가 발생했습니다. 다시 시도해 주세요.', 'error');
	}
}

/**
 * 파일 업로드 처리 핸들러
 * 2MB 이상 파일은 업로드 제한
 */
export async function checkFileSize(event: Event) {
	const input = event.target as HTMLInputElement;
	const file = input.files?.[0];

	if (file) {
		// 파일 크기 확인 (바이트 단위, 2MB = 2 * 1024 * 1024 바이트)
		const maxSize = 2 * 1024 * 1024; // 2MB

		// 파일 크기가 2MB를 초과하는 경우
		if (file.size > maxSize) {
			await executeMessage("파일 크기는 2MB를 초과할 수 없습니다.");
			input.value = '';

			return;
		}
	}
}

// F5 키 또는 ESC 키 누르면 새로고침 이벤트 핸들러
export function reloadPageOnEscapeOrF5(event: KeyboardEvent) {
	if (event.key === 'Escape' || event.key === 'F5') {
		window.location.reload();
	}
}

export function openWindow(name: string, url: string) {
	new WebviewWindow(name, {
		url: url,
		width: 1280,
		height: 1024
	});
}