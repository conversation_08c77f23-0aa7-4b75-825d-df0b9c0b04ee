import {browser} from "$app/environment";
import { error } from '@sveltejs/kit';

export type User = {
    id: number;
    company_id: string;
    role: string;
    part: string;
    position: string;
    username: string;
    name: string;
    email: string;
    cellphone: string;
    status: number;
    menu: string;
    login_at: Date | null;
    login_ip: string | null;
    login_os: string | null;
    created_at: Date;
    updated_at: Date;
    deleted_at: Date;
};

/**
 * Checks if a user is currently logged in.
 * @returns {boolean} - Returns true if a user is logged in, otherwise false.
 */
export const isUser = (): boolean => {
    if (!browser) {
        return false;
    }

    return !!window.sessionStorage.getItem('user');
}

export const setUser = async (user: User) => {
    if (!browser) {
        return false;
    }

    const { menu, ...rest } = user;

    window.sessionStorage.setItem('user', JSON.stringify(rest));
    window.sessionStorage.setItem('menu', JSON.stringify(menu));

    document.cookie = `login=true; max-age=7200; path=/; SameSite=Strict;`;
}

export const getUser = (): User => {
    if (!browser) {
        error(500, "클라이언트에서 브라우저를 실행할 수 없습니다.");
    }

    const user = window.sessionStorage.getItem('user');

    if (user) {
        try {
            return JSON.parse(user) as User;
        } catch (e) {
            error(500, {
                message: `직원 데이터 파싱 에러: ${e}`,
            });
        }
    }

    error(500, "개인정보를 가져올 수 없습니다.");
}

export const removeUser = async () => {
    if (!browser) {
        return false;
    }

    window.sessionStorage.removeItem('user');
    window.sessionStorage.removeItem('menu');

    document.cookie = `login=true; max-age=0; path=/; SameSite=Strict;`;
}
