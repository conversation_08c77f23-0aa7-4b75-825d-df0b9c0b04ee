export const themes = [
    'light',
    'dark',
    'cupcake',
    'bumblebee',
    'emerald',
    'corporate',
    'synthwave',
    'retro',
    'cyberpunk',
    'valentine',
    'halloween',
    'garden',
    'forest',
    'aqua',
    'lofi',
    'pastel',
    'fantasy',
    'wireframe',
    'black',
    'luxury',
    'dracula',
    'cmyk',
    'autumn',
    'business',
    'acid',
    'lemonade',
    'night',
    'coffee',
    'winter'
];

export function setTheme(theme: string) {
    if (themes.includes(theme)) {
        const one_year = 60 * 60 * 24 * 365;

        window.localStorage.setItem('theme', theme);
        document.cookie = `theme=${theme}; max-age=${one_year}; path=/; SameSite=Strict;`;
        document.documentElement.setAttribute('data-theme', theme);
    }
}