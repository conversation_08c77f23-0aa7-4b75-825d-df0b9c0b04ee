import type { User } from '$lib/User';
import pdfMake from 'pdfmake/build/pdfmake';
import pdfFonts from '$lib/vfs_fonts';
import type { TDocumentDefinitions } from 'pdfmake/interfaces';
import {
	deleteFile,
	executeAsk,
	executeMessage,
	getDefaultPrinter,
	handleCatch,
	makeBarcodeImage
} from '$lib/Functions';
import { authClient } from '$lib/AxiosBackend';
import { BaseDirectory, exists, mkdir, writeFile } from '@tauri-apps/plugin-fs';
import { DIR_NAME } from '$stores/constant';
import { documentDir } from '@tauri-apps/api/path';
import { print_file } from 'tauri-plugin-printer-api';

async function addDateInLabel(user: User) {
	const date = new Date();
	const month = (date.getMonth() + 1).toString().padStart(2, '0');
	const day = date.getDate().toString().padStart(2, '0');
	const year = date.getFullYear().toString().substring(2);
	const hours = date.getHours().toString().padStart(2, '0');
	const minutes = date.getMinutes().toString().padStart(2, '0');

	return `${month}/${day}/${year} ${hours}:${minutes} #${user.id}`;
}

async function getDocDefinition(qaid: string, user: User) {
	pdfMake.vfs = pdfFonts;
	pdfMake.fonts = {
		Roboto: {
			normal: 'Roboto-Regular.ttf',
			bold: 'Roboto-Medium.ttf',
			italics: 'Roboto-Italic.ttf',
			bolditalics: 'Roboto-MediumItalic.ttf',
		},
	};

	const point = 2.83465;
	const width = Math.round(50.8 * point);
	const height = Math.round(40 * point);

	const docDefinition: TDocumentDefinitions = {
		pageSize: {
			width,
			height
		},
		pageOrientation: 'landscape',
		pageMargins: [4, 0, 4, 0],
		content: [
			{
				image: 'barcode',
				width: width - 20,
				style: 'image'
			},
			{
				text: await addDateInLabel(user),
				style: 'datetime'
			}
		],
		images: {
			barcode: await makeBarcodeImage(qaid, {
				fontSize: 20
			})
		},
		defaultStyle: {
			font: 'Roboto',
			margin: [0, 0, 0, 0]
		},
		styles: {
			image: {
				alignment: 'center',
				margin: [0, 15]
			},
			datetime: {
				fontSize: 7,
				bold: false,
				alignment: 'right',
				margin: [0, 0, 5, 0],
				lineHeight: 0.8
			}
		}
	};

	return docDefinition;
}

async function printLabel(qaid: string, user: User) {
	// 디렉토리 존재 여부 확인
	const dirExists = await exists(DIR_NAME, { baseDir: BaseDirectory.Document });
	// 디렉토리가 존재하지 않으면 생성
	if (!dirExists) {
		try {
			await mkdir(DIR_NAME, { baseDir: BaseDirectory.Document });
		} catch (error) {
			await executeMessage(`디렉토리 생성 중 오류가 발생했습니다: ${error}`, 'error');
			return; // 디렉토리 생성 실패 시 함수 종료
		}
	}

	const defaultPrint = getDefaultPrinter();

	// 저장될 PDF 파일경로
	const writeFilePath = `${DIR_NAME}/${qaid}.pdf`;

	const documentDirPath = await documentDir();
	const readFilePath = `${documentDirPath}${writeFilePath}`;

	const docDefinition = await getDocDefinition(qaid, user);
	pdfMake.createPdf(docDefinition).getBuffer((buffer) => {
		const contents = new Uint8Array(buffer);

		// pdf 저장 후 프린트
		writeFile(writeFilePath, contents, { baseDir: BaseDirectory.Document }).then(() => {
			print_file({
				id: defaultPrint.id, // "QklYT0xPTiBTTFAtRDIyMA=="
				name: defaultPrint.name, // "BIXOLON SLP-D220"
				path: readFilePath,
				file: buffer,
				print_setting: {
					orientation: 'landscape',
					method: 'simplex', // duplex | simplex | duplexshort
					paper: 'tabloid', // "A2" | "A3" | "A4" | "A5" | "A6" | "letter" | "legal" | "tabloid"
					scale: 'fit', //"noscale" | "shrink" | "fit"
					repeat: 1,
					range: '1'
				},
				remove_temp: true
			}).then(() => {
				deleteFile(qaid)
					.then(() => {
						console.log(`${qaid}.pdf 파일삭제 완료`);
					})
					.catch(() => {
						console.log(`${qaid}.pdf 파일삭제 실패`);
					});
			});
		});
	});
}

/**
 * qaid 재발행::일반 작업자<br>
 * 서버에서 qaid 재발행 리스트에 출력 여부 확인하고 없다면 출력
 *
 * @param qaid
 * @param id
 */
export async function isRePrint(qaid: string, id: number, user: User) {
	// QAID 재발행 테이블에서 중복확인
	try {
		const { status, data } = await authClient.get(`/wms/qaids/${qaid}/${id}`);

		if (status === 200 && data.success === false) {
			await executeMessage(data.data.message, 'error');
		} else {
			await printLabel(qaid, user);
		}
	} catch (e: any) {
		await handleCatch(e);
		return false;
	}
}

/**
 * qaid 재발행::관리자<br>
 *
 * @param qaid
 * @param id
 */
export async function isRePrintFromAdmin(qaid: string, id: number, user: User) {
	// QAID 재발행 테이블에서 중복확인
	try {
		const { status, data } = await authClient.put(`/wms/qaids/${qaid}/${id}`);

		if (status === 200 && data.success) {
			await printLabel(qaid, user);
		} else {
			await executeMessage(data.data.message, 'error');
		}
	} catch (e: any) {
		await handleCatch(e);
		return false;
	}
}