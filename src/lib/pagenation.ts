export interface PageData {
    total: number;
    current_page: number;
    from: number;
    to: number;
    last_page: number;
    per_page: number;
    prev_page_url: string|null;
    next_page_url: string|null;
    links: Array<{label: string|null, active: string|null, url: string|null}>;
}

export async function processAPIData(pageData: PageData) {
    const deleteStr = import.meta.env.VITE_DELETE_URL_STRING;

    const pageCurrentPage = pageData.current_page;
    const pageTotal = pageData.total;
    const pageFrom = pageData.from;
    const pageTo = pageData.to;
    const pageLastPage = pageData.last_page;
    const pagePerPage = pageData.per_page;
    let pagePrevPageUrl = pageData.prev_page_url;
    let pageNextPageUrl = pageData.next_page_url;
    const pageStartNo = pageTotal - pageFrom + 1;

    if (pagePrevPageUrl) {
        pagePrevPageUrl = pagePrevPageUrl.replace(deleteStr, "");
    }

    if (pageNextPageUrl) {
        pageNextPageUrl = pageNextPageUrl.replace(deleteStr, "");
    }

    let pageLinks = null;
    if (pageData.links.length > 2) {
        const links = pageData.links.slice(1, -1);

        pageLinks = links.map((item) => {
            if (item.url) {
                item.url = item.url.replace(deleteStr, "");
            }
            return item;
        })
    }

    return {
        pageTotal,
        pageCurrentPage,
        pageFrom,
        pageTo,
        pageLastPage,
        pagePerPage,
        pagePrevPageUrl,
        pageNextPageUrl,
        pageStartNo,
        pageLinks: JSON.stringify(pageLinks)
    };
}