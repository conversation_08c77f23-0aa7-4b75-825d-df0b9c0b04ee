import { writable } from 'svelte/store';

/**
 * 메뉴 상태를 저장
 */
interface ParentMenu {
	dashboard: boolean,
	receiving: boolean,
	pallet: boolean,
	carryout: boolean,
	setting: boolean,
}

export const menuStatus = writable<ParentMenu>({
	dashboard: true,
	receiving: false,
	pallet: false,
	carryout: false,
	setting: false
});

export const menuToggle = (menu: keyof ParentMenu) => {
	menuStatus.update((item: ParentMenu) => {
		return {
			...item,
			[menu]: !item[menu]
		};
	});
};

// 메뉴 하위항목
export interface MenuItem {
	id: string;
	name: string;
	url: string;
	icon: string | null;
}

// 메뉴 상위 항목
export interface Menu {
	id: string;
	name: string;
	icon: string | null;
	items: MenuItem[];
}

export const MENUS: Menu[] = [
	{
		id: 'dashboard',
		name: '대시보드',
		icon: 'faHouse',
		items: [
			{
				id: 'dashboard_search',
				name: '검색',
				url: '/(app)/works/search',
				icon: null,
			},
			{
				id: 'dashboard_notice',
				name: '공지사항',
				url: '/(app)/board/notice',
				icon: null,
			},
			{
				id: 'dashboard_faq',
				name: 'FAQ',
				url: '/(app)/board/faq',
				icon: null,
			}
		]
	},
	{
		id: 'receiving',
		name: '입고',
		icon: 'faShop',
		items: [
			{
				id: 'receiving_work',
				name: '입고 목록',
				url: '/(app)/works',
				icon: null
			},
			{
				id: 'receiving_inspection',
				name: '입고 검수',
				url: '/(app)/works/inspects',
				icon: null
			},
			{
				id: 'receiving_inspection_loading',
				name: '입고 상품 적재',
				url: '/(app)/works/inspections/loading',
				icon: null
			},
			{
				id: 'receiving_inspection_pallets',
				name: '입고 팔레트 목록',
				url: '/(app)/works/inspections/pallets',
				icon: null
			}
		]
	},
	{
		id: 'repair',
		name: '점검 및 수리',
		icon: 'faScrewdriverWrench',
		items: [
			{
				id: 'repair_repairs',
				name: '점검 및 수리',
				url: '/(app)/works/repairs',
				icon: null,
			},
			{
				id: 'repair_tasks',
				name: '작업 목록',
				url: '/(app)/works/tasks',
				icon: null
			},
			{
				id: 'repair_rgs',
				name: 'RG 작업목록',
				url: '/(app)/works/rgs',
				icon: null
			},
			{
				id: 'repair_deleted',
				name: '삭제된 상품 목록',
				url: '/(app)/works/deleted',
				icon: null
			},
			{
				id: 'repair_duplicates',
				name: '중복 상품 목록',
				url: '/(app)/works/duplicates',
				icon: null
			},
			{
				id: 'repair_unlinked',
				name: '미등록 상품 목록',
				url: '/(app)/works/unlinked',
				icon: null
			}
		]
	},
	{
		id: 'shipping',
		name: '출고',
		icon: 'faTruckArrowRight',
		items: [
			{
				id: 'shipping_create',
				name: '출고 상품 적재',
				url: '/(app)/pallets/create',
				icon: null
			},
			{
				id: 'shipping_list',
				name: '출고 팔레트 목록',
				url: '/(app)/pallets/list',
				icon: null
			},
			{
				id: 'receiving_undelivered',
				name: '미입고 상품 목록',
				url: '/(app)/works/undelivered',
				icon: null
			},
		]
	},
	{
		id: 'outsourcing',
		name: '반출/반입',
		icon: 'faHouseCircleCheck',
		items: [
			{
				id: 'outsourcing_out',
				name: '외주 반출 목록',
				url: '/(app)/carryout',
				icon: null
			},
			{
				id: 'outsourcing_in',
				name: '반출 상품 반입',
				url: '/(app)/carryout/products',
				icon: null
			},
			{
				id: 'outsourcing_guest',
				name: '외주 반출 수리',
				url: '/(app)/carryout/repairs',
				icon: null
			}
		]
	},
	{
		id: 'management',
		name: '관리',
		icon: 'faLock',
		items: [
			{
				id: 'management_location',
				name: '출고 팔레트 관리',
				url: '/(app)/management/locations',
				icon: null
			},
			{
				id: 'management_pallet',
				name: '입고 팔레트 관리',
				url: '/(app)/management/pallets',
				icon: null
			},
			{
				id: 'management_qaid_reprint',
				name: 'QAID 재발행 리스트',
				url: '/(app)/management/qaid/reprint',
				icon: null
			},
			{
				id: 'management_attendance',
				name: '근태 관리',
				url: '/(app)/management/attendance',
				icon: null
			}
		]
	},
	{
		id: 'setting',
		name: '설정',
		icon: 'faGear',
		items: [
			{
				id: 'setting_member',
				name: '직원 정보',
				url: '/(app)/settings/members',
				icon: null
			},
			{
				id: 'setting_types',
				name: '작업 분류 설정',
				url: '/(app)/settings/statuses',
				icon: null
			},
			{
				id: 'setting_repair_policy',
				name: '수리 정책 통합 관리',
				url: '/(app)/settings/repairs/policy',
				icon: null
			},
			{
				id: 'setting_process',
				name: '점검 코드 설정',
				url: '/(app)/settings/processes',
				icon: null
			},
			{
				id: 'setting_repair_fee',
				name: '수리비 설정',
				url: '/(app)/settings/repairs/fees',
				icon: null
			},
			{
				id: 'setting_repair_parts_categories',
				name: '구성품 카테고리 설정',
				url: '/(app)/settings/repairs/parts-categories',
				icon: null
			},
			{
				id: 'setting_repair_part',
				name: '구성품 설정',
				url: '/(app)/settings/repairs/parts',
				icon: null
			},
			{
				id: 'setting_label_printer',
				name: '라벨 프린터 설정',
				url: '/(app)/settings/printer',
				icon: null
			}
		]
	}
];