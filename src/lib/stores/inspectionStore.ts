import { get, readable, writable } from 'svelte/store';
import { authClient } from '$lib/AxiosBackend';

import type { User } from '$lib/User';
import { processAPIData } from '$lib/pagenation';
import { executeMessage, handleCatch } from '$lib/Functions';

export const inspectionStore = writable({});

export async function loadInspection(endpoint: string, user: User) {
	const payload = {
		userId: user.id
	};

	const { status, data } = await authClient.get(endpoint, {
		params: payload
	});

	try {
		if (status === 200 && data) {
			if (!data.data.items.data) {
				await executeMessage("데이터 전송중 에러가 발생했습니다.\n잠시 후 F5 키를 눌러 다시 시도해 주세요.", 'error');
				return;
			}

			const pageData = await processAPIData(data.data.items);
			inspectionStore.set(pageData);

			/**
			 * items 속성
			 * [{
			 *     id: 0,
			 *     req_at: "",
			 *     req_type: 0,
			 *     status: "",
			 *     user: {} ?? null,
			 *     user_memo: {} ?? null,
			 *     checked_at: "",
			 *     checked_user: {} ?? null,
			 *     checked_user_memo: {} ?? null,
			 *     count_total: 0,
			 *     count_unchecked: 0,
			 *     count_checked: 0,
			 *     count_outsourcing: 0,
			 *     count_checkout: 0,
			 *     count_shipped: 0,
			 *     count_duplicated: 0,
			 *     count_unlinked: 0,
			 *     count_completed: 0,
			 *     count_deleted: 0,
			 *     total: 0,
			 *     created_at: "",
			 *     updated_at: "",
			 *     deleted_at: "",
			 * }]
			 */
			const items = await data.data.items.data;
			inspectionStore.update(currentData => ({
				...currentData,
				items
			}));
		} else {
			await executeMessage('입고 목록 정보를 받아올 수 없습니다.', 'error');
		}
	} catch (error) {
		await handleCatch(error);
	}
}

/**
 * 입고요청 목록 등록 상태
 */
export const REQ_STATUS_REGISTERED = 10;
export const REQ_STATUS_CHECKED = 30;
export const REQ_STATUS_COMPLETED = 50;
export const REQ_STATUS_DELETED = 90;

export const inspectionStatuses = readable([
	{ value: REQ_STATUS_REGISTERED, text: '등록' },
	{ value: REQ_STATUS_CHECKED, text: '점검중' }, // 검수통과, 점검중
	{ value: REQ_STATUS_COMPLETED, text: '완료' }, // 검수통과, 점검중
	{ value: REQ_STATUS_DELETED, text: '삭제' }
]);

export function getInspectionStatusName(value: number) {
	const statuses = get(inspectionStatuses);
	const status = statuses.find(status => status.value === value);

	return status ? status.text : 'Unknown';
}

export function inspectionStatusColor(value: number) {
	const statusStyles: Record<number, { bg: string; text: string }> = {
		[REQ_STATUS_REGISTERED]: { bg: 'bg-blue-100', text: 'text-blue-900' },
		[REQ_STATUS_CHECKED]: { bg: 'bg-orange-100', text: 'text-orange-900' },
		[REQ_STATUS_COMPLETED]: { bg: 'bg-green-100', text: 'text-green-900' },
		[REQ_STATUS_DELETED]: { bg: 'bg-red-100', text: 'text-red-900' },
	};

	const defaultStyle = { bg: 'bg-gray-100', text: 'text-gray-900' };
	const styles = statusStyles[value] || defaultStyle;
	const name = getInspectionStatusName(value);

	return `<span class="${styles.text} ${styles.bg} px-3 py-1 font-bold rounded shadow min-w-[70px] inline-block text-center">${name}</span>`;
}
