import { writable } from 'svelte/store';
import { authClient } from '$lib/AxiosBackend';

import type { User } from '$lib/User';
import { processAPIData } from '$lib/pagenation';
import { executeMessage, handleCatch } from '$lib/Functions';

export const boardStore = writable({});

export async function loadBoard(endpoint: string, user: User) {
	const payload = {
		userId: user.id
	};

	const { status, data } = await authClient.get(endpoint, {
		params: payload
	});

	try {
		if (status === 200 && data) {
			if (!data.data.articles.data) {
				await executeMessage("데이터 전송중 에러가 발생했습니다.\n잠시 후 F5 키를 눌러 다시 시도해 주세요.", 'error');
				return;
			}

			const pageData = await processAPIData(data.data.articles);
			boardStore.set(pageData);

			const notices = await data.data.notices;
			const articles = await data.data.articles.data;
			boardStore.update(currentData => ({
				...currentData,
				articles,
				notices
			}));
		} else {
			await executeMessage('게시글 정보를 받아올 수 없습니다.', 'error');
		}
	} catch (error) {
		await handleCatch(error);
	}
}
