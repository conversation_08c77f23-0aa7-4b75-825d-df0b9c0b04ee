import { writable } from 'svelte/store';
import { authClient } from '$lib/AxiosBackend';

export const loadedStore = writable({});

export async function loadItems(endpoint: string, user: User) {
	const payload = {
		userId: user.id
	};

	try {
		loadedStore.set({}); // 먼저 초기화

		const { status, data } = await authClient.get(endpoint, {
			params: payload
		});

		if (status === 200 && data) {
			const items = await data.data;
			loadedStore.set(items); // 새 데이터로 업데이트
		}
	} catch (error) {
		console.error('Failed to load items:', error);
		loadedStore.set({}); // 에러 발생시 빈 객체로 초기화
	}
}