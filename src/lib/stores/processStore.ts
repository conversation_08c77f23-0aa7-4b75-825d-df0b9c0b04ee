import type { User } from '$lib/User';

import { get, readable, writable } from 'svelte/store';

import { authClient } from '$lib/AxiosBackend';
import { executeMessage } from '$lib/Functions';

export const processStore = writable([]);

export async function loadProcesses(endpoint: string, user: User) {
	const payload = {
		userId: user.id
	};

	const { status, data } = await authClient.get(endpoint, {
		params: payload
	});

	if (status === 200 && data) {
		const processData = await data.data.processes;

		processStore.set(processData);
	} else {
		await executeMessage('점검코드 정보를 받아올 수 없습니다.', 'error');
	}
}

/**
 * 점검 검수코드
 */
export const PROCESS_TYPE_CHECK = 'check';
export const PROCESS_TYPE_REPAIR = 'repair';
export const PROCESS_TYPE_GRADE = 'grade';
export const PROCESS_TYPE_FIX = 'fix';
export const PROCESS_TYPE_CHARGE = 'charge';

export const processTypes = readable([
	{ value: PROCESS_TYPE_CHECK, text: '증상내용' },
	{ value: PROCESS_TYPE_REPAIR, text: '처리내용' },
	{ value: PROCESS_TYPE_GRADE, text: '수리상태' },
	{ value: PROCESS_TYPE_FIX, text: '별도 수리비' },
	{ value: PROCESS_TYPE_CHARGE, text: '추가비용' }
]);

export function getProcessTypeName(value: string) {
	const types = get(processTypes);
	const type = types.find((type) => type.value === value);
	return type ? type.text : 'Unknown';
}

/**
 * 점검 등급
 */
export const PROCESS_GRADE_BEST = 'ST_BEST';
export const PROCESS_GRADE_GOOD = 'ST_GOOD';
export const PROCESS_GRADE_NORMAL = 'ST_NORMAL';
export const PROCESS_GRADE_REFURB = 'ST_REFURB';
export const PROCESS_GRADE_XL = 'ST_XL';

export const processGrade = readable([
	{ value: PROCESS_GRADE_BEST, text: 'CB' },
	{ value: PROCESS_GRADE_GOOD, text: 'CG' },
	{ value: PROCESS_GRADE_NORMAL, text: 'CN' },
	{ value: PROCESS_GRADE_REFURB, text: 'RP' },
	{ value: PROCESS_GRADE_XL, text: 'XL' }
]);

export function getProcessGradeName(value: string) {
	const types = get(processGrade);
	const type = types.find((type) => type.value === value);

	return type ? type.text : 'Unknown';
}

/**
 * 점검(수리)상태 색상 표시 버튼
 *
 * @param process
 */
export function getProcessGradeColorButton(process: any) {
	let color;
	let name;

	if (process.code === 'ST_BEST' || process.name === 'CB' || process === 'CB') {
		color = '#29b8ff';
		name = 'CB';
	} else if (process.code === 'ST_GOOD' || process.name === 'CG' || process === 'CG') {
		color = '#29b8ff';
		name = 'CG';
	} else if (process.code === 'ST_NORMAL' || process.name === 'CN' || process === 'CN') {
		color = '#29b8ff';
		name = 'CN';
	} else if (process.code === 'ST_REFURB' || process.name === 'RP' || process === 'RP') {
		color = '#f8d903';
		name = 'RP';
	} else if (
		(typeof process.code === 'string' && process.code.startsWith('ST_XL')) ||
		(typeof process.name === 'string' && process.name.startsWith('XL')) ||
		(typeof process === 'string' && process.startsWith('XL'))
	) {
		color = '#ff6666';
		name = process.name || process;
	}

	return `<button class="btn btn-ghost btn-xs text-neutral" style='background-color: ${color};'>${name}</button>`;
}
