import type { User } from '$lib/User';

import { writable } from 'svelte/store';

import { authClient } from '$lib/AxiosBackend';
import { executeMessage } from '$lib/Functions';

export const statusStore = writable([]);

export async function loadStatuses(endpoint: string, user: User) {
	const payload = {
		userId: user.id
	};

	const { status, data } = await authClient.get(endpoint, {
		params: payload
	});

	if (status === 200 && data) {
		const statusData = await data.data.items;

		statusStore.set(statusData);
	} else {
		await executeMessage('작업 분류 및 세부 설정정보를 받아올 수 없습니다.', 'error');
	}
}