import type { User } from '$lib/User';
import { processAPIData } from '$lib/pagenation';

import { get, readable, writable } from 'svelte/store';
import { authClient } from '$lib/AxiosBackend';
import { executeMessage, handleCatch } from '$lib/Functions';

export const memberStore = writable({});

export async function loadMembers(endpoint: string, user: User) {
	const payload = {
		userId: user.id
	};

	const { status, data } = await authClient.get(endpoint, {
		params: payload
	});

	try {
		if (status === 200 && data) {
			if (!data.data.members.data) {
				await executeMessage("데이터 전송중 에러가 발생했습니다.\n잠시 후 F5 키를 눌러 다시 시도해 주세요.", 'error');
				return;
			}

			const pageData = await processAPIData(data.data.members);
			memberStore.set(pageData);

			/**
			 * items 속성
			 * [{
			 *     id: 0,
			 *     company_id: 0,
			 *     role: "Employee",
			 *     username: "",
			 *     name: "",
			 *     email: "",
			 *     cellphone: "",
			 *     status: 0,
			 *     login_at: "",
			 *     login_ip: "",
			 *     login_os: "",
			 *     created_at: "",
			 *     updated_at: "",
			 *     deleted_at: "",
			 * }]
			 */
			const members = await data.data.members.data;
			memberStore.update(currentData => ({
				...currentData,
				members
			}));
		} else {
			await executeMessage('직원 정보를 받아올 수 없습니다.', 'error');
		}
	} catch (error) {
		await handleCatch(error);
	}
}

/**
 * 직원 권한(형태)
 */
export const ROLE_SUPER_ADMIN = 'Super-Admin';
export const ROLE_ADMIN = 'Admin';
export const ROLE_RECEIVING_MANAGER = 'Receiving-Manager';
export const ROLE_PALLET_MANAGER = 'Pallet-Manager';
export const ROLE_CARRYOUT_MANAGER = 'Carryout-Manager';
export const ROLE_EMPLOYEE = 'Employee';
export const ROLE_GUEST = 'Guest';

export const memberRoles = readable([
	{ value: ROLE_GUEST, text: '게스트' },
	{ value: ROLE_EMPLOYEE, text: '일반 작업자' },
	{ value: ROLE_CARRYOUT_MANAGER, text: '외주 담당' },
	{ value: ROLE_PALLET_MANAGER, text: '출고 담당' },
	{ value: ROLE_RECEIVING_MANAGER, text: '입고 담당' },
	{ value: ROLE_ADMIN, text: '관리자' },
	{ value: ROLE_SUPER_ADMIN, text: '시스템 관리자' }
]);

export function getRoleName(value: string) {
	const roles = get(memberRoles);
	const role = roles.find(role => role.value === value);

	return role ? role.text : 'Unknown';
}

/**
 * 직원 등록 상태
 */
export const MEMBER_STATUS_ACTIVE = 1;
export const MEMBER_STATUS_PAUSE = 2;
export const MEMBER_STATUS_UNAVAILABLE = 3;
export const MEMBER_STATUS_DELETED = 9;

export const memberStatuses = readable([
	{ value: MEMBER_STATUS_ACTIVE, text: '활성' },
	{ value: MEMBER_STATUS_PAUSE, text: '일시정지' },
	{ value: MEMBER_STATUS_UNAVAILABLE, text: '이용불가' },
	{ value: MEMBER_STATUS_DELETED, text: '삭제' }
]);

export function getMemberStatusName(value: number) {
	const statuses = get(memberStatuses);
	const status = statuses.find(status => status.value === value);

	return status ? status.text : 'Unknown';
}