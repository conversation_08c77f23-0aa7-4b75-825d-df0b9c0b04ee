import { writable } from 'svelte/store';

// localStorage에서 초기값을 가져옵니다. 없으면 false를 사용합니다.
const storedValue = localStorage.getItem('isBarcodeScannerVisible');
const initialValue = storedValue ? JSON.parse(storedValue) : false;

// writable store를 생성합니다.
const isBarcodeScannerVisible = writable(initialValue);

// subscribe 메서드를 오버라이드하여 값이 변경될 때마다 localStorage를 업데이트합니다.
const subscribe = isBarcodeScannerVisible.subscribe;
isBarcodeScannerVisible.subscribe = (run, invalidate) => {
	const unsubscribe = subscribe(value => {
		localStorage.setItem('isBarcodeScannerVisible', JSON.stringify(value));
		run(value);
	}, invalidate);
	return unsubscribe;
};

export { isBarcodeScannerVisible };
