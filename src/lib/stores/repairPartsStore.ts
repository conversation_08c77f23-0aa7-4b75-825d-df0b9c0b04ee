import { writable } from 'svelte/store';
import { authClient } from '$lib/AxiosBackend';

import type { User } from '$lib/User';
import { processAPIData } from '$lib/pagenation';
import { executeMessage, handleCatch } from '$lib/Functions';

export const repairPartsStore = writable({});

export async function loadRepairParts(endpoint: string, user: User) {
	const payload = {
		userId: user.id
	};

	const { status, data } = await authClient.get(endpoint, {
		params: payload
	});

	try {
		if (status === 200 && data.success) {
			if (!data.data.items.data) {
				await executeMessage("데이터 전송중 에러가 발생했습니다.\n잠시 후 F5 키를 눌러 다시 시도해 주세요.", 'error');
				return;
			}

			const pageData = await processAPIData(data.data.items);
			repairPartsStore.set(pageData);

			const items = await data.data.items.data;
			repairPartsStore.update(currentData => ({
				...currentData,
				items
			}));
		} else {
			await executeMessage(data.data.message, 'error');
		}
	} catch (error) {
		await handleCatch(error);
	}
}
