import { get, readable, writable } from 'svelte/store';
import { authClient } from '$lib/AxiosBackend';

import type { User } from '$lib/User';
import { processAPIData } from '$lib/pagenation';
import { executeMessage, handleCatch } from '$lib/Functions';

export const carryoutProductStore = writable({});

export async function loadItems(endpoint: string, user: User) {
	const payload = {
		userId: user.id
	};

	const { status, data } = await authClient.get(endpoint, {
		params: payload
	});

	try {
		if (status === 200 && data) {
			if (!data.data.items.data) {
				await executeMessage("데이터 전송중 에러가 발생했습니다.\n잠시 후 F5 키를 눌러 다시 시도해 주세요.", 'error');
				return;
			}

			const pageData = await processAPIData(data.data.items);
			carryoutProductStore.set(pageData);

			const carryout = await data.data.carryout;
			const items = await data.data.items.data;
			const count = {
				carryout: 0,
				carryin: 0
			};
			items.forEach((item: any) => {
				if (!item.carryin_at) {
					count.carryout += 1;
				} else {
					count.carryin += 1;
				}
			});

			carryoutProductStore.update(currentData => ({
				...currentData,
				carryout,
				items,
				count
			}));
		} else {
			await executeMessage('외주 반출 상품목록을 받아올 수 없습니다.', 'error');
		}
	} catch (error) {
		await handleCatch(error);
	}
}

/**
 * 상품의 상태
 */
export const CARRYOUT_PRODUCT_STATUS_ONBOARD = 10;
export const CARRYOUT_PRODUCT_STATUS_RENOVATED = 20;
export const CARRYOUT_PRODUCT_STATUS_CANCELED = 90;

export const carryoutProductStatuses = readable([
	{ value: CARRYOUT_PRODUCT_STATUS_ONBOARD, text: '수리대기' },
	{ value: CARRYOUT_PRODUCT_STATUS_RENOVATED, text: '수리완료' },
	{ value: CARRYOUT_PRODUCT_STATUS_CANCELED, text: '취소' }
]);

export function getCarryoutProductStatusName(value: number) {
	const items = get(carryoutProductStatuses);
	const status = items.find(status => status.value === value);

	return status ? status.text : 'Unknown';
}
