import { get, readable, writable } from 'svelte/store';
import { authClient } from '$lib/AxiosBackend';

import type { User } from '$lib/User';
import { processAPIData } from '$lib/pagenation';
import { executeMessage, handleCatch } from '$lib/Functions';

export const productStore = writable({});

export async function loadProduct(endpoint: string, user: User) {
	const payload = {
		userId: user.id
	};

	const { status, data } = await authClient.get(endpoint, {
		params: payload
	});

	try {
		if (status === 200 && data.success) {
			if (!data.data.products.data) {
				await executeMessage("데이터 전송중 에러가 발생했습니다.\n잠시 후 F5 키를 눌러 다시 시도해 주세요.", 'error');
				return;
			}

			const pageData = await processAPIData(data.data.products);
			productStore.set(pageData);

			const requestAt = await data.data.requestAt;
			const inspects = await data.data.inspects;
			const cate4 = await data.data.cate4;
			const products = await data.data.products.data;
			productStore.update(currentData => ({
				...currentData,
				requestAt,
				inspects,
				cate4,
				products
			}));
		} else {
			await executeMessage(data.data.message, 'error');
		}
	} catch (error) {
		await handleCatch(error);
	}
}

/**
 * 상품의 검수 상태
 */
export const PRODUCT_CHECKED_STATUS_UNCHECKED = 10;
export const PRODUCT_CHECKED_STATUS_CHECKED = 20;
export const PRODUCT_CHECKED_STATUS_UNDELIVERED = 30;

export const productCheckedStatuses = readable([
	{ value: PRODUCT_CHECKED_STATUS_UNCHECKED, text: '검수대기' },
	{ value: PRODUCT_CHECKED_STATUS_CHECKED, text: '검수완료' },
	{ value: PRODUCT_CHECKED_STATUS_UNDELIVERED, text: '미입고' }
]);

export function getProductCheckedStatusName(value: number) {
	const statuses = get(productCheckedStatuses);
	const status = statuses.find(status => status.value === value);

	return status ? status.text : 'Unknown';
}

/**
 * 상품의 상태
 */
export const PRODUCT_STATUS_REGISTERED = 10;
export const PRODUCT_STATUS_WAITING = 19;
export const PRODUCT_STATUS_REPAIRED = 20;
export const PRODUCT_STATUS_CHECKED_ON_PALLET = 30;
export const PRODUCT_STATUS_CARRIED_OUT = 50;
export const PRODUCT_STATUS_EXPORTED = 70;
export const PRODUCT_STATUS_HELD = 80;
export const PRODUCT_STATUS_DELETED = 90;

export const productStatuses = readable([
	{ value: PRODUCT_STATUS_REGISTERED, text: '수리대기중(창고)' },
	{ value: PRODUCT_STATUS_WAITING, text: '구성품 신청중(대기)' },
	{ value: PRODUCT_STATUS_REPAIRED, text: '수리/점검완료(창고)' },
	{ value: PRODUCT_STATUS_CHECKED_ON_PALLET, text: '점검완료(적재중)' },
	{ value: PRODUCT_STATUS_CARRIED_OUT, text: '반출중' },
	{ value: PRODUCT_STATUS_EXPORTED, text: '출고완료' },
	{ value: PRODUCT_STATUS_HELD, text: '출고보류' },
	{ value: PRODUCT_STATUS_DELETED, text: '삭제' }
]);

export function getProductStatusName(value: number) {
	const statuses = get(productStatuses);
	const status = statuses.find(status => status.value === value);

	return status ? status.text : 'Unknown';
}

