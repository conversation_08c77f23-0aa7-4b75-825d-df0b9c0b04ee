import { get, readable, writable } from 'svelte/store';
import { authClient } from '$lib/AxiosBackend';

import type { User } from '$lib/User';
import { processAPIData } from '$lib/pagenation';
import { executeMessage, handleCatch, makeLocationInfo } from '$lib/Functions';

export const locationStore = writable({});

export async function loadItems(endpoint: string, user: User) {
	const payload = {
		userId: user.id
	};

	const { status, data } = await authClient.get(endpoint, {
		params: payload
	});

	try {
		if (status === 200 && data) {
			if (!data.data.items.data) {
				await executeMessage("데이터 전송중 에러가 발생했습니다.\n잠시 후 F5 키를 눌러 다시 시도해 주세요.", 'error');
				return;
			}

			const pageData = await processAPIData(data.data.items);
			locationStore.set(pageData);

			let items = await data.data.items.data;
			items = items.map((item: any) => {
				const loc = makeLocationInfo(item);
				// if (item.location && 'level' in item.location && 'column' in item.location) {
				const pallet_info = {
					'code': loc.pallet_code,
					'store': loc.store,
					'line': loc.line,
					'rack': loc.rack,
					'level': loc.level,
					'column': loc.column,
					'location_name': loc.location_name,
					'country': loc.country,
					'city': loc.city
				};

				return { ...item, pallet_info };
			});
			locationStore.update(currentData => ({
				...currentData,
				items
			}));
		} else {
			await executeMessage('파레트 목록을 받아올 수 없습니다.', 'error');
		}
	} catch (error) {
		await handleCatch(error);
	}
}

export const LOCATION_COUNTRY_KR = 'KR';
export const LOCATION_COUNTRY_US = 'US';

export const locationCountries = readable([
	{ value: LOCATION_COUNTRY_KR, text: '한국' },
	{ value: LOCATION_COUNTRY_US, text: '미국' }
]);

export const LOCATION_CITY_GS = 'GS';
export const LOCATION_CITY_PJ = 'PJ';
export const LOCATION_CITY_LA = 'LA';
export const LOCATION_CITY_ESCS = 'ESCS';

export const locationCities = readable([
	{ value: LOCATION_CITY_GS, text: '고산(주덕)' },
	{ value: LOCATION_CITY_PJ, text: '파주' },
	{ value: LOCATION_CITY_LA, text: 'Los Angeles' },
	{ value: LOCATION_CITY_ESCS, text: '음성나동(코너스톤)' }
]);

export const F_ENABLE_Y = 'Y';
export const F_ENABLE_N = 'N';

export const locationEnable = readable([
	{ value: F_ENABLE_Y, text: '적재가능' },
	{ value: F_ENABLE_N, text: '사용불가' }
]);

export function getLocationEnableName(value: string) {
	const isEnable = get(locationEnable);
	const enable = isEnable.find(enable => enable.value === value);

	return enable ? enable.text : 'Unknown';
}
