export enum InvoiceTypes {
	Checked = 'CHECK',
	Repaired = 'REPAIR',
	ReinstalledOS = 'OS reinstall',
}

export enum GradeBasis {
	Size = 'size',
	Price = 'price',
	None = 'none'
}

export enum GradeBasisUnit {
	Inch = 'inch',
	CM = 'cm',
	Won = 'won'
}

interface Condition {
	cate4_name: string;
	cate5_name?: string | { not?: string; notEndsWith?: string } | string[];
}

interface Category {
	conditions: Condition[];
}

function matchesCategory(category: Category, cate4_name: string, cate5_name: string): boolean {
	return category.conditions.some(condition => {
		const matchesCate4 = condition.cate4_name === cate4_name;

		if (condition.cate5_name) {
			if (typeof condition.cate5_name === 'object') {
				if ('not' in condition.cate5_name) {
					return matchesCate4 && cate5_name !== condition.cate5_name.not;
				}
				if ('notEndsWith' in condition.cate5_name) {
					return matchesCate4 && !cate5_name.endsWith(<string>condition.cate5_name.notEndsWith);
				}
				if (Array.isArray(condition.cate5_name)) {
					return matchesCate4 && condition.cate5_name.includes(cate5_name);
				}
			} else {
				return matchesCate4 && condition.cate5_name === cate5_name;
			}
		} else {
			return matchesCate4;
		}
	});
}

const CATEGORY_RULES = {
	TV_MONITOR: {
		conditions: [
			{ cate4_name: 'TV', cate5_name: 'TV' },
			{ cate4_name: '컴퓨터주변기기', cate5_name: '모니터' }
		],
		handler: (invoice_type: string, productName: string) => {
			const isOtherBrand = productName.indexOf('삼성전자') === -1 && productName.indexOf('엘지전자') === -1 && productName.indexOf('LG전자') === -1;

			const gradeBasisData = {
				[InvoiceTypes.Checked]: {
					other: [
						[GradeBasis.Size, 0, 60.96, GradeBasisUnit.CM, 16000],
						[GradeBasis.Size, 60.96, 81.28, GradeBasisUnit.CM, 19000],
						[GradeBasis.Size, 81.28, 109.22, GradeBasisUnit.CM, 22000],
						[GradeBasis.Size, 109.22, 124.46, GradeBasisUnit.CM, 25000],
						[GradeBasis.Size, 124.46, 139.7, GradeBasisUnit.CM, 32000],
						[GradeBasis.Size, 139.7, 200, GradeBasisUnit.CM, 39000],
						[GradeBasis.Size, 0, 24, GradeBasisUnit.Inch, 16000],
						[GradeBasis.Size, 24, 32, GradeBasisUnit.Inch, 19000],
						[GradeBasis.Size, 34, 43, GradeBasisUnit.Inch, 22000],
						[GradeBasis.Size, 43, 49, GradeBasisUnit.Inch, 25000],
						[GradeBasis.Size, 49, 55, GradeBasisUnit.Inch, 32000],
						[GradeBasis.Size, 55, 100, GradeBasisUnit.Inch, 39000]
					],
					samsung_lg: [
						[GradeBasis.Size, 0, 60.96, GradeBasisUnit.CM, 16000],
						[GradeBasis.Size, 60.96, 81.28, GradeBasisUnit.CM, 19000],
						[GradeBasis.Size, 81.28, 109.22, GradeBasisUnit.CM, 22000],
						[GradeBasis.Size, 109.22, 124.46, GradeBasisUnit.CM, 25000],
						[GradeBasis.Size, 124.46, 139.7, GradeBasisUnit.CM, 32000],
						[GradeBasis.Size, 139.7, 200, GradeBasisUnit.CM, 39000],
						[GradeBasis.Size, 0, 24, GradeBasisUnit.Inch, 16000],
						[GradeBasis.Size, 24, 32, GradeBasisUnit.Inch, 19000],
						[GradeBasis.Size, 32, 43, GradeBasisUnit.Inch, 22000],
						[GradeBasis.Size, 43, 49, GradeBasisUnit.Inch, 25000],
						[GradeBasis.Size, 49, 55, GradeBasisUnit.Inch, 32000],
						[GradeBasis.Size, 55, 100, GradeBasisUnit.Inch, 39000]
					]
				},
				[InvoiceTypes.Repaired]: {
					other: [
						[GradeBasis.Size, 0, 60.96, GradeBasisUnit.CM, 26000],
						[GradeBasis.Size, 60.96, 81.28, GradeBasisUnit.CM, 36000],
						[GradeBasis.Size, 81.28, 109.22, GradeBasisUnit.CM, 44000],
						[GradeBasis.Size, 109.22, 124.46, GradeBasisUnit.CM, 61000],
						[GradeBasis.Size, 124.46, 139.7, GradeBasisUnit.CM, 86000],
						[GradeBasis.Size, 139.7, 200, GradeBasisUnit.CM, 102000],
						[GradeBasis.Size, 0, 24, GradeBasisUnit.Inch, 26000],
						[GradeBasis.Size, 24, 32, GradeBasisUnit.Inch, 36000],
						[GradeBasis.Size, 34, 43, GradeBasisUnit.Inch, 44000],
						[GradeBasis.Size, 43, 49, GradeBasisUnit.Inch, 61000],
						[GradeBasis.Size, 49, 55, GradeBasisUnit.Inch, 86000],
						[GradeBasis.Size, 55, 100, GradeBasisUnit.Inch, 102000]
					],
					samsung_lg: [
						[GradeBasis.Size, 0, 60.96, GradeBasisUnit.CM, 27000],
						[GradeBasis.Size, 60.96, 81.28, GradeBasisUnit.CM, 41000],
						[GradeBasis.Size, 81.28, 109.22, GradeBasisUnit.CM, 51000],
						[GradeBasis.Size, 109.22, 124.46, GradeBasisUnit.CM, 71000],
						[GradeBasis.Size, 124.46, 139.7, GradeBasisUnit.CM, 95000],
						[GradeBasis.Size, 139.7, 200, GradeBasisUnit.CM, 110000],
						[GradeBasis.Size, 0, 24, GradeBasisUnit.Inch, 27000],
						[GradeBasis.Size, 24, 32, GradeBasisUnit.Inch, 41000],
						[GradeBasis.Size, 32, 43, GradeBasisUnit.Inch, 51000],
						[GradeBasis.Size, 43, 49, GradeBasisUnit.Inch, 71000],
						[GradeBasis.Size, 49, 55, GradeBasisUnit.Inch, 95000],
						[GradeBasis.Size, 55, 100, GradeBasisUnit.Inch, 110000]
					]
				}
			};

			return gradeBasisData[invoice_type][isOtherBrand ? 'other' : 'samsung_lg'];
		}
	},
	REFRIGERATOR_WASHER: {
		conditions: [
			{ cate4_name: '냉장고', cate5_name: { not: '미니/화장품냉장고' } },
			{ cate4_name: '세탁기' }
		],
		handler: (invoice_type: string, productName: string) => {
			if (invoice_type === InvoiceTypes.Checked) {
				return [[GradeBasis.None, 0, 0, GradeBasisUnit.Won, 20000]];
			} else if (invoice_type === InvoiceTypes.Repaired) {
				return [[GradeBasis.None, 0, 0, GradeBasisUnit.Won, 35000]];
			}
		}
	},
	HEATING_COOLING_KITCHEN: {
		conditions: [
			{ cate4_name: '냉방가전' },
			{ cate4_name: '환경가전' },
			{ cate4_name: '위생/미용용품' },
			{ cate4_name: '냉난방가전' },
			{ cate4_name: '가사가전' },
			{ cate4_name: '주방가전' },
			{ cate4_name: '조리보조도구' }
		],
		handler: (invoice_type: string, productname: string) => {
			if (invoice_type === InvoiceTypes.Checked) {
				return [
					[GradeBasis.Price, 0, 50000, GradeBasisUnit.Won, 5800],
					[GradeBasis.Price, 50000, 100000, GradeBasisUnit.Won, 13000],
					[GradeBasis.Price, 100000, 200000, GradeBasisUnit.Won, 19000],
					[GradeBasis.Price, 200000, 100000000, GradeBasisUnit.Won, 25000]
				];
			} else if (invoice_type === InvoiceTypes.Repaired) {
				return [
					[GradeBasis.Price, 0, 50000, GradeBasisUnit.Won, 8200],
					[GradeBasis.Price, 50000, 100000, GradeBasisUnit.Won, 20000],
					[GradeBasis.Price, 100000, 200000, GradeBasisUnit.Won, 28000],
					[GradeBasis.Price, 200000, 100000000, GradeBasisUnit.Won, 36000]
				];
			}
		}
	},
	NOTEBOOK: {
		conditions: [
			{ cate4_name: '컴퓨터', cate5_name: '노트북' }
		],
		handler: (invoice_type: string, productname: string) => {
			if (invoice_type == InvoiceTypes.Checked) {
				return [[GradeBasis.None, 0, 0, GradeBasisUnit.Won, 24000]];
			} else if (invoice_type == InvoiceTypes.Repaired) {
				return [[GradeBasis.None, 0, 0, GradeBasisUnit.Won, 42000]];
			} else if (invoice_type == InvoiceTypes.ReinstalledOS) {
				return [[GradeBasis.None, 0, 0, GradeBasisUnit.Won, 40000]];
			}
		}
	},
	DESKTOP_PC: {
		conditions: [
			{
				cate4_name: '컴퓨터',
				cate5_name: ['조립PC', '브랜드PC', '일체형PC']
			}
		],
		handler: (invoice_type: string, productname: string) => {
			if (invoice_type === InvoiceTypes.Checked) {
				return [[GradeBasis.None, 0, 0, GradeBasisUnit.Won, 34000]];
			} else if (invoice_type === InvoiceTypes.Repaired) {
				return [[GradeBasis.None, 0, 0, GradeBasisUnit.Won, 54000]];
			} else if (invoice_type === InvoiceTypes.ReinstalledOS) {
				return [[GradeBasis.None, 0, 0, GradeBasisUnit.Won, 40000]];
			}
		}
	},
	MINI_PC_TABLET: {
		conditions: [
			{ cate4_name: '컴퓨터', cate5_name: '미니PC' },
			{ cate5_name: '태블릿PC' }
		],
		handler: (invoice_type: string, productname: string) => {
			if (invoice_type == InvoiceTypes.Checked) {
				return [
					[GradeBasis.Price, 0, 100000, GradeBasisUnit.Won, 15000],
					[GradeBasis.Price, 100000, 100000000, GradeBasisUnit.Won, 20000]
				];
			} else if (invoice_type == InvoiceTypes.Repaired) {
				return [
					[GradeBasis.Price, 0, 100000, GradeBasisUnit.Won, 22000],
					[GradeBasis.Price, 100000, 100000000, GradeBasisUnit.Won, 33000]
				];
			} else if (invoice_type == InvoiceTypes.ReinstalledOS) {
				return [
					[GradeBasis.Price, 0, 100000, GradeBasisUnit.Won, 25000],
					[GradeBasis.Price, 100000, 100000000, GradeBasisUnit.Won, 25000]
				];
			}
		}
	},
	COMPUTER_SUPPLIES_GRAPHIC_CARD: {
		conditions: [
			{ cate4_name: '컴퓨터용품', cate5_name: '그래픽카드' }
		],
		handler: (invoice_type: string, productname: string) => {
			if (invoice_type == InvoiceTypes.Checked) {
				return [[GradeBasis.None, 0, 0, GradeBasisUnit.Won, 22000]];
			} else if (invoice_type == InvoiceTypes.Repaired) {
				return [[GradeBasis.None, 0, 0, GradeBasisUnit.Won, 35000]];
			}
		}
	},
	MASSAGE_BEAUTY: {
		conditions: [
			{ cate4_name: '마사지/안마기기' },
			{ cate4_name: '이미용가전' },
			{
				cate4_name: '냉장고',
				cate5_name: '미니/화장품냉장고'
			}
		],
		handler: (invoice_type: string, productname: string) => {
			if (invoice_type === InvoiceTypes.Checked) {
				return [
					[GradeBasis.Price, 0, 50000, GradeBasisUnit.Won, 5800],
					[GradeBasis.Price, 50000, 100000, GradeBasisUnit.Won, 8000],
					[GradeBasis.Price, 100000, 200000, GradeBasisUnit.Won, 16000],
					[GradeBasis.Price, 200000, 100000000, GradeBasisUnit.Won, 23000]
				];
			} else if (invoice_type === InvoiceTypes.Repaired) {
				return [
					[GradeBasis.Price, 0, 50000, GradeBasisUnit.Won, 8200],
					[GradeBasis.Price, 50000, 100000, GradeBasisUnit.Won, 14000],
					[GradeBasis.Price, 100000, 200000, GradeBasisUnit.Won, 23000],
					[GradeBasis.Price, 200000, 100000000, GradeBasisUnit.Won, 35000]
				];
			}
		}
	},
	ELECTRIC_WHEEL_BOARD: {
		conditions: [
			{ cate4_name: '전동휠/보드' }
		],
		handler: (invoice_type: string, productname: string) => {
			if (invoice_type === InvoiceTypes.Checked) {
				return [
					[GradeBasis.Price, 0, 50000, GradeBasisUnit.Won, 5800],
					[GradeBasis.Price, 50000, 100000, GradeBasisUnit.Won, 20000],
					[GradeBasis.Price, 100000, 200000, GradeBasisUnit.Won, 27000],
					[GradeBasis.Price, 200000, 100000000, GradeBasisUnit.Won, 35000]
				];
			} else if (invoice_type === InvoiceTypes.Repaired) {
				return [
					[GradeBasis.Price, 0, 50000, GradeBasisUnit.Won, 8200],
					[GradeBasis.Price, 50000, 100000, GradeBasisUnit.Won, 31000],
					[GradeBasis.Price, 100000, 200000, GradeBasisUnit.Won, 39000],
					[GradeBasis.Price, 200000, 100000000, GradeBasisUnit.Won, 47000]
				];
			}
		}
	},
	AUDIO_DEVICES: {
		conditions: [
			{ cate4_name: '헤드폰/헤드셋' },
			{ cate4_name: '스피커/엠프' },
			{ cate4_name: '라디오/오디오' },
			{ cate4_name: '기타음향기기' },
			{ cate4_name: '전자악기' },
			{ cate4_name: '건반악기' },
			{ cate4_name: '게임' },
			{ cate4_name: 'RC완구' },
			{ cate4_name: '골프용품' },
			{ cate4_name: 'VTR/DVD플레이어' },
			{ cate4_name: '빔/스크린' },
			{ cate4_name: '기타통신기기' },
			{ cate4_name: '차량가전용품' },
			{ cate4_name: '차량용 공기청정기' },
			{ cate4_name: '차량용 가습기' },
			{ cate4_name: '차량용음향기기' },
			{ cate4_name: '건강측정기기' },
			{ cate4_name: '전자학습기기' },
			{ cate4_name: '오토바이전자기기' },
			{ cate4_name: '후방카메라/감지기' },
			{ cate4_name: '카메라/카메라용품' },
			{ cate4_name: '저장기기' },
			{ cate4_name: '배터리/전기용품' },
			{ cate4_name: '보안가전' },
			{ cate4_name: '조명' },
			{ cate4_name: '스마트기기용품' },
			{ cate4_name: '스마트워치/액세서리' },
			{
				cate4_name: '태블릿PC/액세서리',
				cate5_name: { not: '태블릿PC' }
			},
			{
				cate4_name: '컴퓨터용품',
				cate5_name: { not: '그래픽카드' }
			},
			{
				cate4_name: '휴대폰/액세서리',
				cate5_name: { notEndsWith: '휴대폰' }
			},
			{
				cate4_name: '컴퓨터주변기기',
				cate5_name: { not: '모니터' }
			}
		],
		handler: (invoice_type: string, productName: string) => {
			if (invoice_type === InvoiceTypes.Checked) {
				return [
					[GradeBasis.Price, 0, 50000, GradeBasisUnit.Won, 6000],
					[GradeBasis.Price, 50000, 100000, GradeBasisUnit.Won, 10000],
					[GradeBasis.Price, 100000, 200000, GradeBasisUnit.Won, 17000],
					[GradeBasis.Price, 200000, 100000000, GradeBasisUnit.Won, 25000]
				];
			} else if (invoice_type === InvoiceTypes.Repaired) {
				return [
					[GradeBasis.Price, 0, 50000, GradeBasisUnit.Won, 8000],
					[GradeBasis.Price, 50000, 100000, GradeBasisUnit.Won, 15000],
					[GradeBasis.Price, 100000, 200000, GradeBasisUnit.Won, 24000],
					[GradeBasis.Price, 200000, 100000000, GradeBasisUnit.Won, 34000]
				];
			}
		}
	},
	MOBILE_ACCESSORIES: {
		conditions: [
			{
				cate4_name: '휴대폰/액세서리',
				cate5_name: { endsWith: '휴대폰' }
			}
		],
		handler: (invoice_type: string, productName: string) => {
			if (invoice_type === InvoiceTypes.Checked) {
				return [
					[GradeBasis.Price, 0, 500000, GradeBasisUnit.Won, 20000],
					[GradeBasis.Price, 500000, 100000000, GradeBasisUnit.Won, 29000]
				];
			} else if (invoice_type === InvoiceTypes.Repaired) {
				return [
					[GradeBasis.Price, 0, 500000, GradeBasisUnit.Won, 44000],
					[GradeBasis.Price, 500000, 100000000, GradeBasisUnit.Won, 56000]
				];
			}
		}
	},
	DEFAULT: {
		conditions: [], // 빈 배열은 항상 매치됨을 의미합니다.
		handler: (invoice_type: string) => {
			if (invoice_type === InvoiceTypes.Checked) {
				return [
					[GradeBasis.Price, 0, 50000, GradeBasisUnit.Won, 5800],
					[GradeBasis.Price, 50000, 100000000, GradeBasisUnit.Won, 5800]
				];
			} else if (invoice_type === InvoiceTypes.Repaired) {
				return [
					[GradeBasis.Price, 0, 50000, GradeBasisUnit.Won, 8200],
					[GradeBasis.Price, 50000, 100000000, GradeBasisUnit.Won, 8200]
				];
			}
		}
	}
};


export class CategoryInvoiceClass {
	public static readonly INVOICE_TYPE_NAME: { [key in InvoiceTypes]: string } = {
		[InvoiceTypes.Checked]: '점검',
		[InvoiceTypes.Repaired]: '기본수리',
		[InvoiceTypes.ReinstalledOS]: 'OS재설치'
	};
	public static readonly GRADE_BASIS_TYPE_NAME: { [key in GradeBasis]: string } = {
		[GradeBasis.Size]: '크기',
		[GradeBasis.Price]: '판매가',
		[GradeBasis.None]: '공통'
	};

	public static readonly GRADE_BASIS_UNIT_NAME: { [key in GradeBasisUnit]: string } = {
		[GradeBasisUnit.Inch]: 'inch',
		[GradeBasisUnit.CM]: 'cm',
		[GradeBasisUnit.Won]: '원'
	};

	public getGradeBasis(cate4_name: string, cate5_name: string, productName: string, invoice_type: string) {
		for (const [categoryName, category] of Object.entries(CATEGORY_RULES)) {
			if (matchesCategory(category, cate4_name, cate5_name)) {
				return category.handler(invoice_type, productName);
			}
		}

		// 모든 규칙에 해당하지 않는 경우 DEFAULT 규칙 적용
		return CATEGORY_RULES.DEFAULT.handler(invoice_type);
	}
}