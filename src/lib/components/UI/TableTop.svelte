<script lang="ts">
	import { getNumberFormat } from '$lib/Functions';

	interface Props {
		total: number;
		pageSize: string;
		onUpdate: any;
		left?: import('svelte').Snippet;
		right?: import('svelte').Snippet;
	}
	
	let { total, pageSize, onUpdate, left, right }: Props = $props();

	function updateParams(event: Event) {
		onUpdate({
			detail: {
				pageSize: pageSize
			}
		});
	}
</script>

<div class="w-full p-1 flex items-center justify-between">
	<div class="inline-flex border border-neutral-300 rounded-md">
		<span class="flex items-center pl-2">
			전체 {getNumberFormat(total)}
		</span>
		
		<span class="flex items-center px-3">|</span>
		
		<span>
			<select bind:value={pageSize}
			        class="select select-sm max-w-xs py-0 border-0 rounded-md focus:ring-0 focus:outline-none"
							onchange={updateParams}
			>
				<option value="10">10개씩 보기</option>
				<option value="15">15개씩 보기</option>
				<option value="20">20개씩 보기</option>
				<option value="30">30개씩 보기</option>
				<option value="50">50개씩 보기</option>
				<option value="100">100개씩 보기</option>
				<option value="200">200개씩 보기</option>
				<option value="300">300개씩 보기</option>
				<option value="500">500개씩 보기</option>
			</select>
		</span>
	</div>
	
	<div class="flex flex-grow items-center justify-between">
		<div>
			{@render left?.()}
		</div>
		
		<div>
			{@render right?.()}
		</div>
	</div>
</div>