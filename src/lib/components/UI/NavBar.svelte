<script lang="ts">
    import { themes, setTheme } from '$lib/Themes';
    import {authClient} from "$lib/AxiosBackend";
    import {removeUser, type User} from "$lib/User";

    import Icon from "svelte-awesome";
    import {faUser} from "@fortawesome/free-regular-svg-icons/faUser";
    import {faPalette} from "@fortawesome/free-solid-svg-icons/faPalette";
    import {faRightFromBracket} from "@fortawesome/free-solid-svg-icons/faRightFromBracket";
    import {goto} from "$app/navigation";

    interface Props {
        user: User;
        currentTheme: string;
    }

    let { user, currentTheme = $bindable() }: Props = $props();

    async function logout() {
        await authClient.post('/wms/logout')
            .then(async (res) => {
                if (res.status === 200 && res.data.authenticated === false) {
                    await removeUser();

                    await goto('/login');
                }
            });
    }
</script>

<div id="navbar" class="navbar bg-base-100 border-b border-gray-100">
    <div class="navbar-start">
        <div class="dropdown">
            <label tabindex="0" class="btn btn-ghost lg:hidden">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h8m-8 6h16" />
                </svg>
            </label>
            <ul tabindex="0" class="menu menu-sm dropdown-content mt-3 z-[1] p-2 shadow bg-base-100 rounded-box w-52">
                <li>
                    <a>메인</a>
                    <ul class="p-2">
                        <li><a>검색</a></li>
                        <li><a>공지사항</a></li>
                        <li><a>생산현황</a></li>
                    </ul>
                </li>
                <li>
                    <a>입고</a>
                    <ul class="p-2">
                        <li><a>입고목록</a></li>
                        <li><a>작업목록</a></li>
                        <li><a>중복상품목록</a></li>
                        <li><a>미등록상품목록</a></li>
                    </ul>
                </li>
                <li>
                    <a>출고</a>
                    <ul class="p-2">
                        <li><a>출고상품적재</a></li>
                        <li><a>출고팔레트목록</a></li>
                    </ul>
                </li>
                <li>
                    <a>반출/반입</a>
                    <ul class="p-2">
                        <li><a>외주반출목록</a></li>
                        <li><a>외주반출수리</a></li>
                        <li><a>반출상품반입</a></li>
                    </ul>
                </li>
                <li>
                    <a>설정</a>
                    <ul class="p-2">
                        <li><a>직원 정보</a></li>
                        <li><a>점검코드 설정</a></li>
                        <li><a>팔레트 관리</a></li>
                    </ul>
                </li>
            </ul>
        </div>
        <button class="btn btn-ghost normal-case text-xl">CNSProWMS</button>
    </div>
    <div class="navbar-center hidden lg:flex">
        <ul class="menu menu-horizontal px-1">
            <li tabindex="0">
                <details>
                    <summary>메인</summary>
                    <ul class="p-2 w-32">
                        <li><a>검색</a></li>
                        <li><a>공지사항</a></li>
                        <li><a>생산현황</a></li>
                    </ul>
                </details>
            </li>
            <li tabindex="0">
                <details>
                    <summary>입고</summary>
                    <ul class="p-2 w-40">
                        <li><a>입고목록</a></li>
                        <li><a>작업목록</a></li>
                        <li><a>중복상품목록</a></li>
                        <li><a>미등록상품목록</a></li>
                    </ul>
                </details>
            </li>
            <li tabindex="0">
                <details>
                    <summary>출고</summary>
                    <ul class="p-2 w-40">
                        <li><a>출고상품적재</a></li>
                        <li><a>출고팔레트목록</a></li>
                    </ul>
                </details>
            </li>
            <li tabindex="0">
                <details>
                    <summary>반출/반입</summary>
                    <ul class="p-2 w-40">
                        <li><a>외주반출목록</a></li>
                        <li><a>외주반출수리</a></li>
                        <li><a>반출상품반입</a></li>
                    </ul>
                </details>
            </li>
            <li tabindex="0">
                <details>
                    <summary>설정</summary>
                    <ul class="p-2 w-36">
                        <li><a>직원 정보</a></li>
                        <li><a>점검코드 설정</a></li>
                        <li><a>팔레트 관리</a></li>
                    </ul>
                </details>
            </li>
        </ul>
    </div>
    <div class="navbar-end">
        <div class="dropdown dropdown-hover dropdown-end">
            <button tabindex="0" class="btn btn-ghost">
                <Icon data={faUser} />
            </button>

            <ul class="menu menu-sm dropdown-content mt-3 z-[1] p-2 shadow bg-base-100 rounded-box w-52">
                <li><a class="justify-between">하위메뉴 1</a></li>
                <li><a class="justify-between">하위메뉴 2</a></li>
            </ul>
        </div>
        <div>
            <button tabindex="0" class="btn btn-ghost" onclick={logout}>
                <Icon data={faRightFromBracket} />
            </button>
        </div>
        <div class="dropdown dropdown-hover dropdown-end">
            <button tabindex="0" class="btn btn-ghost">
                <Icon data={faPalette} />
            </button>

            <ul class="dropdown-content menu z-[1] p-2 shadow bg-base-100 rounded-box w-52 border border-base-300">
                {#each themes as theme}
                    <li>
                        <button class="justify-between" onclick={() => {
                            setTheme(theme);
                            currentTheme = theme;
                        }}>
                            {theme}
                            {#if theme === currentTheme}
                                <span class="badge badge-primary">현재</span>
                            {/if}
                        </button>
                    </li>
                {/each}
            </ul>
        </div>
    </div>
</div>