<script lang="ts">
    import { afterNavigate, goto } from '$app/navigation';
    import { page } from '$app/state';
    
    import Icon from 'svelte-awesome';
    import { faArrowLeft } from '@fortawesome/free-solid-svg-icons/faArrowLeft';
    
    let search = new URLSearchParams(page.url.search);
    
    // 불필요한 parameter 제거
    if (search.has('id')) {
        search.delete('id');
    }
    
    let url = $state('');
    afterNavigate(({ from, to }) => {
        url = from?.url.pathname + '?' + search.toString();
    });
    
    function goBack() {
        goto(url);
    }
</script>

<div class="pl-2 flex justify-start flex-col items-start space-y-2">
    <div class="flex flex-row items-center space-x-1">
        <button class="btn btn-ghost"
                onclick={goBack}
        >
            <Icon class="text-base-content" data={faArrowLeft} />
            <span class="text-base-content leading-none">Back</span>
        </button>
    </div>
</div>