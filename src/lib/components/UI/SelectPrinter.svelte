<script lang="ts">
	import { onMount } from 'svelte';

	import type { Printer } from 'tauri-plugin-printer-api/dist/types';
	import { printers } from 'tauri-plugin-printer-api';
	import { getDefaultPrinter, getLocalLabelPrintList, setDefaultPrinter } from '$lib/Functions';

	import Icon from 'svelte-awesome';
	import { faCheck } from '@fortawesome/free-solid-svg-icons/faCheck';
	import { faCaretRight } from '@fortawesome/free-solid-svg-icons/faCaretRight';
	import { faCaretDown } from '@fortawesome/free-solid-svg-icons/faCaretDown';

	interface Props {
		allListView?: boolean;
		className?: string;
	}

	let { allListView = true, className = '' }: Props = $props();

	let visible = $state(true);

	let allPrinters: Printer[] = $state([]);
	// 사용 가능한 라벨 프린트 리스트
	let myPrinters: Printer[] = $state([]);
	// 사용할 라벨 프린트
	let defaultPrint: Printer = $state();
	onMount(async () => {
		allPrinters = await printers();
		myPrinters = await getLocalLabelPrintList();
		defaultPrint = getDefaultPrinter();
	});
</script>

<div class={className}>
	<span class="text-xl font-bold">라벨 프린터 선택</span>
	{#if myPrinters}
		<div class="flex flex-col md:flex-row md:space-y-0 md:space-x-3">
			{#each myPrinters as printer}
				{#if printer.id === defaultPrint.id}
					<button
						type="button"
						onclick={() => setDefaultPrinter(printer)}
						class="btn btn-success font-bold p-4"
					>
						<Icon data={faCheck} />
						{printer.name}
					</button>
				{:else}
					<button type="button" onclick={() => setDefaultPrinter(printer)} class="btn p-4">
						{printer.name}
					</button>
				{/if}
			{/each}
		</div>
	{/if}
</div>

{#if allListView && allPrinters && (!defaultPrint || !myPrinters || myPrinters.length === 0)}
	<div class="py-2">
		<p>라벨 프린터를 찾지 못할 경우 버튼을 클릭하고 아래 리스트를 프로그래머에게 전달해 주세요.</p>
		<button class="btn btn-accent text-xl font-bold" onclick={() => (visible = !visible)}>
			{#if visible}
				<Icon data={faCaretRight} />
			{:else}
				<Icon data={faCaretDown} />
			{/if}
			전체 프린트 확인
		</button>

		{#if visible === false}
			<div class="border border-gray-400 rounded p-2">
				{#each allPrinters as printer}
					<p>{printer.name} : {printer.id}</p>
				{/each}
			</div>
		{/if}
	</div>
{/if}
