<script lang="ts">
	import { getHrefFromEventTarget, getNumberFormat, scrollToElement } from '$lib/Functions';

	import Icon from 'svelte-awesome';
	import { faAngleLeft } from '@fortawesome/free-solid-svg-icons/faAngleLeft';
	import { faAngleRight } from '@fortawesome/free-solid-svg-icons/faAngleRight';

	interface Props {
		links: string[];
		localUrl: string;
		onUpdate: any;
		pageCurrentPage: number;
		pagePrevPageUrl: string;
		pageNextPageUrl: string;
		searchParams: string | undefined;
	}

	let { links, localUrl, onUpdate, pageCurrentPage, pagePrevPageUrl, pageNextPageUrl, searchParams }: Props = $props();

	if (searchParams === undefined) searchParams = '';

	async function updateParams(event: Event) {
		event.preventDefault();
		const target = event.target as HTMLElement;

		if (target) {
			let href = getHrefFromEventTarget(target);
			href = href?.replace('&undefined', '');

			if (href) {
				let urlObj = new URL(href);
				let params = new URLSearchParams(urlObj.search);

				let paramsObj = {};
				params.forEach((value, key) => {
					// page 키가 중복되는데, 첫번 째 것만 저장한다.
					if (value && !Object.prototype.hasOwnProperty.call(paramsObj, key)) {
						paramsObj[key] = value;
					}
				});

				onUpdate({
					detail: {
						p: paramsObj['p']
					}
				});
			}
		}
	}
</script>

<div class="divider"></div>

<nav aria-label="Table navigation" class="flex flex-col space-y-3 items-center justify-center">
	<div class="join">
		{#if pagePrevPageUrl}
			<a class="join-item btn"
				 href={`${localUrl}?p=${pageCurrentPage - 1}&${searchParams}#search-box-bottom`}
				 onclick={async (e) => {
				   await updateParams(e);
				   scrollToElement("search-box-bottom");
			   }}
			>
				<Icon data={faAngleLeft} />
			</a>
		{/if}
		
		{#each links as link}
			{#if link.label === "..."}
			<span class="join-item btn btn-disabled">
				...
			</span>
			{:else if link.active}
				<span class="join-item btn btn-accent cursor-default">
					{getNumberFormat(Number(link.label))}
				</span>
			{:else}
				<a class="join-item btn"
					 href={`${localUrl}?p=${link.label}&${searchParams}`}
					 onclick={async (e) => {
					   await updateParams(e);
					   scrollToElement("search-box-bottom");
				   }}
				>
					{getNumberFormat(Number(link.label))}
				</a>
			{/if}
		{/each}
		
		{#if pageNextPageUrl}
			<a class="join-item btn"
				 href={`${localUrl}?p=${pageCurrentPage + 1}&${searchParams}`}
				 onclick={async (e) => {
				   await updateParams(e);
				   scrollToElement("search-box-bottom");
			   }}
			>
				<Icon data={faAngleRight} />
			</a>
		{/if}
	</div>
</nav>