<script lang="ts">
	import {
		formatDateTimeToFullString,
		formatDateTimeToString,
		getNumberFormat,
		isOverDueDate
	} from '$lib/Functions.js';
	import { getPalletNo, getPalletStatusName } from '$stores/palletStore.js';
	import { getProcessGradeColorButton } from '$stores/processStore.js';
	import { getProductCheckedStatusName } from '$stores/productStore.js';

	import Icon from 'svelte-awesome';
	import { faRocket } from '@fortawesome/free-solid-svg-icons/faRocket';
	
	interface Props {
		ids: string[];
		startNo: number;
		products: any[];
	}
	
	let { ids = $bindable([]), startNo, products }: Props = $props();

	let allChecked = $state(false); // 전체 선택: 체크박스
	let idChecked = $state([]); // 전체 선택: 모든 체크박스의 상태를 참조하는 reactive 변수
	
	// 전체 선택
	const toggleAllCheck = (items: any) => {
		allChecked = !allChecked;
		idChecked = items.map(() => allChecked);
		ids = allChecked ? items.map((item: any) => item.id) : [];
	};
</script>

<table class="table text-xs table-pin-rows table-zebra">
	<thead class="uppercase relative">
	<tr class="bg-base-content text-base-300 text-center h-[60px]">
		<th class="p-0.5">
			<input checked={allChecked}
						 onchange={() => toggleAllCheck(products)}
						 type="checkbox"
			/>
		</th>
		<th class="p-0.5">번호</th>
		<th class="p-0.5">입고날짜</th>
		<th class="p-0.5">로트번호</th>
		<th class="p-0.5">카테고리</th>
		<th class="p-0.5">QAID</th>
		<th class="p-0.5">
			상품명<br>
			바코드
		</th>
		<th class="p-0.5">중복</th>
		<th class="p-0.5">단가</th>
		<th class="p-0.5">검수상태</th>
		<th class="p-0.5">검수일자</th>
		<th class="p-0.5">점검(수리)<br>상태</th>
		<th class="p-0.5">점검(수리)<br>일자</th>
		<th class="p-0.5">출고(적재)<br>상태</th>
		<th class="p-0.5">출고일자</th>
	</tr>
	</thead>
	
	<tfoot class="uppercase relative">
	<tr class="bg-base-content text-base-300 text-center">
		<th class="p-0.5">
			<input checked={allChecked}
						 onchange={() => toggleAllCheck(products)}
						 type="checkbox"
			/>
		</th>
		<th class="p-0.5">번호</th>
		<th class="p-0.5">입고날짜</th>
		<th class="p-0.5">로트번호</th>
		<th class="p-0.5">카테고리</th>
		<th class="p-0.5">QAID</th>
		<th class="p-0.5">
			상품명<br>
			바코드
		</th>
		<th class="p-0.5">중복</th>
		<th class="p-0.5">단가</th>
		<th class="p-0.5">검수상태</th>
		<th class="p-0.5">검수일자</th>
		<th class="p-0.5">점검(수리)<br>상태</th>
		<th class="p-0.5">점검(수리)<br>일자</th>
		<th class="p-0.5">출고(적재)<br>상태</th>
		<th class="p-0.5">출고일자</th>
	</tr>
	</tfoot>
	
	<tbody>
	{#if products}
		{#each products as item, index}
			<tr class="hover:bg-base-content/10">
				<td class="w-[20px] min-w-[20px] max-w-[20px] p-0.5 text-center">
					<input bind:checked={idChecked[index]}
								 bind:group={ids}
								 value={item.id}
								 type="checkbox"
					/>
				</td>
				<td class="w-[50px] min-w-[50px] max-w-[50px] p-0.5 text-center">
					{getNumberFormat(startNo - index)}
				</td>
				<td class="w-[80px] min-w-[80px] max-w-[80px] p-0.5 text-center">
					<span class={item.status === 10 && isOverDueDate(item.req.req_at, item.rg)
						? 'font-bold text-red-700'
						: ''}>
						{item.req.req_at}
					</span>
				</td>
				<td class="w-[110px] min-w-[70px] max-w-[110px] p-0.5 text-center">
					{item.lot.name}
				</td>
				<td class="w-[180px] min-w-[120px] max-w-[180px] p-0.5 text-center">
					<div class="flex items-center">
						<div class="w-1/2 p-0">{item.cate4.name}</div>
						{#if item.cate5}
							<div class="w-1/2 p-0">{item.cate5.name}</div>
						{/if}
					</div>
				</td>
				<td class="w-[100px] min-w-[100px] max-w-[120px] p-0.5 text-base text-center">
					<span class="flex">
						{item.qaid}
						
						{#if item.rg === 'Y'}
							<Icon data={faRocket} class="mx-0.5 text-red-700" />
						{/if}
					</span>
				</td>
				<td class="p-0.5">
					<p>{item.name}</p>
					<p class="text-base-content/50">{item.barcode}</p>
				</td>
				<td class="w-[30px] min-w-[30px] max-w-[30px] p-0.5 text-center">
					{#if item.duplicated === "N"}
						-
					{:else}
						중복
					{/if}
				</td>
				<td class="w-[60px] min-w-[60px] max-w-[60px] p-0.5 text-right">
					{getNumberFormat(item.amount)}
				</td>
				<td class="w-[76px] min-w-[76px] max-w-[76px] p-0.5 text-center">
					{getProductCheckedStatusName(item.checked_status)}
				</td>
				<td class="w-[90px] min-w-[86px] max-w-[90px] p-0.5 text-center">
					{#if item.checked_at}
						{formatDateTimeToFullString(item.checked_at)}
					{:else}
						-
					{/if}
				</td>
				<td class="w-[50px] min-w-[50px] max-w-[50px] p-0.5 text-center">
					{#if item.pallet_products.length > 0}
						{@html getProcessGradeColorButton(item.pallet_products[0].process_grade)}
					{:else}
						-
					{/if}
				</td>
				<td class="w-[90px] min-w-[86px] max-w-[90px] p-0.5 text-center">
					{#if item.pallet_products.length > 0}
						{formatDateTimeToFullString(item.pallet_products[0].registered_at)}
					{:else}
						-
					{/if}
				</td>
				<td class="w-[90px] min-w-[86px] max-w-[90px] p-0.5 text-center">
					{#if item.pallet_products.length > 0}
						{getPalletStatusName(item.pallet_products[0].pallet.status)}
					{:else}
						-
					{/if}
				</td>
				<td class="w-[90px] min-w-[86px] max-w-[90px] p-0.5 text-center">
					{#if item.pallet_products.length > 0}
						<p>{formatDateTimeToString(item.pallet_products[0].pallet.exported_at)}</p>
						<p class="badge badge-info badge-sm">
							<a href="/pallets/products?id={item.pallet_products[0].pallet_id}">
								{getPalletNo(item.pallet_products[0].pallet.location)}
							</a>
						</p>
					{:else}
						-
					{/if}
				</td>
			</tr>
		{/each}
	{/if}
	</tbody>
</table>