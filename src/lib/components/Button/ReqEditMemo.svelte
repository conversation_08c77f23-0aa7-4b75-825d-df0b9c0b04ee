<script>
	import { faFilePen } from '@fortawesome/free-solid-svg-icons/faFilePen';
	import Icon from 'svelte-awesome';

	/** @type {{color?: string, size?: string, margin?: string, useTooltip?: boolean, tooltipData?: string, onclick: any}} */
	let {
		color = 'btn-ghost',
		size = 'btn-sm',
		margin = 'ml-4',
		useTooltip = false,
		tooltipData = '메모/상태 수정',
		onclick
	} = $props();
</script>

{#if useTooltip}
	<button class="btn {color} {size} {margin} tooltip"
					data-tip={tooltipData}
					{onclick}
					type="button"
	>
		<Icon data={faFilePen} scale={1.3} class="text-secondary" />
	</button>
{:else}
	<button class="btn {color} {size} {margin}"
					{onclick}
					type="button"
	>
		<Icon data={faFilePen} scale={1.3} class="text-secondary" />
	</button>
{/if}