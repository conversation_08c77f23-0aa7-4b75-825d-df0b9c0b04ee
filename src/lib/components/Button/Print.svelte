<script>
	import { faPrint } from '@fortawesome/free-solid-svg-icons/faPrint';
	import Icon from 'svelte-awesome';

	/** @type {{color?: string, size?: string, margin?: string, useTooltip?: boolean, tooltipData?: string, onclick: any}} */
	let {
		color = 'btn-success',
		size = 'btn-sm',
		margin = 'ml-4',
		useTooltip = false,
		tooltipData = '프린트',
		onclick
	} = $props();
</script>

{#if useTooltip}
	<button class="btn {color} {size} {margin} tooltip tooltip-bottom"
					data-tip={tooltipData}
					{onclick}
					type="button"
	>
		<Icon data={faPrint} />
		선택목록 인쇄
	</button>
{:else}
	<button class="btn {color} {size} {margin}"
					{onclick}
					type="button"
	>
		<Icon data={faPrint} />
		선택목록 인쇄
	</button>
{/if}