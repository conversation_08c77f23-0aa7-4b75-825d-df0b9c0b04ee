<script>
	import { faTruckLoading } from '@fortawesome/free-solid-svg-icons/faTruckLoading';
	import Icon from 'svelte-awesome';

	/** @type {{color?: string, size?: string, margin?: string, useTooltip?: boolean, tooltipData?: string, onclick: any}} */
	let {
		color = 'btn-primary',
		size = 'btn-sm',
		margin = 'ml-4',
		useTooltip = false,
		tooltipData = '검색',
		onclick
	} = $props();
</script>

{#if useTooltip}
	<button class="btn {color} {size} {margin} tooltip tooltip-bottom"
					data-tip={tooltipData}
					{onclick}
					type="button"
	>
		<Icon data={faTruckLoading} />
		선택 출고
	</button>
{:else}
	<button class="btn {color} {size} {margin}"
					{onclick}
					type="button"
	>
		<Icon data={faTruckLoading} />
		선택 출고
	</button>
{/if}