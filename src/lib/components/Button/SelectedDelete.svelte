<script>
	import { faTrashCan } from '@fortawesome/free-regular-svg-icons/faTrashCan';
	import Icon from 'svelte-awesome';

	/** @type {{color?: string, size?: string, margin?: string, useTooltip?: boolean, tooltipData?: string, onclick: any}} */
	let {
		color = 'btn-error',
		size = 'btn-sm',
		margin = 'ml-4',
		useTooltip = false,
		tooltipData = '선택한 것 삭제',
		onclick
	} = $props();
</script>

{#if useTooltip}
	<button class="btn {color} {size} {margin} tooltip tooltip-right"
					data-tip={tooltipData}
					{onclick}
					type="button"
	>
		<Icon class="w-4 h-4 mr-2" data={faTrashCan} />
		선택삭제
	</button>
{:else}
	<button class="btn {color} {size} {margin}"
					{onclick}
					type="button"
	>
		<Icon class="w-4 h-4 mr-2" data={faTrashCan} />
		선택삭제
	</button>
{/if}