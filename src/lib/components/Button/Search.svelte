<script>
	import { faSearch } from '@fortawesome/free-solid-svg-icons/faSearch';
	import Icon from 'svelte-awesome';

	/** @type {{color?: string, size?: string, margin?: string, useTooltip?: boolean, tooltipData?: string, tooltipDirection?: string, onclick: any}} */
	let {
		color = 'btn-primary',
		size = 'btn-sm',
		margin = 'ml-4',
		useTooltip = false,
		tooltipData = '검색',
		tooltipDirection = 'tooltip-top',
		onclick
	} = $props();
</script>

{#if useTooltip}
	<button class="btn {color} {size} {margin} tooltip {tooltipDirection}"
					data-tip={tooltipData}
					{onclick}
					type="button"
	>
		<Icon data={faSearch} />
		검색
	</button>
{:else}
	<button class="btn {color} {size} {margin}"
					{onclick}
					type="button"
	>
		<Icon data={faSearch} />
		검색
	</button>
{/if}