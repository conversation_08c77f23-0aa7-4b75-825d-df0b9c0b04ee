<script>
	import { faFileExcel } from '@fortawesome/free-regular-svg-icons/faFileExcel';
	import Icon from 'svelte-awesome';

	/** @type {{color?: string, size?: string, margin?: string, useTooltip?: boolean, tooltipData?: string, onclick: any}} */
	let {
		color = 'btn-success',
		size = 'btn-sm',
		margin = 'ml-4',
		useTooltip = false,
		tooltipData = '저장',
		onclick
	} = $props();
</script>

{#if useTooltip}
	<button class="btn {color} {size} {margin} tooltip tooltip-bottom"
					data-tip={tooltipData}
					{onclick}
					type="button"
	>
		<Icon data={faFileExcel} />
		선택 저장
	</button>
{:else}
	<button class="btn {color} {size} {margin}"
					{onclick}
					type="button"
	>
		<Icon data={faFileExcel} />
		선택 저장
	</button>
{/if}