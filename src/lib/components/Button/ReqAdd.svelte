<script>
	import { faPlus } from '@fortawesome/free-solid-svg-icons/faPlus';
	import Icon from 'svelte-awesome';

	/** @type {{color?: string, size?: string, margin?: string, useTooltip?: boolean, tooltipData?: string, onclick: any}} */
	let {
		color = 'btn-ghost',
		size = 'btn-sm',
		margin = 'ml-4',
		useTooltip = false,
		tooltipData = '상품 추가',
		onclick
	} = $props();
</script>

{#if useTooltip}
	<button class="btn {color} {size} {margin} tooltip tooltip-left"
					data-tip={tooltipData}
					{onclick}
					type="button"
	>
		<Icon data={faPlus} scale={1.3} class="text-info" />
	</button>
{:else}
	<button class="btn {color} {size} {margin}"
					{onclick}
					type="button"
	>
		<Icon data={faPlus} scale={1.3} class="text-info" />
	</button>
{/if}