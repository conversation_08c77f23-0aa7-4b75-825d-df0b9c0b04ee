<script lang="ts">
	import type { User } from '$lib/User';

	import GuestTopNav from '$components/Layouts/GuestTopNav.svelte';
	import Footer from '$components/Layouts/Footer.svelte';

	interface Props {
		user: User;
		main?: import('svelte').Snippet;
	}

	let { user, main }: Props = $props();
</script>

<svelte:head>
	<title>CornerStone Project WMS</title>
</svelte:head>

<GuestTopNav {user} />
<div class="p-2">
	
	<div class="flex flex-col xl:flex-row">
		<div class="w-full">
			{@render main?.()}
		</div>
	</div>

</div>
<Footer />