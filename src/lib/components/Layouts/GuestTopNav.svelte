<script lang="ts">
	import { goto } from '$app/navigation';
	import { browser } from '$app/environment';

	import { setTheme, themes } from '$lib/Themes';
	import { authClient } from '$lib/AxiosBackend';
	import { removeUser, type User } from '$lib/User';

	import Icon from 'svelte-awesome';
	import { faRightFromBracket } from '@fortawesome/free-solid-svg-icons/faRightFromBracket';
	import { faPalette } from '@fortawesome/free-solid-svg-icons/faPalette';
	import { faCircleCheck } from '@fortawesome/free-solid-svg-icons/faCircleCheck';

	interface Props {
		user: User;
	}

	let { user }: Props = $props();

	let currentTheme = $state('light');
	if (browser) {
		let theme = window.localStorage.getItem('theme');
		currentTheme = theme || currentTheme;
	}

	async function logout() {
		await authClient.post(`/wms/logout`)
			.then(async (res) => {
				if (res.status === 200 && res.data.authenticated === false) {
					await removeUser();

					await goto('/login');
				}
			}).catch(async (err) => {
				if (err.response.status === 500) {
					await removeUser();

					await goto('/login');
				}
			});
	}
</script>

<svelte:window />

<nav
	class="navbar bg-base-100 top-0 left-0 right-0 fixed flex h-14 border-b border-base-100 z-30 w-screen transition-all md:w-auto">
	<div class="w-full flex justify-end">
		<div class="dropdown dropdown-hover dropdown-end">
			<button class="btn btn-ghost" tabindex="0" type="button">
				<Icon data={faPalette} />
			</button>
			
			<ul class="dropdown-content menu z-[1] p-2 bg-base-300 shadow rounded-box w-96 h-96 border border-base-300">
				{#each themes as theme}
					<li>
						<button class="items-center"
										onclick={() => {
                      setTheme(theme);
                      currentTheme = theme;
                    }}
						>
							{#if theme === currentTheme}
								<Icon data={faCircleCheck} />
							{/if}
							{theme}
						</button>
					</li>
				{/each}
			</ul>
		</div>
		<div class="tooltip tooltip-bottom" data-tip="Logout">
			<button class="btn btn-ghost" onclick={logout} tabindex="0"
							type="button"
			>
				<Icon data={faRightFromBracket} label="로그아웃" />
				로그아웃
			</button>
		</div>
	</div>
</nav>