<script lang="ts">
	import { goto } from '$app/navigation';
	import { browser } from '$app/environment';

	import { setTheme, themes } from '$lib/Themes';
	import { authClient } from '$lib/AxiosBackend';
	import { removeUser, type User } from '$lib/User';

	import Icon from 'svelte-awesome';
	import { faRightFromBracket } from '@fortawesome/free-solid-svg-icons/faRightFromBracket';
	import { faPalette } from '@fortawesome/free-solid-svg-icons/faPalette';
	import { faEllipsisVertical } from '@fortawesome/free-solid-svg-icons/faEllipsisVertical';
	import { faCircleCheck } from '@fortawesome/free-solid-svg-icons/faCircleCheck';

	interface Props {
		user: User;
	}

	let { user }: Props = $props();

	let currentTheme = $state('light');
	if (browser) {
		let theme = window.localStorage.getItem('theme');
		currentTheme = theme || currentTheme;
	}

	async function logout() {
		await authClient.post(`/wms/logout`)
			.then(async (res) => {
				if (res.status === 200 && res.data.authenticated === false) {
					await removeUser();

					await goto('/login');
				}
			}).catch(async (err) => {
				if (err.response.status === 500) {
					await removeUser();

					await goto('/login');
				} else {
					console.error("로그아웃 에러: ", err);
				}
			});
	}
</script>

<svelte:window />

<nav class="navbar bg-base-100 top-0 left-0 right-0 fixed flex h-14 border-b border-base-100 z-30 w-screen transition-all">
	<div class="w-full flex justify-end">
		<!-- 모바일 대응 메뉴 -->
		<div class="dropdown dropdown-end md:hidden">
			<button class="btn btn-ghost" tabindex="0" type="button">
				<Icon class="w-5 h-5" data={faEllipsisVertical} />
			</button>
			
			<ul class="menu menu-sm dropdown-content mt-3 z-[1] p-2 shadow bg-base-300 rounded-box w-52">
				<li>
					<div>
						<div class="w-6 h-6 inline-flex">
							<img alt="{user?.name}"
									 class="rounded-full"
									 src="https://api.dicebear.com/7.x/initials/svg?seed=Cnspro&radius=50"
							/>
						</div>
						
						{user?.name}
					</div>
					<ul class="p-2">
						<li><a href="/me/edit">내 정보</a></li>
					</ul>
				</li>
			</ul>
		</div>
		
		<!-- 데스크탑 대응 메뉴 -->
		<div class="hidden md:flex">
			<ul class="menu menu-horizontal px-1">
				<li>
					<details>
						<summary>
							<div class="w-6 h-6 inline-flex">
								<img alt="{user?.name}"
										 class="rounded-full"
										 src="https://api.dicebear.com/7.x/initials/svg?seed=Cnspro&radius=50"
								/>
							</div>
							
							{user?.name}
						</summary>
						<ul class="top-nav-submenu">
							<li><a href="/me/edit">내 정보</a></li>
						</ul>
					</details>
				</li>
			</ul>
		</div>
		
		<div class="dropdown dropdown-hover dropdown-end">
			<button class="btn btn-ghost" tabindex="0" type="button">
				<Icon data={faPalette} />
			</button>
			
			<ul class="dropdown-content menu z-[1] p-2 bg-base-300 shadow rounded-box w-96 h-96 border border-base-300">
				{#each themes as theme}
					<li>
						<button class="items-center"
										onclick={() => {
                      setTheme(theme);
                      currentTheme = theme;
                    }}
						>
							{#if theme === currentTheme}
								<Icon data={faCircleCheck} />
							{/if}
							{theme}
						</button>
					</li>
				{/each}
			</ul>
		</div>
		<div class="tooltip tooltip-bottom" data-tip="Logout">
			<button class="btn btn-ghost" onclick={logout} tabindex="0"
							type="button"
			>
				<Icon data={faRightFromBracket} label="로그아웃" />
			</button>
		</div>
	</div>
</nav>