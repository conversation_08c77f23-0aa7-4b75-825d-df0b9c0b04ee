<script lang="ts">
	import { page } from '$app/state';
	import { sideNavVisible } from '$lib/stores/constant';

	import Icon from 'svelte-awesome';
	import { faBars } from '@fortawesome/free-solid-svg-icons/faBars';
	import { faAnglesLeft } from '@fortawesome/free-solid-svg-icons/faAnglesLeft';

	import Menu from '$components/Layouts/Menu.svelte';

	let routeId: string = page.route.id as string;
	
	function toggleSideNav() {
		$sideNavVisible = !$sideNavVisible;
	}
</script>

{#if $sideNavVisible}
	<aside class="aside">
		<div class="aside-desktop">
			<div>
				<a href="/dashboard">CornerStone<b class="font-black">Project</b></a>
				
				<button class="btn btn-ghost" tabindex="0" type="button" onclick={toggleSideNav}>
					<Icon class="w-5 h-5" data={faAnglesLeft} />
				</button>
			</div>
		</div>
		<div class="menu">
			<Menu {routeId} />
		</div>
	</aside>
{:else}
	<aside>
		<div class="aside-mobile">
			<div>
				<a href="/dashboard">CornerStone<b class="font-black">Project</b></a>
				
				<button class="btn btn-ghost" tabindex="0" type="button" onclick={toggleSideNav}>
					<Icon class="w-5 h-5" data={faBars} />
				</button>
			</div>
		</div>
	</aside>
{/if}