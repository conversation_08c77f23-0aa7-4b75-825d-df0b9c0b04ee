<script lang="ts">
	import type { User } from '$lib/User';
	import { sideNavVisible } from '$lib/stores/constant';

	import SideNav from '$components/Layouts/SideNav.svelte';
	import TopNav from '$components/Layouts/TopNav.svelte';
	import Footer from '$components/Layouts/Footer.svelte';

	interface Props {
		user: User;
		main?: import('svelte').Snippet;
	}

	let { user, main }: Props = $props();
</script>

<svelte:head>
	<title>CornerStone Project WMS</title>
</svelte:head>

<SideNav {user} />
<TopNav {user} />
<div class="p-2" class:pl-64={$sideNavVisible}>
	<div class="flex flex-1 flex-col xl:flex-row">
		<div class="w-full flex-1">
			{@render main?.()}
		</div>
	</div>
</div>
<Footer />
