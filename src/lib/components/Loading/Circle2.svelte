<script lang="ts">
	import type { Circle2Types } from '$components/Loading/spinner';
	
	interface Props {
		size?: Circle2Types['size'];
		unit?: Circle2Types['unit'];
		pause?: Circle2Types['pause'];
		colorOuter?: Circle2Types['colorOuter'];
		colorCenter?: Circle2Types['colorCenter'];
		colorInner?: Circle2Types['colorInner'];
		durationMultiplier?: Circle2Types['durationMultiplier'];
		durationOuter?: Circle2Types['durationOuter'];
		durationInner?: Circle2Types['durationInner'];
		durationCenter?: Circle2Types['durationCenter'];
	}

	let {
		size = '60',
		unit = 'px',
		pause = false,
		colorOuter = '#FF3E00',
		colorCenter = '#40B3FF',
		colorInner = '#676778',
		durationMultiplier = 1,
		durationOuter = `${durationMultiplier * 2}s`,
		durationInner = `${durationMultiplier * 1.5}s`,
		durationCenter = `${durationMultiplier * 3}s`
	}: Props = $props();
</script>

<div class="fixed z-10 inset-0 bg-black bg-opacity-50 flex items-center justify-center">
	<div
		class="circle"
		class:pause-animation={pause}
		style="--size: {size}{unit}; --colorInner: {colorInner}; --colorCenter: {colorCenter}; --colorOuter: {colorOuter}; --durationInner: {durationInner}; --durationCenter: {durationCenter}; --durationOuter: {durationOuter};"
	></div>
</div>

<style>
	.circle {
		width: var(--size);
		height: var(--size);
		box-sizing: border-box;
		border: 3px solid transparent;
		border-top-color: var(--colorOuter);
		border-radius: 50%;
		animation: circleSpin var(--durationOuter) linear infinite;
	}
	.circle::before,
	.circle::after {
		content: '';
		box-sizing: border-box;
		position: absolute;
		border: 3px solid transparent;
		border-radius: 50%;
	}
	.circle::after {
		border-top-color: var(--colorInner);
		top: 9px;
		left: 9px;
		right: 9px;
		bottom: 9px;
		animation: circleSpin var(--durationInner) linear infinite;
	}
	.circle::before {
		border-top-color: var(--colorCenter);
		top: 3px;
		left: 3px;
		right: 3px;
		bottom: 3px;
		animation: circleSpin var(--durationCenter) linear infinite;
	}
	.pause-animation,
	.pause-animation::after,
	.pause-animation::before {
		animation-play-state: paused;
	}
	
	@keyframes circleSpin {
		0% {
			transform: rotate(0deg);
		}
		100% {
			transform: rotate(360deg);
		}
	}
</style>