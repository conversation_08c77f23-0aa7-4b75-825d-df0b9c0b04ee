<script lang="ts">
	import { createEventDispatcher } from 'svelte';

	import Icon from 'svelte-awesome';
	import { faParagraph } from '@fortawesome/free-solid-svg-icons/faParagraph';
	import { faQuoteLeft } from '@fortawesome/free-solid-svg-icons/faQuoteLeft';
	import { faCode } from '@fortawesome/free-solid-svg-icons/faCode';
	import { faTerminal } from '@fortawesome/free-solid-svg-icons/faTerminal';
	import { faMinus } from '@fortawesome/free-solid-svg-icons/faMinus';
	import { faList } from '@fortawesome/free-solid-svg-icons/faList';
	import { faListOl } from '@fortawesome/free-solid-svg-icons/faListOl';
	import { faArrowTurnDown } from '@fortawesome/free-solid-svg-icons/faArrowTurnDown';
	import { faTextSlash } from '@fortawesome/free-solid-svg-icons/faTextSlash';
	import { faImage } from '@fortawesome/free-regular-svg-icons/faImage';

	interface Props {
		editor: any;
	}

	let { editor }: Props = $props();

	const dispatch = createEventDispatcher();
	let fileInput: HTMLInputElement = $state();

	function addImage(event: Event) {
		if (event.target) {
			const file = event.target.files[0];
			if (file) {
				const reader = new FileReader();
				reader.onload = function(e) {
					dispatch('change', e.target?.result);
				};
				reader.readAsDataURL(file);
			}
		}
	}
</script>

{#if editor}
	<input type="file" accept="image/*" onchange={addImage} class="hidden" bind:this={fileInput} />
	
	<div>
		<button
			class="btn btn-sm hover:btn-neutral {editor.isActive('heading', { level: 1 }) ? 'btn-neutral' : 'btn-ghost'}"
			onclick={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}
			title="제목 1"
			type="button"
		>
			h1
		</button>
		
		<button
			class="btn btn-sm hover:btn-neutral {editor.isActive('heading', { level: 2 }) ? 'btn-neutral' : 'btn-ghost'}"
			onclick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
			title="제목 2"
			type="button"
		>
			h2
		</button>
		
		<button
			class="btn btn-sm hover:btn-neutral {editor.isActive('heading', { level: 3 }) ? 'btn-neutral' : 'btn-ghost'}"
			onclick={() => editor.chain().focus().toggleHeading({ level: 3 }).run()}
			title="제목 3"
			type="button"
		>
			h3
		</button>
		
		<button class="btn btn-sm hover:btn-neutral {editor.isActive('bold') ? 'btn-neutral' : 'btn-ghost'}"
						onclick={() => editor.chain().focus().toggleBold().run()}
						disabled={!editor.can().chain().focus().toggleBold().run()}
						title="굵게"
						type="button"
		>
			<span class="font-bold">B</span>
		</button>
		
		<button class="btn btn-sm hover:btn-neutral {editor.isActive('italic') ? 'btn-neutral' : 'btn-ghost'}"
						onclick={() => editor.chain().focus().toggleItalic().run()}
						disabled={!editor.can().chain().focus().toggleItalic().run()}
						title="기울임꼴"
						type="button"
		>
			<span class="italic">I</span>
		</button>
		
		<!--		<button class="btn btn-sm hover:btn-neutral"-->
		<!--						on:click={() => editor.chain().focus().setColor('#B9F18D').run()}-->
		<!--						className={editor.isActive('textStyle', { color: '#B9F18D' }) ? 'is-active' : ''}-->
		<!--						title="글자 색상"-->
		<!--						type="button"-->
		<!--		>-->
		<!--			<Icon data={faPalette} />-->
		<!--		</button>-->
		
		<button class="btn btn-sm hover:btn-neutral {editor.isActive('strike') ? 'btn-neutral' : 'btn-ghost'}"
						onclick={() => editor.chain().focus().toggleStrike().run()}
						disabled={!editor.can().chain().focus().toggleStrike().run()}
						title="취소선"
						type="button"
		>
			<span class="line-through">S</span>
		</button>
		
		<button class="btn btn-sm hover:btn-neutral {editor.isActive('underline') ? 'btn-neutral' : 'btn-ghost'}"
						onclick={() => editor.chain().focus().toggleUnderline().run()}
						title="밑줄"
						type="button"
		>
			<span class="underline">U</span>
		</button>
		
		<button class="btn btn-sm hover:btn-neutral {editor.isActive('bulletList') ? 'btn-neutral' : 'btn-ghost'}"
						onclick={() => editor.chain().focus().toggleBulletList().run()}
						title="순서 없는 목록"
						type="button"
		>
			<Icon data={faList} />
		</button>
		<button class="btn btn-sm hover:btn-neutral {editor.isActive('orderedList') ? 'btn-neutral' : 'btn-ghost'}"
						onclick={() => editor.chain().focus().toggleOrderedList().run()}
						title="순서 있는 목록"
						type="button"
		>
			<Icon data={faListOl} />
		</button>
		
		<button class="btn btn-sm hover:btn-neutral {editor.isActive('blockquote') ? 'btn-neutral' : 'btn-ghost'}"
						onclick={() => editor.chain().focus().toggleBlockquote().run()}
						title="인용 단락"
						type="button"
		>
			<Icon data={faQuoteLeft} />
		</button>
		
		<button class="btn btn-sm hover:btn-neutral {editor.isActive('image') ? 'btn-neutral' : 'btn-ghost'}"
						onclick={() => fileInput.click()}
						title="이미지 삽입"
						type="button"
		>
			<Icon data={faImage} />
		</button>
		
		<button class="btn btn-sm hover:btn-neutral {editor.isActive('code') ? 'btn-neutral' : 'btn-ghost'}"
						disabled={!editor.can().chain().focus().toggleCode().run()}
						onclick={() => editor.chain().focus().toggleCode().run()}
						title="코드 1줄"
						type="button"
		>
			<Icon data={faTerminal} />
		</button>
		
		<button class="btn btn-sm hover:btn-neutral {editor.isActive('codeBlock') ? 'btn-neutral' : 'btn-ghost'}"
						onclick={() => editor.chain().focus().toggleCodeBlock().run()}
						title="코드 블럭"
						type="button"
		>
			<Icon data={faCode} />
		</button>
		
		<button class="btn btn-sm hover:btn-neutral {editor.isActive('paragraph') ? 'btn-neutral' : 'btn-ghost'}"
						onclick={() => editor.chain().focus().setParagraph().run()}
						title="단락"
						type="button"
		>
			<Icon data={faParagraph} />
		</button>
		
		<button class="btn btn-ghost btn-sm hover:btn-neutral"
						onclick={() => editor.chain().focus().setHorizontalRule().run()}
						title="분리 선"
						type="button"
		>
			<Icon data={faMinus} />
		</button>
		
		<button class="btn btn-ghost btn-sm hover:btn-neutral"
						onclick={() => editor.chain().focus().setHardBreak().run()}
						title="한줄 띄우기(Shift + Enter)"
						type="button"
		>
			<Icon data={faArrowTurnDown} class="rotate-90" />
		</button>
		
		<button class="btn btn-ghost btn-sm hover:btn-neutral"
						onclick={() => editor.chain().focus().clearNodes().unsetAllMarks().run()}
						title="모든 서식 제거"
						type="button"
		>
			<Icon data={faTextSlash} />
		</button>
	</div>
	
	<div class="pt-0 border-0 border-b-2 border-b-neutral-600"></div>
{/if}
