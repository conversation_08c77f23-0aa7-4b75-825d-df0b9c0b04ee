/* Tiptap 에디터 */
.tiptap {
    > * + * {
        margin-top: 0.75em;
    }

    ul {
        display: block;
        list-style-type: disc;
        margin-block-start: 1em;
        margin-block-end: 1em;
        margin-inline-start: 0;
        margin-inline-end: 0;
        padding-inline-start: 40px;
        unicode-bidi: isolate;
    }
    ul > li {
        display: list-item;
        text-align: -webkit-match-parent;
        unicode-bidi: isolate;
    }

    ol {
        display: block;
        list-style-type: decimal;
        margin-block-start: 1em;
        margin-block-end: 1em;
        margin-inline-start: 0;
        margin-inline-end: 0;
        padding-inline-start: 40px;
        unicode-bidi: isolate;
    }
    ol > li {
        display: list-item;
        text-align: -webkit-match-parent;
        unicode-bidi: isolate;
    }

    h1 {
        @apply block text-3xl my-6 mx-0 font-bold;
        unicode-bidi: isolate;
    }

    h2 {
        @apply block text-2xl my-4 mx-0 font-bold;
        unicode-bidi: isolate;
    }

    h3 {
        @apply block text-xl my-4 mx-0 font-bold;
        unicode-bidi: isolate;
    }

    h4,
    h5,
    h6 {
        line-height: 1.1;
    }

    p {
        @apply block my-4 mx-0;
        unicode-bidi: isolate;
    }

    /* placeholder */
    p.is-editor-empty:first-child::before {
        color: #adb5bd;
        content: attr(data-placeholder);
        float: left;
        height: 0;
        pointer-events: none;
    }

    /* placeholder */
    p.is-empty::before {
        color: #adb5bd;
        content: attr(data-placeholder);
        float: left;
        height: 0;
        pointer-events: none;
    }

    blockquote {
        @apply block pl-4 my-4 mx-10 border-l-2 border-neutral-500;
        unicode-bidi: isolate;
    }

    code {
        @apply bg-neutral-300 text-black;
        unicode-bidi: isolate;
    }

    pre {
        @apply w-full py-3 px-4 bg-neutral-700 text-white rounded-lg;
        font-family: 'JetBrainsMono', monospace;

        code {
            color: inherit;
            padding: 0;
            background: none;
            font-size: 0.8rem;
        }
    }

    img {
        @apply max-w-full h-auto;
    }

    hr {
        @apply border-0 border-t-2 border-t-neutral-400 my-4 mx-2;
    }

    a {
        color: #68cef8;
    }
}

.ProseMirror-focused {
    @apply outline-none;
}