<!--
참고 사이트 : https://svelte.dev/repl/be2a7886b58e4ccaa33b9c286255bf1f?version=3.38.2
-->
<script lang="ts">
	import '$components/tiptap/tiptap.pcss';

	import { onDestroy, onMount } from 'svelte';
	import { Editor } from '@tiptap/core';
	import StarterKit from '@tiptap/starter-kit';
	import Underline from '@tiptap/extension-underline';
	import Link from '@tiptap/extension-link';
	import Image from '@tiptap/extension-image';
	import { Placeholder } from '@tiptap/extension-placeholder';
	import { Color } from '@tiptap/extension-color';
	import ListItem from '@tiptap/extension-list-item';
	import TextStyle from '@tiptap/extension-text-style';

	// bubble-menu는 Svelte REPL에서 '프로세스가 정의되지 않음'오류가 발생합니다.
	// 다음 줄의 주석 처리를 제거하면 REPL 문제를 확인할 수 있습니다.
	// import BubbleMenu from '@tiptap/extension-bubble-menu'
	import FixedMenu from '$components/Tiptap/FixedMenu.svelte';

	interface Props {
		content?: string;
	}

	let { content = '' }: Props = $props();

	let editor = $state<Editor | null>(null);
	let element = $state<HTMLElement | null>(null);
	let imageData = $state<string | null>(null);

	function insertImage(event: CustomEvent) {
		imageData = event.detail;
		editor?.chain().focus().setImage({ src: imageData }).run();
	}

	export function getHTML(): string {
		return editor ? editor.getHTML() : '';
	}

	onMount(() => {
		if (element) {
			editor = new Editor({
				element,
				extensions: [
					Underline,
					Color.configure({ types: [TextStyle.name, ListItem.name] }),
					TextStyle.configure({ types: [ListItem.name] }),
					Link.configure({
						protocols: ['https', 'http', 'mailto', 'tel']
					}),
					Image.configure({
						inline: true,
						allowBase64: true
					}),
					Placeholder.configure({
						placeholder: '여기에 내용을 입력해 주세요.',
						emptyEditorClass: 'is-empty'
					}),
					StarterKit
				],
				autofocus: true,
				content: content
			});
		}
	});

	onDestroy(() => {
		if (editor) {
			editor.destroy();
		}
	});

	$effect(() => {
		if (editor && content) {
			editor.commands.setContent(content);
		}
	});
</script>

<div class="w-full h-[600px] mt-4 border border-neutral-600">
	<FixedMenu {editor} on:change={insertImage} />
	
	<div bind:this={element}
			 class="flex-1 w-full max-w-full h-full max-h-[550px] px-2 resize overflow-y-auto focus:outline-none">
	</div>
</div>