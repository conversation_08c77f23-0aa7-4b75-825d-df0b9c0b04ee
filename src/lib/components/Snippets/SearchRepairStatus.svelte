<script lang="ts">
	import { carryoutProductStatuses } from '$stores/carryoutProductStore';

	interface Props {
		repairStatusGroup: string;
		onUpdate: any;
	}
	
	let { repairStatusGroup, onUpdate }: Props = $props();

	function updatePalletStatus(event: Event) {
		const target = event.target as HTMLElement;

		onUpdate({
			detail: {
				repairStatusGroup: target.value
			}
		});
	}
</script>

<div class="w-full flex">
	<div class="flex items-center w-48 p-1 bg-base-content text-base-300">팔레트 상태</div>
	<div class="flex flex-grow items-center pl-5 py-0.5">
		<label class="cursor-pointer pl-2">
			<input bind:group={repairStatusGroup} class="radio-success radio-sm"
						 onclick={updatePalletStatus}
						 type="radio" value=""> 전체
		</label>
		{#each $carryoutProductStatuses as status}
			<label class="cursor-pointer pl-2">
				<input bind:group={repairStatusGroup} class="radio-success radio-sm"
							 onclick={updatePalletStatus}
							 type="radio" value={status.value.toString()}> {status.text}
			</label>
		{/each}
	</div>
</div>