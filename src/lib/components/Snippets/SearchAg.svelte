<script lang="ts">
	interface Props {
		isAg: string;
		onUpdate: any;
	}

	let { isAg, onUpdate }: Props = $props();
	let isChecked = $state(isAg === 'Y');

	async function updateParams() {
		onUpdate({
			detail: {
				isAg: isChecked ? 'Y' : 'N'
			}
		});
	}
</script>

<div class="form-control">
	<label class="label cursor-pointer">
		<input
			bind:checked={isChecked}
			class="checkbox"
			onchange={updateParams}
			type="checkbox"
		/>
		<span class={isChecked ? "font-bold text-red-700" : "text-red-700"}>AG</span>
	</label>
</div>