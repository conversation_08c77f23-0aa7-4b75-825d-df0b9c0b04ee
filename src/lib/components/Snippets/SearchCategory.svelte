<script lang="ts">
	import Icon from 'svelte-awesome';
	import { faArrowRight } from '@fortawesome/free-solid-svg-icons/faArrowRight';

	interface Props {
		category: any;
		cate4Selected: string;
		cate5Selected: string;
		onUpdate: any;
	}

	let { category, cate4Selected, cate5Selected, onUpdate }: Props = $props();

	function updateCate4(event: Event) {
		if (event.target) {
			cate4Selected = event.target.value;
			onUpdate({
				detail: {
					cate4: cate4Selected,
					cate5: ''
				}
			});
		}
	}


	function updateCate5(event: Event) {
		if (event.target) {
			cate5Selected = event.target.value;
			onUpdate({
				detail: {
					cate4: cate4Selected,
					cate5: cate5Selected
				}
			});
		}
	}
</script>

<div class="w-full flex">
	<div class="flex items-center w-48 p-1 bg-base-content text-base-300">카테고리</div>
	<div class="flex flex-grow items-center pl-5 py-0.5">
		<select bind:value={cate4Selected}
						class="select select-bordered select-sm focus:ring-0 focus:outline-none"
						onchange={updateCate4}>
			<option value="">4차 카테고리</option>
			{#each category as item (item.id)}
				<option value={String(item.id)}>{item.name}</option>
			{/each}
		</select>
		
		&nbsp;<Icon data={faArrowRight} scale={2} />&nbsp;
		
		{#if cate4Selected}
			<select bind:value={cate5Selected}
							class="select select-bordered select-sm focus:ring-0 focus:outline-none"
							onchange={updateCate5}
			>
				<option value="">5차 카테고리</option>
				{#each category.find(item => String(item.id) === cate4Selected).cate5 as item}
					<option value={String(item.id)}>{item.name}</option>
				{/each}
			</select>
		{:else}
			<select bind:value={cate5Selected}
							class="select select-bordered select-sm focus:ring-0 focus:outline-none"
			>
				<option value="">5차 카테고리</option>
			</select>
		{/if}
	</div>
</div>