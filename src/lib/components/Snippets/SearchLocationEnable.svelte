<script lang="ts">
	import { locationEnable } from '$stores/locationStore';
	import SearchButton from '$components/Button/Search.svelte';

	interface Props {
		locationEnableGroup: string;
		onUpdate: any;
	}

	let { locationEnableGroup, onUpdate }: Props = $props();

	function updateParams(event: Event) {
		const target = event.target as HTMLElement;
		onUpdate({
			detail: {
				locationEnableGroup: target.value
			}
		});
	}
</script>

<div class="w-full flex">
	<div class="flex items-center w-48 p-1 bg-base-content text-base-300">사용가능 여부</div>
	<div class="flex flex-grow items-center pl-5 py-0.5">
		<label class="cursor-pointer pl-2">
			<input bind:group={locationEnableGroup} class="radio-success radio-sm"
						 onclick={updateParams}
						 type="radio"
						 value="YN"> 전체
		</label>
		{#each $locationEnable as enable}
			<label class="cursor-pointer pl-2">
				<input bind:group={locationEnableGroup} class="radio-success radio-sm"
							 onclick={updateParams}
							 type="radio"
							 value={enable.value}> {enable.text}
			</label>
		{/each}
		
		<SearchButton onclick={updateParams} tooltipData="검색" useTooltip={true} />
	</div>
</div>