<script lang="ts">
	import SearchButton from '$components/Button/Search.svelte';

	import Icon from 'svelte-awesome';
	import { faXmark } from '@fortawesome/free-solid-svg-icons/faXmark';

	interface Props {
		searchType?: string;
		keyword: string;
		onUpdate: any;
		children?: import('svelte').Snippet;
	}

	let { searchType, keyword, onUpdate, children }: Props = $props();

	async function updateParams() {
		if (keyword) {
			onUpdate({
				detail: {
					searchType: searchType,
					keyword: keyword
				}
			});
		} else {
			alert('검색어를 입력해 주세요.');
		}
	}
</script>

<div class="w-full flex">
	<div class="flex items-center w-48 p-1 bg-base-content text-base-300">검색 항목</div>
	<div class="flex flex-grow items-center pl-5 py-0.5">
		<select bind:value={searchType}
						class="select select-bordered select-sm py-1 px-8 focus:ring-0 focus:outline-none"
		>
			<option value="qaid">QAID/바코드/상품명</option>
			<option value="token">수리키</option>
			<option value="memo">수리내용(메모)</option>
		</select>
		
		<label class="input input-bordered input-sm flex items-center justify-center gap-2 mx-2">
			<input bind:value={keyword}
						 class="grow bg-base-100"
						 onkeydown={e => {if (e.key === "Enter") { updateParams() }}}
						 type="text"
			/>
			
			<span onclick={() => {keyword = ''}} role="presentation">
				<Icon class="cursor-pointer" data={faXmark} />
			</span>
		</label>
		
		<SearchButton onclick={updateParams} tooltipData="검색" useTooltip={true} />
		
		{@render children?.()}
	</div>
</div>