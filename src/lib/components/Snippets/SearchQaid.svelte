<script lang="ts">
	import { onMount } from 'svelte';
	
	import SearchButton from '$components/Button/Search.svelte';

	interface Props {
		option1?: string;
		option2?: string;
		searchType?: string;
		keyword: string;
		onUpdate: any;
		children?: import('svelte').Snippet;
	}

	let {
		option1 = 'QAID/바코드/로트번호',
		option2 = '작성자',
		searchType = $bindable('qaid'),
		keyword = $bindable(),
		onUpdate,
		children
	}: Props = $props();
	
	let keywordElement: HTMLElement;

	async function updateParams() {
		onUpdate({
			detail: {
				searchType: searchType,
				keyword: keyword
			}
		});

		keyword = '';
		keywordElement.focus();
	}
	
	onMount(() => {
		keywordElement.focus();
	})
</script>

<div class="w-full flex">
	<div class="flex items-center w-48 p-1 bg-base-content text-base-300">검색 항목</div>
	<div class="flex flex-grow items-center pl-5 py-0.5">
		<select bind:value={searchType}
						class="select select-bordered select-sm focus:ring-0 focus:outline-none"
		>
			<option value="qaid">{option1}</option>
			<option value="user">{option2}</option>
		</select>
		
		<label class="input input-bordered input-sm flex items-center justify-center gap-2 mx-2">
			<input bind:value={keyword}
						 bind:this={keywordElement}
						 class="grow bg-base-100"
						 onkeydown={e => {if (e.key === "Enter") { updateParams() }}}
						 type="text"
			/>
		</label>
		
		<SearchButton onclick={updateParams} tooltipData="검색" useTooltip={true} />
		
		{@render children?.()}
	</div>
</div>