<script lang="ts">
	import { onMount, tick } from 'svelte';
	import JsBarcode from 'jsbarcode';

	interface Props {
		id?: string;
		value?: string;
		options?: object;
	}

	let { id = 'barcode1', value = 'cornerstone-project', options = {} }: Props = $props();

	const defaultOptions = {
		format: 'CODE128',
		width: 0.7,
		height: 20,
		displayValue: true,
		text: undefined,
		fontOptions: '',
		font: 'Roboto',
		textAlign: 'center',
		textPosition: 'bottom',
		textMargin: 2,
		fontSize: 10,
		background: '#ffffff',
		lineColor: '#000000',
		margin: 2,
		marginTop: undefined,
		marginBottom: undefined,
		marginLeft: undefined,
		marginRight: undefined,
		flat: true
	};

	onMount(async () => {
		await tick();
		JsBarcode(`#${id}`, value, {...defaultOptions, ...options});
	});
</script>

<svg id={id}></svg>