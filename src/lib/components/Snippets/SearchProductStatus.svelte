<script lang="ts">
	import { productStatuses } from '$stores/productStore';

	interface Props {
		productStatusGroup: string;
		onUpdate: any;
	}

	let { productStatusGroup, onUpdate }: Props = $props();

	function updateParams(event: Event) {
		const target = event.target as HTMLElement;
		onUpdate({
			detail: {
				productStatusGroup: target.value
			}
		});
	}
</script>

<div class="w-full flex">
	<div class="flex items-center w-48 p-1 bg-base-content text-base-300">입고처리 상태</div>
	<div class="flex flex-grow items-center pl-5 py-0.5">
		<label class="cursor-pointer pl-2">
			<input bind:group={productStatusGroup} checked class="radio-success radio-sm" onclick={updateParams}
						 type="radio" value=""> 전체
		</label>
		{#each $productStatuses as status}
			<label class="cursor-pointer pl-2">
				<input bind:group={productStatusGroup} class="radio-success radio-sm"
							 onclick={updateParams}
							 type="radio"
							 value={status.value.toString()}> {status.text}
			</label>
		{/each}
	</div>
</div>