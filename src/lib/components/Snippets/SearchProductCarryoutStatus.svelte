<script lang="ts">
	interface Props {
		carryoutStatusGroup: string;
		onUpdate: any;
	}

	let { carryoutStatusGroup, onUpdate }: Props = $props();

	function updateCheckedStatus(event: Event) {
		const target = event.target as HTMLElement;

		onUpdate({
			detail: {
				carryoutStatusGroup: target.value
			}
		});
	}
</script>

<div class="w-full flex">
	<div class="flex items-center w-48 p-1 bg-base-content text-base-300">상품 상태</div>
	<div class="flex flex-grow items-center pl-5 py-0.5">
		<label class="cursor-pointer pl-2">
			<input bind:group={carryoutStatusGroup} class="radio-success radio-sm"
						 onchange={updateCheckedStatus}
						 type="radio" value="N" /> 반출
		</label>
		
		<label class="cursor-pointer pl-2">
			<input bind:group={carryoutStatusGroup} class="radio-success radio-sm"
						 onchange={updateCheckedStatus}
						 type="radio" value="Y" /> 반입
		</label>
	</div>
</div>