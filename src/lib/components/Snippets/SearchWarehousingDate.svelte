<script lang="ts">
	interface Props {
		title?: string;
		beginAt: string;
		endAt: string;
		onUpdate: any;
		children?: import('svelte').Snippet;
	}

	let {
		title = '입고날짜',
		beginAt,
		endAt,
		onUpdate,
		children
	}: Props = $props();

	// 날짜 업데이트 함수
	function updateDate(event: Event) {
		try {
			const beginDate = new Date(beginAt || '');
			const endDate = new Date(endAt || '');

			// 날짜가 유효한지 확인
			if (isNaN(beginDate.getTime()) || isNaN(endDate.getTime())) {
				return;
			}

			// 작은 값은 beginAt, 큰 값은 endAt으로 설정
			const formattedBeginDate = beginDate.toISOString().split('T')[0];
			const formattedEndDate = endDate.toISOString().split('T')[0];

			if (beginDate <= endDate) {
				onUpdate({
					detail: {
						beginAt: formattedBeginDate,
						endAt: formattedEndDate,
					}
				});
			} else {
				onUpdate({
					detail: {
						beginAt: formattedEndDate,
						endAt: formattedBeginDate,
					}
				});
			}
		} catch (error) {
			console.error('날짜 처리 중 오류가 발생했습니다:', error);
		}
	}
</script>

<div class="w-full flex">
	<div class="flex items-center w-48 p-1 bg-base-content text-base-300">
		{title}
	</div>
	<div class="flex flex-grow-0 items-center pl-5">
		<div class="flex items-center">
			<input bind:value={beginAt}
						 class="input input-bordered input-sm w-36 focus:ring-0 focus:outline-none"
						 name="beginAt"
						 onchange={updateDate}
						 type="date"
			>
			
			<span class="mx-2 font-bold text-xl"> ~ </span>
			
			<input bind:value={endAt}
						 class="input input-bordered input-sm w-36 focus:ring-0 focus:outline-none"
						 name="endAt"
						 onchange={updateDate}
						 type="date"
			>
			
			<!-- 검색 버튼을 추가할 수 있는 공간 -->
			{@render children?.()}
		</div>
	</div>
</div>
