<script lang="ts">
	import { palletProductCheckedStatuses } from '$stores/palletProductStore';

	interface Props {
		checkedStatusGroup: string;
		onUpdate: any;
	}

	let { checkedStatusGroup, onUpdate }: Props = $props();

	function updatePalletStatus(event: Event) {
		const target = event.target as HTMLElement;

		onUpdate({
			detail: {
				checkedStatusGroup: target.value
			}
		});
	}
</script>

<div class="w-full flex">
	<div class="flex items-center w-48 p-1 bg-base-content text-base-300">출고 검수 여부</div>
	<div class="flex flex-grow items-center pl-5 py-0.5">
		<label class="cursor-pointer pl-2">
			<input bind:group={checkedStatusGroup} class="radio-success radio-sm"
						 onclick={updatePalletStatus}
						 type="radio" value=""> 전체
		</label>
		{#each $palletProductCheckedStatuses as status}
			<label class="cursor-pointer pl-2">
				<input bind:group={checkedStatusGroup} class="radio-success radio-sm"
							 onclick={updatePalletStatus}
							 type="radio" value={status.value.toString()}> {status.text}
			</label>
		{/each}
	</div>
</div>