<script lang="ts">
	interface Props {
		isRg: string;
		onUpdate: any;
	}

	let { isRg, onUpdate }: Props = $props();
	let isChecked = $state(isRg === 'Y');

	async function updateParams() {
		onUpdate({
			detail: {
				isRg: isChecked ? 'Y' : 'N'
			}
		});
	}
</script>

<div class="form-control pl-5">
	<label class="label cursor-pointer">
		<input
			bind:checked={isChecked}
			class="checkbox"
			onchange={updateParams}
			type="checkbox"
		/>
		<span class={isChecked ? "font-bold" : ""}>RG</span>
	</label>
</div>