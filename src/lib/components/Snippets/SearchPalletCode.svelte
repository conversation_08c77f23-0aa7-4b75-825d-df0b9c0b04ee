<script lang="ts">
	import SearchButton from '$components/Button/Search.svelte';

	import Icon from 'svelte-awesome';
	import { faXmark } from '@fortawesome/free-solid-svg-icons/faXmark';

	interface Props {
		keyword: string;
		children?: import('svelte').Snippet;
		onUpdate: any;
	}

	let { keyword, onUpdate, children }: Props = $props();

	async function updateParams() {
		onUpdate({
			detail: {
				keyword
			}
		});
	}
</script>

<div class="w-full flex">
	<div class="flex items-center w-48 p-1 bg-base-content text-base-300">팔레트 번호</div>
	<div class="flex flex-grow items-center pl-5 py-0.5">
		<label class="input input-bordered flex items-center justify-center gap-2 mx-2">
			<input bind:value={keyword}
						 class="grow bg-base-100"
						 onkeydown={e => {if (e.key === "Enter") { updateParams() }}}
						 type="text"
			/>
			
			<span onclick={() => {keyword = ''}} role="presentation">
				<Icon class="cursor-pointer" data={faXmark} />
			</span>
		</label>
		
		<SearchButton onclick={updateParams} tooltipData="검색" tooltipDirection="tooltip-bottom" useTooltip={true} />
		
		{@render children?.()}
	</div>
</div>