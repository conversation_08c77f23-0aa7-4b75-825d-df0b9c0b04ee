<script lang="ts">
	import { processGrade } from '$stores/processStore';

	interface Props {
		processGradeGroup: string;
		onUpdate: any;
	}

	let { processGradeGroup, onUpdate }: Props = $props();

	function updateProcessGrade(event: Event) {
		const target = event.target as HTMLElement;
		onUpdate({
			detail: {
				processGradeGroup: target.value
			}
		});
	}
</script>

<div class="w-full flex">
	<div class="flex items-center w-48 p-1 bg-base-content text-base-300">점검(수리) 상태</div>
	<div class="flex flex-grow items-center pl-5 py-0.5">
		<label class="cursor-pointer pl-2">
			<input bind:group={processGradeGroup} checked class="radio-success radio-sm"
						 onclick={updateProcessGrade}
						 type="radio" value=""> 전체
		</label>
		{#each $processGrade as grade}
			<label class="cursor-pointer pl-2">
				<input bind:group={processGradeGroup} class="radio-success radio-sm"
							 onclick={updateProcessGrade}
							 type="radio" value={grade.value}> {grade.text}
			</label>
		{/each}
	</div>
</div>