<script lang="ts">
interface Props {
	display_keyword1: string;
	display_keyword2?: string;
}

let {
	display_keyword1,
	display_keyword2
}: Props = $props();
</script>

<div class="w-full flex">
	<div class="flex flex-grow items-center pl-5 text-xl font-bold">
		검색어 표시:
		{#if display_keyword1 || display_keyword1 === ''}
			{display_keyword1}
		{/if}
		
		{#if display_keyword2 || display_keyword2 === ''}
			{display_keyword2}
		{/if}
	</div>
</div>