<script lang="ts">
	import { productCheckedStatuses } from '$stores/productStore';

	interface Props {
		checkedStatusGroup: string;
		onUpdate: any;
		children?: import('svelte').Snippet;
	}

	let { checkedStatusGroup, onUpdate, children }: Props = $props();

	function updateCheckedStatus(event: Event) {
		const target = event.target as HTMLElement;
		onUpdate({
			detail: {
				checkedStatusGroup: target.value
			}
		});
	}
</script>

<div class="w-full flex">
	<div class="flex items-center w-48 p-1 bg-base-content text-base-300">입고검수 여부</div>
	<div class="flex flex-grow-0 items-center pl-5 py-0.5">
		<label class="cursor-pointer pl-2">
			<input bind:group={checkedStatusGroup} checked class="radio-success radio-sm"
						 onchange={updateCheckedStatus}
						 type="radio" value="" /> 전체
		</label>
		{#each $productCheckedStatuses as status}
			<label class="cursor-pointer pl-2">
				<input bind:group={checkedStatusGroup} class="radio-success radio-sm"
							 onchange={updateCheckedStatus}
							 type="radio" value={status.value.toString()} /> {status.text}
			</label>
		{/each}
	</div>
	<div class="flex flex-grow items-center pl-5 py-0.5">
		{@render children?.()}
	</div>
</div>