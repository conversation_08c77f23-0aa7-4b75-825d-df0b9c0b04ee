// See https://kit.svelte.dev/docs/types#app
// for information about these interfaces
declare global {
	namespace App {
		// interface Error {}
		// interface Locals {}
		// interface PageData {}
		// interface Platform {}
	}

	interface User {
		id: number;
		company_id: string;
		role: string;
		username: string;
		name: string;
		email: string;
		cellphone: string;
		status: number;
		login_at: Date | null;
		login_ip: string | null;
		login_os: string | null;
		created_at: Date;
		updated_at: Date;
	}

	// drag and drop
	type Item = import("svelte-dnd-action").Item;
	type DndEvent<ItemType = Item> = import("svelte-dnd-action").DndEvent<ItemType>;
	namespace svelteHTML {
		interface HTMLAttributes<T> {
			"on:consider"?: (event: CustomEvent<DndEvent<ItemType>> & {target: EventTarget & T}) => void;
			"on:finalize"?: (event: CustomEvent<DndEvent<ItemType>> & {target: EventTarget & T}) => void;
		}
	}
}

export {};
