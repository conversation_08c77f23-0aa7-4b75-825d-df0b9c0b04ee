<script lang="ts">
	import { getUser, type User } from '$lib/User';
	import { onMount } from 'svelte';
	import { page } from '$app/state';
	import { WebviewWindow } from '@tauri-apps/api/webviewWindow';

	import {
		executeMessage,
		formatDateTimeToFullString,
		formatDateTimeToString,
		generateRandomNumber,
		getNumberFormat
	} from '$lib/Functions';

	import { getProductCheckedStatusName, loadProduct, productStore } from '$stores/productStore';
	import { getProcessGradeColorButton } from '$stores/processStore';

	import GuestLayout from '$components/Layouts/GuestLayout.svelte';
	import Loading from '$components/Loading/Circle2.svelte';
	import TitleBar from '$components/UI/TitleBar.svelte';
	import SearchUI from '$components/UI/SearchUI.svelte';
	import TableTop from '$components/UI/TableTop.svelte';
	import Paginate from '$components/UI/Paginate.svelte';
	import Barcode from '$components/Snippets/Barcode.svelte';
	import SearchButton from '$components/Button/Search.svelte';

	import Icon from 'svelte-awesome';
	import { faFilter } from '@fortawesome/free-solid-svg-icons/faFilter';
	import { faCopyright } from '@fortawesome/free-regular-svg-icons/faCopyright';
	import { faRocket } from '@fortawesome/free-solid-svg-icons/faRocket';
	import { faXmark } from '@fortawesome/free-solid-svg-icons/faXmark';
	import type { Breadcrumb } from '$lib/types';

	let user: User = getUser();
	const apiUrl = '/wms/products/search';
	const localUrl = page.url.pathname;
	let isLoading = $state(false); // 로딩중 표시 여부

	// 검색 query string 시작 ==========
	let keyword = $state(''); // 검색어
	let p = $state(page.url.searchParams.get('p') || '1');
	let pageSize = $state(page.url.searchParams.get('pageSize') || '15');
	let searchParams = $state('');
	let apiSearchParams = '';
	// 검색 query string 종료 ==========

	// 페이징 관련 변수 시작 =============
	let links: [] = $state([]);
	let total = $state(0);
	let startNo = $state(0);
	// 페이징 관련 변수 종료 =============

	// =============== 페이지 내 필요한 변수들 시작 ===============
	let focusInput: HTMLElement = $state(); // 페이지 로딩시 입고검수 input에 커서 포커싱
	// =============== 페이지 내 필요한 변수들 종료 ===============

	/**
	 * API로 부터 데이터를 가져온다.
	 */
	async function makeData() {
		if (!keyword) {
			await executeMessage('검색어를 입력해 주세요.', 'warning');
			return false;
		}

		isLoading = true;

		const common_params = {
			searchType: 'qaid', // 외부업체(guest) 일 경우 qaid 로 변경할 것
			keyword: keyword,
			pageSize: pageSize
		};

		const params = new URLSearchParams({ p: p, ...common_params });
		const api_params = new URLSearchParams({ page: p, ...common_params });

		searchParams = params.toString();
		apiSearchParams = api_params.toString();

		await loadProduct(`${apiUrl}?${apiSearchParams}`, user);

		if ($productStore) {
			links = JSON.parse($productStore.pageLinks);
			total = $productStore.pageTotal ?? 0;
			startNo = $productStore.pageTotal - $productStore.pageFrom + 1;
		}

		isLoading = false;
		focusInput.focus();
	}

	/**
	 * 자식 컴포넌트에서 변경된 변수의 값을 매칭
	 *
	 * @param value
	 */
	function changeSearchParams(value) {
		if (value.detail.p) p = value.detail.p;
		if (value.detail.keyword || value.detail.keyword === '') keyword = value.detail.keyword;
		if (value.detail.pageSize) pageSize = value.detail.pageSize;

		makeData();
	}

	function clearSearchBox() {
		keyword = '';
		focusInput.focus();
	}

	function openCoupang(id: number, url: string) {
		let windowLabel = 'coupang-' + id;

		new WebviewWindow(windowLabel, {
			url: url,
			width: 1280,
			height: 1024
		});
	}

	onMount(async () => {
		productStore.set('');

		focusInput.focus();
	});

	const breadcrumbs: Breadcrumb[] = [
		{ title: '검색', url: '/guest/qaid-search' },
		{ title: 'QAID 검색', url: '/guest/qaid-search' }
	];
</script>

<svelte:head>
	<title>검색 > QAID 검색</title>
</svelte:head>

{#if isLoading}
	<Loading size="60" unit="px" />
{/if}

<GuestLayout {user}>
	{#snippet main()}
		<div>
			<TitleBar {breadcrumbs} />

			<section class="main-section">
				<SearchUI>
					<div class="w-full flex">
						<div class="flex items-center w-72 p-1 bg-base-content text-base-300">
							<Icon data={faFilter} />
							<span class="ml-2">QAID</span>
						</div>
						<div class="flex flex-grow items-center pl-5 p-3">
							<label class="input input-bordered flex items-center justify-center gap-2">
								<input
									bind:this={focusInput}
									bind:value={keyword}
									class="grow bg-base-100"
									onkeydown={(e) => {
										if (e.key === 'Enter') {
											makeData();
										}
									}}
									type="text"
								/>

								<span onclick={clearSearchBox} role="presentation">
									<Icon class="cursor-pointer" data={faXmark} />
								</span>
							</label>

							<SearchButton onclick={makeData} tooltipData="검색" useTooltip={true} />
						</div>
					</div>
				</SearchUI>

				<!-- 리스트 시작 -->
				<div class="overflow-x-auto px-2">
					<TableTop onUpdate={changeSearchParams} {pageSize} {total} />

					<table class="table text-xs table-pin-rows">
						<thead class="uppercase">
							<tr class="bg-base-content text-base-300 text-center">
								<th rowspan="2">번호</th>
								<th rowspan="2">입고날짜</th>
								<th rowspan="2">로트번호</th>
								<th colspan="2">카테고리</th>
								<th rowspan="2">QAID</th>
								<th rowspan="2">바코드</th>
								<th rowspan="2">상품명</th>
								<th rowspan="2">중복여부</th>
								<th rowspan="2">단가</th>
								<th rowspan="2">검수상태</th>
								<th rowspan="2">검수일자</th>
								<th rowspan="2">점검(수리)상태</th>
								<th rowspan="2">점검(수리)일자</th>
								<th rowspan="2">출고일자</th>
							</tr>
							<tr class="bg-base-content text-base-300 text-center">
								<th class="text-center">4차</th>
								<th class="text-center">5차</th>
							</tr>
						</thead>

						<tbody>
							{#if $productStore.products}
								{#each $productStore.products as item, index}
									<tr class="hover:bg-base-content/10">
										<td class="w-[50px] p-1 text-center">
											{getNumberFormat(startNo - index)}
										</td>
										<td class="w-[90px] p-1 text-center">
											{item.req.req_at}
										</td>
										<td class="w-[150px] p-1">
											{item.lot.name}
										</td>
										<td class="w-[100px] p-1">
											{item.cate4.name}
										</td>
										<td class="w-[100px] p-1">
											{#if item.cate5}
												{item.cate5.name}
											{/if}
										</td>
										<td class="w-[100px] p-1 text-center cursor-pointer">
											<div class="flex items-center justify-center">
												<Barcode id="barcode1" value={item.qaid} />
												{#if item.rg === 'Y'}
													<Icon data={faRocket} class="ml-1 text-red-700" />
												{/if}
											</div>
										</td>
										<td class="w-[100px] p-1 text-center">
											<Barcode id="barcode2" value={item.barcode.barcode} />
										</td>
										<td class="p-1">
											{#if item.link.product_id && item.link.product_id.slice(-8) !== '00000000'}
												<a
													href="https://www.coupang.com/"
													onclick={(e) => {
														e.preventDefault();

														const id = generateRandomNumber();
														openCoupang(
															id,
															`https://www.coupang.com/vp/products/${item.link.product_id}?itemId=${item.link.item_id}&vendorItemId=${item.link.vendor_item_id}`
														);
													}}
												>
													{item.barcode.name}
													<Icon data={faCopyright} class="text-red-700" />
												</a>
											{:else}
												{item.barcode.name}
											{/if}
										</td>
										<td class="w-[50px] p-1 text-center">
											{#if item.duplicated === 'N'}
												-
											{:else}
												중복
											{/if}
										</td>
										<td class="w-[70px] p-1 text-right">
											{getNumberFormat(item.amount)}
										</td>
										<td class="w-[100px] p-1 text-center">
											{getProductCheckedStatusName(item.checked_status)}
										</td>
										<td class="w-[90px] p-1 text-center">
											{#if item.checked_at}
												{formatDateTimeToFullString(item.checked_at)}
											{:else}
												-
											{/if}
										</td>
										<td class="w-[50px] p-1 text-center">
											{#if item.pallet_products.length > 0}
												{@html getProcessGradeColorButton(item.pallet_products[0].process_grade)}
											{/if}
										</td>
										<td class="w-[90px] p-1 text-center">
											{#if item.pallet_products.length > 0}
												{formatDateTimeToFullString(item.pallet_products[0].registered_at)}
											{:else}
												-
											{/if}
										</td>
										<td class="w-[90px] p-1 text-center">
											{#if item.pallet_products.length > 0}
												<p>{formatDateTimeToString(item.pallet_products[0].pallet.exported_at)}</p>
											{:else}
												-
											{/if}
										</td>
									</tr>
								{/each}
							{:else}
								<tr>
									<td colspan="14" class="text-center"> 검색 결과가 없습니다. </td>
								</tr>
							{/if}
						</tbody>
					</table>
				</div>

				<!-- Pagination -->
				<Paginate
					{links}
					{localUrl}
					onUpdate={changeSearchParams}
					pageCurrentPage={$productStore.pageCurrentPage}
					pageNextPageUrl={$productStore.pageNextPageUrl}
					pagePrevPageUrl={$productStore.pagePrevPageUrl}
					{searchParams}
				/>
			</section>
		</div>
	{/snippet}
</GuestLayout>
