<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';

	import { executeMessage } from '$lib/Functions';
	import { getUser, type User } from '$lib/User';
	interface Props {
		children?: import('svelte').Snippet;
	}

	let { children }: Props = $props();

	let user: boolean | User = false;

	onMount(async () => {
		try {
			user = getUser();
		} catch (error) {
			await executeMessage('회원정보 변환 실패', 'warning');

			await goto('/login');
		}
	});
</script>

<div>
	{@render children?.()}
</div>