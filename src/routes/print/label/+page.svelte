<script lang="ts">
	import { onDestroy, onMount } from 'svelte';
	import { browser } from '$app/environment';
	import { page } from '$app/state';

	import { getCurrentWindow } from '@tauri-apps/api/window';
	import { getProcessGradeName } from '$stores/processStore';

	import Layout from '$components/Layouts/PrintLayout.svelte';
	import Barcode from '$components/Snippets/Barcode.svelte';

	let mounted = $state(false);
	let isPrinting = false;

	const queryParams = page.url.searchParams;

	let grade_code = queryParams.get('grade_code');
	let pallet_code_list = queryParams.get('pallet_code_list');
	let grade_name = $state(queryParams.get('grade_name'));
	let level = queryParams.get('level');
	let column = queryParams.get('column');
	let export_date = $state(queryParams.get('export_date'));

	if (export_date) {
		export_date = export_date.substring(2);
	} else {
		export_date = '';
	}

	if (!grade_name && grade_code) {
		grade_name = getProcessGradeName(grade_code);
	}

	if (!grade_name || grade_name === 'Unknown') {
		grade_name = '';
	}

	let pallet_code_arr: any[] = $state([]);
	let pallet_code;
	if (level === null && column === null && pallet_code_list !== null) {
		pallet_code_arr = pallet_code_list.split('|');
	} else {
		pallet_code = `${level}-${column}`;
		pallet_code_arr.push(pallet_code);
	}

	const options = {
		format: 'CODE128',
		width: 2,
		height: 100,
		displayValue: true,
		text: undefined,
		fontOptions: '',
		font: 'sans-serif',
		textAlign: 'center',
		textPosition: 'bottom',
		textMargin: 2,
		fontSize: 20,
		background: '#ffffff',
		lineColor: '#000000',
		margin: 10,
		marginTop: undefined,
		marginBottom: undefined,
		marginLeft: undefined,
		marginRight: undefined,
		flat: true
	};

	function handleBeforePrint() {
		isPrinting = true;
	}

	function handleAfterPrint() {
		isPrinting = false;
		closeWindow();
	}

	async function closeWindow() {
		if (!isPrinting) {
			setTimeout(async () => {
				try {
					const currentWindow = getCurrentWindow();
					await currentWindow.close();
				} catch (error) {
					console.error('Error closing window:', error);
				}
			}, 2000); // 1초 후에 창 닫기
		}
	}

	onMount(() => {
		mounted = true;

		if (browser) {
			setTimeout(() => {
				window.print();
			}, 500);
		}

		window.addEventListener('beforeprint', handleBeforePrint);
		window.addEventListener('afterprint', handleAfterPrint);
	});

	onDestroy(() => {
		window.removeEventListener('beforeprint', handleBeforePrint);
		window.removeEventListener('afterprint', handleAfterPrint);
	});
</script>

{#if mounted}
	<style>
		@media print {
			@page {
				size: 297mm 210mm;
				margin: 0;
			}

			body {
				width: 297mm;
				height: 210mm;
			}

			.printable {
				width: 100%;
				height: 100%;
			}

			.printable * {
				visibility: visible;
			}

			body * {
				visibility: hidden;
			}

			.barcode-container {
				display: flex;
				justify-content: center;
				align-items: center;
				width: 100%;
			}
		}
	</style>

	<Layout>
		<div class="printable" style="font-size: 50px; width: 100%; height: 100%; text-align: center;">
			{#each pallet_code_arr as pallet_code, index}
				<div
					style="width: 100%; display: flex; flex-direction: column; justify-content: space-between;"
				>
					<div style="width: 100%; font-size:130pt; height: 350px; line-height: 350px;">
						{pallet_code}
					</div>
					<div class="flex w-full justify-center items-center">
						<Barcode id="barcode1" value={pallet_code} {options} />
					</div>
					<div
						style="text-align: center; font-size: 90pt; height: 130px; width: 100%; display: flex; justify-content: center; color: #ec6040;"
					>
						<div style="width: 50%;">{export_date}</div>
						<div style="width: 45%;">[{grade_name}]</div>
					</div>
				</div>

				{#if index < pallet_code_arr.length - 1}
					<div style="page-break-after: always;"></div>
				{/if}
			{/each}
		</div>
	</Layout>
{/if}
