<script lang="ts">
	import { onDestroy, onMount } from 'svelte';
	import { browser } from '$app/environment';
	import { page } from '$app/state';

	import { getCurrentWindow } from '@tauri-apps/api/window';
	import { getProcessGradeName } from '$stores/processStore';

	import Layout from '$components/Layouts/PrintLayout.svelte';
	import Barcode from '$components/Snippets/Barcode.svelte';

	let mounted = $state(false);
	let isPrinting = false;

	const queryParams = page.url.searchParams;
	
	let pallet_number_list = $state(queryParams.get('pallet_number_list') ?? '');
	let pallet_code_arr = pallet_number_list.split('|');
	let import_date = $state(queryParams.get('import_date') ?? '');

	if (import_date) {
		import_date = import_date.substring(2);
	}

	const options = {
		format: 'CODE128',
		width: 2,
		height: 100,
		displayValue: true,
		text: undefined,
		fontOptions: '',
		font: 'sans-serif',
		textAlign: 'center',
		textPosition: 'bottom',
		textMargin: 2,
		fontSize: 20,
		background: '#ffffff',
		lineColor: '#000000',
		margin: 10,
		marginTop: undefined,
		marginBottom: undefined,
		marginLeft: undefined,
		marginRight: undefined,
		flat: true
	};

	function handleBeforePrint() {
		isPrinting = true;
	}

	function handleAfterPrint() {
		isPrinting = false;
		closeWindow();
	}

	async function closeWindow() {
		if (!isPrinting) {
			setTimeout(async () => {
				try {
					const currentWindow = getCurrentWindow();
					await currentWindow.close();
				} catch (error) {
					console.error('Error closing window:', error);
				}
			}, 2000); // 1초 후에 창 닫기
		}
	}

	onMount(() => {
		mounted = true;

		if (browser) {
			setTimeout(() => {
				window.print();
			}, 500);
		}

		window.addEventListener('beforeprint', handleBeforePrint);
		window.addEventListener('afterprint', handleAfterPrint);
	});

	onDestroy(() => {
		window.removeEventListener('beforeprint', handleBeforePrint);
		window.removeEventListener('afterprint', handleAfterPrint);
	});
</script>

{#if mounted}
	<style>
      @media print {
          @page {
              size: 297mm 210mm;
              margin: 0;
          }

          body {
              width: 297mm;
              height: 210mm;
          }

          .printable {
              width: 100%;
              height: 100%;
          }

          .printable * {
              visibility: visible;
          }

          body * {
              visibility: hidden;
          }

          .barcode-container {
              display: flex;
              justify-content: center;
              align-items: center;
              width: 100%;
          }
      }
	</style>
	
	<Layout>
		<div class="printable" style="font-size: 50px; width: 100%; height: 100%; text-align: center;">
			{#each pallet_code_arr as pallet_code, index}
				<div style="font-size: 40px; width: 99%; height: 40px; display: block; clear: both; margin-bottom: 50px;">
					<div style="float: right;">
						<Barcode id="barcode_{index}" value={pallet_code} {options} />
					</div>
				</div>
				
				<div style="text-align: center; font-size:130pt; width: 100%; height: 300px; line-height: 250px; display: block; clear: both;">
					{pallet_code}
				</div>
				
				<div style="text-align: center; font-size: 110pt; width: 100%; height: 200px; line-height: 50px; display: block; clear: both; color: #ec6040;">
					{import_date}
				</div>
				
				{#if index < pallet_code_arr.length - 1}
					<div style="page-break-after: always;"></div>
				{/if}
			{/each}
		</div>
	</Layout>
{/if}
