<!-- @migration-task Error while migrating Svelte code: `<input>` is invalid inside `<tr>` -->
<script lang="ts">
	import { onMount } from 'svelte';
	import { getUser, type User } from '$lib/User';
	import { page } from '$app/state';

	import { authClient } from '$lib/AxiosBackend';
	import { toast } from 'svoast';

	import {
		executeAsk,
		executeMessage,
		formatDateTimeToString,
		getNumberFormat,
		getPayload,
		handleCatch,
		handleDownload
	} from '$lib/Functions';
	import { getPalletStatusName, PALLET_STATUS_CLOSED } from '$stores/palletStore';
	import { getProcessGradeColorButton } from '$stores/processStore';
	import {
		getPalletProductCheckedStatusName,
		loadItems,
		palletInfoStore,
		palletProductStore
	} from '$stores/palletProductStore';

	import AppLayout from '$components/Layouts/AppLayout.svelte';
	import SearchUI from '$components/UI/SearchUI.svelte';
	import Loading from '$components/Loading/Circle2.svelte';
	import SearchQaid from '$components/Snippets/SearchQaid.svelte';
	import SearchPalletInspection from '$components/Snippets/SearchPalletInspection.svelte';
	import ButtonExcelDownload from '$components/Button/ExcelDownload.svelte';

	import Icon from 'svelte-awesome';
	import { faPrint } from '@fortawesome/free-solid-svg-icons/faPrint';
	import { faDeleteLeft } from '@fortawesome/free-solid-svg-icons/faDeleteLeft';
	import { faCheckDouble } from '@fortawesome/free-solid-svg-icons/faCheckDouble';
	import { faBackspace } from '@fortawesome/free-solid-svg-icons/faBackspace';
	import { faLock } from '@fortawesome/free-solid-svg-icons/faLock';
	import { faUnlock } from '@fortawesome/free-solid-svg-icons/faUnlock';
	import { faRocket } from '@fortawesome/free-solid-svg-icons/faRocket';
	import { WebviewWindow } from '@tauri-apps/api/webviewWindow';
	import { faXmark } from '@fortawesome/free-solid-svg-icons/faXmark';
	import { faFolder } from '@fortawesome/free-regular-svg-icons/faFolder';

	let user: User = getUser();
	const apiUrl = `/wms/pallets/products`;
	const localUrl = page.url.pathname;
	let isLoading = $state(false); // 로딩중 표시 여부

	// 검색 query string 시작 ==========
	let palletId = $state(page.url.searchParams.get('id') ?? '');
	let checkedStatus = $state(page.url.searchParams.get('checkedStatus') ?? '');
	let prevPalletStatus = $state(page.url.searchParams.get('palletStatus') ?? '');
	let searchType = $state('qaid');
	let keyword = $state(''); // 검색어
	let p = $state(page.url.searchParams.get('p') || '1');
	let pageSize = $state(page.url.searchParams.get('pageSize') || '1000');
	let searchParams = $state('');
	let apiSearchParams = $state('');
	// 검색 query string 종료 ==========

	// 페이징 관련 변수 시작 =============
	let links: [] = $state([]);
	let total = $state(0);
	let startNo = $state(0);
	// 페이징 관련 변수 종료 =============

	// =============== 페이지 내 필요한 변수들 시작 ===============
	let allChecked = $state(false); // 전체 선택: 체크박스
	let idChecked: [] = $state([]); // 전체 선택: 모든 체크박스의 상태를 참조하는 reactive 변수
	let ids: [] = $state([]); // 전체 선택: 상품의 id를 모으는 변수

	let pageLocationCountry = $state('');
	let pageLocationCity = $state('');
	let pageLocationStore = $state('');
	let pageLocationLine = $state('');
	let pageLocationRack = $state('');
	let pageLocationLevel = $state('');
	let pageLocationColumn = $state('');
	let pageLocationName = $state('');
	let pagePalletCode = $state('');
	let pagePalletStatus = $state(0);
	let pagePalletStatusName = $state('');
	let pageExportDate = $state('');
	let pagePalletGradeName = $state('');
	let pagePalletRegisteredUserName = $state('');
	let pagePalletCheckoutAt = $state('');
	let pageTotalQuantity = $state(0);
	let pageTotalAmount = $state(0);
	let pageTotalInvoice1 = $state(0);
	let pageTotalInvoice2 = $state(0);
	let pageTotalInvoice3 = $state(0);
	let pageInvoiceTotal = $state(0);

	let focusInput: HTMLElement;
	let checkQaid = $state(''); // 검수할 QAID
	let checkboxes: [] = $state([]); // 선택된 항목 찾기
	let productIds: number[] = $state([]); // 점검 취소할 때 상품 id를 담는 배열

	let showProductName = $state('출고검수를 하면 상품 이름이 나옵니다.');
	// =============== 페이지 내 필요한 변수들 종료 ===============

	/**
	 * API로 부터 데이터를 가져온다.
	 */
	async function makeData() {
		isLoading = true;

		const common_params = {
			palletId: palletId,
			checkedStatus: checkedStatus,
			searchType: searchType,
			keyword: keyword,
			pageSize: pageSize
		};

		const params = new URLSearchParams({ p: p, ...common_params });
		const api_params = new URLSearchParams({ page: p, ...common_params });

		searchParams = params.toString(); // local
		apiSearchParams = api_params.toString(); // api

		await loadItems(`${apiUrl}?${apiSearchParams}`, user);

		if ($palletInfoStore) {
			pageLocationCountry = $palletInfoStore.pallet_info.country;
			pageLocationCity = $palletInfoStore.pallet_info.city;
			pageLocationStore = $palletInfoStore.pallet_info.store;
			pageLocationLine = $palletInfoStore.pallet_info.line;
			pageLocationRack = $palletInfoStore.pallet_info.rack;
			pageLocationLevel = $palletInfoStore.pallet_info.level;
			pageLocationColumn = $palletInfoStore.pallet_info.column;
			pageLocationName = $palletInfoStore.pallet_info.location_name;
			pagePalletCode = $palletInfoStore.pallet_info.code;
			pagePalletStatus = $palletInfoStore.status;
			pagePalletStatusName = getPalletStatusName($palletInfoStore.status);
			pageExportDate = formatDateTimeToString($palletInfoStore.exported_at);
			pagePalletGradeName = $palletInfoStore.palletGradeName;
			pagePalletRegisteredUserName = $palletInfoStore.registered_user.name;
			pagePalletCheckoutAt = $palletInfoStore.checked_at;
			pageTotalQuantity = $palletInfoStore.totalQuantity;
			pageTotalAmount = $palletInfoStore.totalAmount;
			pageTotalInvoice1 = $palletInfoStore.totalInvoice1;
			pageTotalInvoice2 = $palletInfoStore.totalInvoice2;
			pageTotalInvoice3 = $palletInfoStore.totalInvoice3;
			pageInvoiceTotal = $palletInfoStore.invoiceTotal;
		}

		if ($palletProductStore) {
			startNo = $palletProductStore.items.length;
		}

		if (pagePalletStatus < PALLET_STATUS_CLOSED) {
			focusInput.focus();
		}
		
		isLoading = false;
	}

	/**
	 * 자식 컴포넌트에서 변경된 변수의 값을 매칭
	 *
	 * @param value
	 */
	function changeSearchParams(value) {
		if (value.detail.p) p = value.detail.p;
		if (value.detail.checkedStatusGroup || value.detail.checkedStatusGroup === '')
			checkedStatus = value.detail.checkedStatusGroup;
		if (value.detail.searchType) searchType = value.detail.searchType;
		if (value.detail.keyword || value.detail.keyword === '') keyword = value.detail.keyword;
		if (value.detail.pageSize) pageSize = value.detail.pageSize;

		makeData();
	}

	// 전체 선택
	const toggleAllCheck = (items: any) => {
		allChecked = !allChecked;
		idChecked = items.map(() => allChecked);
		ids = allChecked ? items.map((item: any) => item.id) : [];
	};

	async function cancelCheckedPalletProduct(e: MouseEvent, index: number) {
		idChecked[index] = true;

		const item = $palletProductStore.items[index];
		if (item && !ids.includes(item.id)) {
			ids.push(item.id);
		}

		productIds = [];
		productIds.push(e.target?.getAttribute('data-product-id') * 1);

		await cancelPalletProducts();
	}

	async function cancelPalletProducts() {
		const count = ids.length;
		if (count > 0) {
			const message =
				count > 1
					? '선택한 ' + count + '개의 상품을 [점검 취소] 하시겠습니까?'
					: '이 상품의 점검내역을 취소하시겠습니까?';
			const ask = await executeAsk(
				`팔레트[ ${pagePalletCode} ]에서 상품을 삭제합니다.\n\n${message}`
			);

			if (!ask) {
				return false;
			}

			productIds = [];
			checkboxes.forEach((checkbox) => {
				if (checkbox && checkbox.checked) {
					productIds.push(checkbox.getAttribute('data-product-id') * 1);
				}
			});

			try {
				const payload = {
					_method: 'PATCH',
					productIds: productIds
				};

				const { status, data } = await authClient.post(`/wms/pallets/products/exclude-from-pallet`, payload);
				if (status === 200 && data.success) {
					toast.success(`점검 취소 완료`);

					idChecked = Array($palletProductStore.items.length).fill(false);
					ids = [];
					productIds = [];

					await makeData();
				} else {
					const message = data.data.message.replace(/\\n/g, '\n');
					await executeMessage(message);
				}
			} catch (e: any) {
				await handleCatch(e);
			}
		} else {
			await executeMessage('삭제(점검취소)할 상품을 선택해주시기 바랍니다.');
		}
	}

	/**
	 * 팔레트 상품 출고검수 처리<br>
	 * 기존 함수: checkQaid
	 */
	async function deliveryInspection() {
		try {
			const payload = {
				_method: 'PATCH',
				palletId: palletId,
				palletCode: pagePalletCode,
				qaid: checkQaid
			};

			const { status, data } = await authClient.post(apiUrl, payload);

			if (status === 200 && data.success) {
				toast.success(`${checkQaid} 출고검수 완료`);
				checkQaid = '';
				showProductName = data.data.product_name;

				await makeData();
			} else {
				const message = data.data.message.replace(/\\n/g, '\n');
				await executeMessage(message);
			}
		} catch (e: any) {
			await handleCatch(e);
		}
	}

	async function closePallet() {
		const ask = await executeAsk(`팔레트[ ${pagePalletCode} ]의 모든 상품을 마감하시겠습니까?`);
		if (!ask) {
			return false;
		}

		try {
			const payload = {
				palletId: palletId
			};
			const { status, data } = await authClient.put(`${apiUrl}/close`, payload);

			if (status === 200 && data.success) {
				toast.success(`팔레트[ ${pagePalletCode} ] 마감 완료`);

				await makeData();
			} else {
				const message = data.data.message.replace(/\\n/g, '\n');
				await executeMessage(message);
			}
		} catch (e: any) {
			await handleCatch(e);
		}
	}

	async function openPallet() {
		const ask = await executeAsk(`팔레트[ ${pagePalletCode} ]의 마감을 취소하시겠습니까?`);
		if (!ask) {
			return false;
		}

		try {
			const payload = {
				palletId: palletId
			};
			const { status, data } = await authClient.put(`${apiUrl}/open`, payload);

			if (status === 200 && data.success) {
				toast.success(`팔레트[ ${pagePalletCode} ] 마감 취소 완료`);

				await makeData();
			} else {
				const message = data.data.message.replace(/\\n/g, '\n');
				await executeMessage(message);
			}
		} catch (e: any) {
			await handleCatch(e);
		}
	}

	function printPalletCode(event: MouseEvent) {
		if (typeof event !== 'undefined') {
			const printInfo = event.target;

			let grade_name = printInfo?.getAttribute('data-grade-name');
			let level = printInfo?.getAttribute('data-level');
			let column = printInfo?.getAttribute('data-column');
			let export_date = printInfo?.getAttribute('data-export-date');

			let url = `/print/label?level=${level}&column=${column}&grade_name=${grade_name}&export_date=${export_date}`;

			const webview = new WebviewWindow('Print', {
				url: url
			});

			// 창 생성 중 오류가 발생했을 때 이벤트 리스너 추가
			webview.once('tauri://error', (e) => {
				console.error('Error creating webview window', e);
			});
		}
	}

	function printQAID(event) {
		let qaid = event.target.getAttribute('data-qaid');

		alert('바코드 프린터기와 연결하여 별도의 출력서비스를 구현해주시기 바랍니다.');
	}

	onMount(async () => {
		palletInfoStore.set('');
		palletProductStore.set('');

		await makeData();
	});
</script>

<svelte:head>
	<title>출고 > 팔레트 작업목록</title>
</svelte:head>

{#if isLoading}
	<Loading size="60" unit="px" />
{/if}

<AppLayout {user}>
	{#snippet main()}
		<section class="pl-4">
			<div class="breadcrumbs">
				<ul>
					<li>
						<a href="/pallets">
							<Icon data={faFolder} /> 출고
						</a>
					</li>
					<li>
						<a href="/pallets/list">
							<Icon data={faFolder} /> 팔레트 작업 목록
						</a>
					</li>
					<li>
						<span>{pagePalletCode}</span>
						<span class="ml-2">{pagePalletStatusName}</span>
					</li>
				</ul>
			</div>
		</section>

		<section class="main-section">
			<SearchUI>
				<div class="w-full flex">
					<div class="flex items-center w-48 p-1 bg-base-content text-base-300">팔레트 위치</div>
					<div class="flex flex-grow items-center pl-5 py-0.5">
						<button
							class="btn btn-ghost"
							data-column={pageLocationColumn}
							data-export-date={pageExportDate}
							data-grade-name={pagePalletGradeName}
							data-level={pageLocationLevel}
							onclick={printPalletCode}
						>
							{pagePalletCode}
							<Icon data={faPrint} />
						</button>
					</div>
				</div>
				{#if pagePalletStatus < PALLET_STATUS_CLOSED}
					<SearchPalletInspection
						onUpdate={changeSearchParams}
						checkedStatusGroup={checkedStatus}
					/>
				{/if}
				<SearchQaid
					{keyword}
					onUpdate={changeSearchParams}
					option1="QAID/바코드/상품명"
					option2="점검자"
					{searchType}
				>
					<ButtonExcelDownload
						onclick={async (e) => {
							e.preventDefault();
							isLoading = true;

							const payload = getPayload(apiSearchParams);
							payload.palletCode = pagePalletCode;
							payload.palletIds = [Number(palletId)];

							const url = `/wms/pallets/download`;
							await handleDownload(url, payload);
							isLoading = false;
						}}
						useTooltip={true}
					/>
				</SearchQaid>
			</SearchUI>

			{#if pagePalletStatus < PALLET_STATUS_CLOSED}
				<div
					class="w-full px-2 flex flex-col-reverse lg:flex-row items-center justify-center xl:justify-between"
				>
					<div class="w-full">
						<div class="w-full flex">
							<div class="flex items-center w-48 p-1 bg-orange-700 text-white">출고검수</div>
							<div class="flex flex-grow items-center pl-5 p-3 bg-orange-200">
								<label class="input input-bordered input-sm flex items-center justify-center gap-2">
									<input
										bind:this={focusInput}
										bind:value={checkQaid}
										class="grow bg-base-100"
										onkeydown={(e) => {
											if (e.key === 'Enter') {
												deliveryInspection();
											}
										}}
										type="text"
									/>

									<span
										onclick={() => {
											checkQaid = '';
										}}
										role="presentation"
									>
										<Icon class="cursor-pointer" data={faXmark} />
									</span>
								</label>

								<button
									class="btn btn-sm bg-orange-500 ml-3 hover:bg-orange-700 text-white"
									onclick={deliveryInspection}
								>
									<Icon data={faCheckDouble} />
									출고검수
								</button>

								<span class="ml-1 text-xs text-black">
									* 상품 QAID를 스캔하면 빠르게 출고검수를 진행할 수 있습니다.
								</span>
							</div>
						</div>

						<div
							class="p-2.5 bg-neutral-950 text-white text-5xl font-bold max-w-full break-words leading-tight"
						>
							{showProductName}
						</div>
					</div>
				</div>

				<div class="pt-3"></div>
			{/if}

			<!-- 리스트 시작 -->
			<div class="px-2">
				<div class="row" style="padding-bottom: 5px;">
					{#if pagePalletStatus < PALLET_STATUS_CLOSED}
						<button onclick={closePallet} class="btn btn-info btn-xs">
							<Icon data={faLock} />
							점검마감(출고대기)
						</button>
					{/if}

					{#if pagePalletStatus === PALLET_STATUS_CLOSED}
						<button onclick={openPallet} class="btn btn-warning btn-xs">
							<Icon data={faUnlock} />
							마감취소(적재가능)
						</button>
					{/if}
				</div>

				<table class="table text-xs table-pin-rows table-zebra">
					<thead class="uppercase">
						<tr class="h-8 bg-base-content text-base-300 text-center">
							{#if pagePalletStatus <= PALLET_STATUS_CLOSED}
								<th class="p-0.5">
									<input
										checked={allChecked}
										onchange={() => toggleAllCheck($palletProductStore.items)}
										type="checkbox"
									/>
								</th>
							{/if}
							<th class="p-0.5">번호</th>
							<th class="p-0.5">QAID</th>
							<th class="p-0.5">카테고리</th>
							<th class="p-0.5">상품명</th>
							<th class="p-0.5">판매가</th>
							<th class="p-0.5">수리상태</th>
							<th class="p-0.5">
								<p class="text-red-600">증상내용</p>
							</th>
							<th class="p-0.5">점검자</th>
							<th class="p-0.5">
								<p class="text-green-700">처리내용</p>
							</th>
							<th class="p-0.5">수리비용1</th>
							<th class="p-0.5">수리비용2</th>
							<th class="p-0.5">추가비용</th>
							<th class="p-0.5">수리일자</th>
							<th class="p-0.5">출고검수</th>
							<th class="p-0.5">검수일자</th>
							<th class="p-0.5">검수자</th>
							<th class="p-0.5">출고일자</th>
							{#if pagePalletStatus <= PALLET_STATUS_CLOSED}
								<th class="min-w-24 p-0.5 text-right">
									<button type="button" class="btn btn-error btn-xs" onclick={cancelPalletProducts}>
										<Icon data={faBackspace} />
										선택취소
									</button>
								</th>
							{/if}
						</tr>
					</thead>

					<tfoot class="uppercase">
						<tr class="h-12 bg-base-content text-base-300">
							{#if pagePalletStatus <= PALLET_STATUS_CLOSED}
								<th class="p-0.5 text-right" colspan="4">합계</th>
							{:else}
								<th class="p-0.5 text-right" colspan="3">합계</th>
							{/if}
							<th class="p-0.5 text-center">{getNumberFormat(pageTotalQuantity)} 개 상품</th>
							<th class="p-0.5 text-right">{getNumberFormat(pageTotalAmount)} 원</th>
							<th class="p-0.5 text-right" colspan="4">수리비: </th>
							<th class="p-0.5 text-right">{getNumberFormat(pageTotalInvoice1)} 원</th>
							<th class="p-0.5 text-right">{getNumberFormat(pageTotalInvoice2)} 원</th>
							<th class="p-0.5 text-right">{getNumberFormat(pageTotalInvoice3)} 원</th>
							{#if pagePalletStatus <= PALLET_STATUS_CLOSED}
								<th class="p-0.5 text-right" colspan="5">총수리비(수리비용 + 추가비용) = {getNumberFormat(pageInvoiceTotal)} 원</th>
								<th class="p-0.5"></th>
							{:else}
								<th class="p-0.5 text-right" colspan="4">총수리비(수리비용 + 추가비용) = {getNumberFormat(pageInvoiceTotal)} 원</th>
								<th class="p-0.5"></th>
							{/if}
						</tr>
					</tfoot>

					<tbody>
						{#if $palletProductStore.items}
							{#each $palletProductStore.items as item, index}
								<tr class="hover:bg-base-content/10">
									{#if pagePalletStatus <= PALLET_STATUS_CLOSED}
										<td class="w-[20px] min-w-[20px] max-w-[20px] p-0.5 text-center">
											<input bind:checked={idChecked[index]}
														 bind:group={ids}
														 bind:this={checkboxes[index]}
														 data-product-id={item.product.id}
														 value={item.id}
														 type="checkbox"
											/>
										</td>
									{/if}
									<td class="w-[30px] min-w-[30px] max-w-[30px] p-0.5 text-center">
										<input type="hidden" name="pallet_product_id" bind:value={item.product_id} />
										<input type="hidden"
													 name="pallet_product_name"
													 bind:value={item.product.name}
										/> {getNumberFormat(startNo - index)}
									</td>
									<td class="w-[100px] min-w-[100px] max-w-[120px] p-0.5 text-base text-center">
										<span class="flex">
											{item.product.qaid}
											
											{#if item.product.rg === 'Y'}
												<Icon data={faRocket} class="mx-0.5 text-red-700" />
											{/if}
										</span>
									</td>
									<td class="w-[180px] min-w-[120px] max-w-[180px] p-0.5 text-center">
										<div class="flex items-center">
											<div class="w-1/2 p-0">{item.product.cate4.name}</div>
											{#if item.product.cate5}
												<div class="w-1/2 p-0">{item.product.cate5.name}</div>
											{/if}
										</div>
									</td>
									<td class="w-[100px] min-w-[100px] max-w-[100px] p-0.5 tooltip tooltip-top" data-tip={item.product.name}>
										<p class="line-clamp-2 cursor-help">
											{item.product.name}
										</p>
									</td>
									<td class="w-[80px] min-w-[80px] max-w-[80px] p-0.5 text-right">
										{getNumberFormat(item.product.amount)} 원
									</td>
									<td class="w-[45px] min-w-[45px] max-w-[45px] p-0.5 text-center">
										{#if item.product.pallet_products.length > 0}
											{@html getProcessGradeColorButton(item.product.pallet_products[0].process_grade)}
										{:else}
											-
										{/if}
									</td>
									<td class="w-[90px] min-w-[90px] max-w-[90px] p-0.5">
										<p class="text-red-600">{item.process_check.name}</p>
									</td>
									<td class="w-[45px] min-w-[45px] max-w-[45px] p-0.5 text-center">
										{pagePalletRegisteredUserName}
									</td>
									<td class="w-[90px] min-w-[90px] max-w-[90px] p-0.5">
										<p class="text-green-700">{item.process_repair.name}</p>
										<!-- 여기에 메모가 들어가야 한다는데 메모를 뽑아오지를 않음 아마 상품의 메모겠지??? -->
									</td>
									<td class="w-[60px] min-w-[60px] max-w-[60px] p-0.5 text-right">{getNumberFormat(item.invoice1)} 원</td>
									<td class="w-[60px] min-w-[60px] max-w-[60px] p-0.5 text-right">{getNumberFormat(item.invoice2)} 원</td>
									<td class="w-[60px] min-w-[60px] max-w-[60px] p-0.5 text-right">{getNumberFormat(item.invoice3)} 원</td>
									<td class="w-[80px] min-w-[80px] max-w-[80px] p-0.5 text-center">{formatDateTimeToString(item.registered_at)}</td>
									<td class="w-[75px] min-w-[75px] max-w-[75px] p-0.5 text-center">
										{getPalletProductCheckedStatusName(item.checked_status)}
									</td>
									<td class="w-[80px] min-w-[80px] max-w-[80px] p-0.5 text-center">{formatDateTimeToString(item.checked_at)}</td>
									<td class="w-[45px] min-w-[45px] max-w-[45px] p-0.5 text-center">
										{#if item.checked_user}
											{item.checked_user.name}
										{/if}
									</td>
									<td class="w-[80px] mix-w-[80px] max-w-[80px] p-0.5 text-center">
										{formatDateTimeToString(pagePalletCheckoutAt)}
									</td>
									{#if pagePalletStatus <= PALLET_STATUS_CLOSED}
										<td class="min-w-24 p-0.5 text-right">
											<button
												class="btn btn-warning btn-xs"
												data-product-id={item.product.id}
												onclick={(e) => cancelCheckedPalletProduct(e, index)}
											>
												<Icon data={faDeleteLeft} />
												점검취소
											</button>
										</td>
									{/if}
								</tr>
							{/each}
						{/if}
					</tbody>
				</table>
			</div>
		</section>
	{/snippet}
</AppLayout>
