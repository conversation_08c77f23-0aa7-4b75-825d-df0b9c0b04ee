<script lang="ts">
	import type { Breadcrumb } from '$lib/types';

	import { onMount } from 'svelte';
	import { getUser, type User } from '$lib/User';
	import { page } from '$app/state';

	import { WebviewWindow } from '@tauri-apps/api/webviewWindow';
	import { authClient } from '$lib/AxiosBackend';

	import {
		getProcessGradeName,
		PROCESS_GRADE_REFURB,
		PROCESS_GRADE_XL
	} from '$stores/processStore';
	import { getProductStatusName } from '$stores/productStore';
	import { loadedStore, loadItems } from '$stores/loadedStore';

	import { executeMessage, getNumberFormat, handleCatch, scrollToElement } from '$lib/Functions';
	import { getData, type ProcessElement, type ProcessItem } from '$lib/indexedDBHelper';
	import {
		CategoryInvoiceClass,
		GradeBasis,
		GradeBasisUnit,
		InvoiceTypes
	} from '$lib/categoryInvoice';

	import AppLayout from '$components/Layouts/AppLayout.svelte';
	import TitleBar from '$components/UI/TitleBar.svelte';
	import Barcode from '$components/Snippets/Barcode.svelte';

	import Icon from 'svelte-awesome';
	import { faRedo } from '@fortawesome/free-solid-svg-icons/faRedo';
	import { faPrint } from '@fortawesome/free-solid-svg-icons/faPrint';
	import { faBell } from '@fortawesome/free-regular-svg-icons/faBell';
	import { faBarcode } from '@fortawesome/free-solid-svg-icons/faBarcode';
	import { faDolly } from '@fortawesome/free-solid-svg-icons/faDolly';
	import { faBan } from '@fortawesome/free-solid-svg-icons/faBan';
	import { faBoxesPacking } from '@fortawesome/free-solid-svg-icons/faBoxesPacking';
	import { faRocket } from '@fortawesome/free-solid-svg-icons/faRocket';
	import { faXmark } from '@fortawesome/free-solid-svg-icons/faXmark';

	import audioCheckLocationStore from '$lib/assets/audio/check_location_store.mp3';
	import audioCompleteCheckIn from '$lib/assets/audio/complete_checkin_audio.mp3';
	import audioCompletingCheckIn from '$lib/assets/audio/completing_checkin_audio.mp3';
	import audioCompletingRepairIn from '$lib/assets/audio/completing_repair_in_audio.mp3';
	import audioPrintLabel from '$lib/assets/audio/print_label_audio.mp3';
	import audioScanBarcode from '$lib/assets/audio/scan_barcode_audio.mp3';
	import audioScanLabel from '$lib/assets/audio/scan_label_audio.mp3';
	import audioScanLevel from '$lib/assets/audio/scan_level_audio.mp3';
	import audioScanPallet from '$lib/assets/audio/scan_pallet_audio.mp3';
	import audioUnputableOnPackedPallet from '$lib/assets/audio/unputable_on_packed_pallet.mp3';
	import audioWriteLabel from '$lib/assets/audio/write_label_audio.mp3';
	import audioWrongLabel from '$lib/assets/audio/wrong_label_audio.mp3';
	import audioWrongPallet from '$lib/assets/audio/wrong_pallet_audio.mp3';
	import audioFailAndRetryAgain from '$lib/assets/audio/fail_and_retry_again.mp3';

	const audioFiles: Record<string, string> = {
		check_location_store: audioCheckLocationStore,
		complete_checkin_audio: audioCompleteCheckIn,
		completing_checkin_audio: audioCompletingCheckIn,
		completing_repair_in_audio: audioCompletingRepairIn,
		print_label_audio: audioPrintLabel,
		scan_barcode_audio: audioScanBarcode,
		scan_label_audio: audioScanLabel,
		scan_level_audio: audioScanLevel,
		scan_pallet_audio: audioScanPallet,
		unputable_on_packed_pallet: audioUnputableOnPackedPallet,
		write_label_audio: audioWriteLabel,
		wrong_label_audio: audioWrongLabel,
		wrong_pallet_audio: audioWrongPallet,
		fail_and_retry_again: audioFailAndRetryAgain
	};

	type BasisType = { invoice_amount: number; basis_over: number; basis_to: number };

	const user: User = getUser();
	const apiUrl = '/wms/pallets/loaded';
	const localUrl = page.url.pathname;
	let isLoading = $state(false); // 로딩중 표시 여부

	// 필수 변수 세팅 시작==================
	let apiSearchParams = '';

	// 작업 중이던 location code
	let workedLocationCode = window.localStorage.getItem('worked_location_code') ?? '';

	let palletCode = $state(page.url.searchParams.get('palletCode') ?? ''); // 원래는 palletNo
	let locationCountry = $state('KR');
	let locationCity = $state('ESCS');
	let locationPlace = locationCountry + '-' + locationCity;
	let store = $state('A');
	let line = $state('1');
	let rack = $state('1');
	let level = $state('');
	let column = $state('');
	let osReinstall = $state(false);
	let addParts = $state([]);

	let splitCode;
	if (palletCode && palletCode.includes('-')) {
		splitCode = palletCode.split('-');

		level = splitCode[0];
		column = splitCode[1];
	} else if (level === '' && workedLocationCode !== '' && workedLocationCode !== 'A-1-1-') {
		splitCode = workedLocationCode.split('-');

		level = splitCode[3];
		column = splitCode[4];
		palletCode = `${level}-${column}`;
	}

	let locationCode = `${store}-${line}-${rack}-${level}-${column}`;

	let prodScanMessage = $state('');
	let palletGradeCode = $state('');
	let palletProdCount = $state(0);
	let palletRecentProducts: any[] = $state([]);

	let checkingBarcode = $state('');
	let checkingProduct: any[] = $state([]);
	let checkCode = $state('');
	let repairCode = $state('');
	let gradeCode = $state('');

	let basisType = $state('');
	let basisUnit = $state('');

	let invoice1 = $state(0);
	let invoice2 = $state(0);
	let invoice3 = $state(0);
	let invoice1Readonly: HTMLInputElement | null = $state(null);
	let invoice3Readonly: HTMLInputElement | null = $state(null);
	let invoiceTotalReadonly: HTMLInputElement | null = $state(null);
	let memo = $state('');
	let commandVisible = $state(false); // 명령어 바코드

	const basisTypeOptions = Object.values(GradeBasis);
	basisType = 'price';
	const basisUnitOptions = Object.values(GradeBasisUnit);
	basisUnit = 'won';
	let basisRangeOptions = $state([]);
	let basisRangeSelected = $state(0);

	// 필수 변수 세팅 종료==================

	// 모달창: 미등록상품 등록/수정 창 시작========
	let modal: HTMLDialogElement;
	let modalErrorMessage = $state('');

	// 모달창: 미등록상품 등록/수정 창 종료========

	/**
	 * 페이지 최초 접속시 실행
	 * 적재중인 팔레트 리스트
	 * 기존 loadPalletCode 함수
	 */
	async function makeData() {
		const common_params = {
			level: level,
			column: column
		};

		const api_params = new URLSearchParams({ ...common_params });
		apiSearchParams = api_params.toString(); // api

		await loadItems(`${apiUrl}?${apiSearchParams}`, user);
	}

	let isFocusCheckingBarcode: HTMLElement;
	let isReadonlyCheckingBarcode = $state(false);
	let locationScanMessageClass = $state(true); // 스타일 변환
	let prodScanMessageOffClass = $state(false); // 스타일 변환
	let prodScanMessageOnClass = $state(false); // 스타일 변환
	let isCompleteButtonSuccess = $state(false); // 점검완료 버튼

	function checkView() {
		if (locationCode === '' || locationCode === 'A-1-1--') {
			playAudio('check_location_store');
			scrollToElement('location_box');
			locationScanMessageClass = true;
			isCompleteButtonSuccess = false;
		} else {
			playAudio('scan_barcode_audio');
			scrollToElement('product_box');
			locationScanMessageClass = false;
			isCompleteButtonSuccess = true;
		}

		isReadonlyCheckingBarcode = false;
		checkingBarcode = '';
		isFocusCheckingBarcode.focus();
		osReinstall = false;
	}

	/**
	 * 작업간 모두 바코드를 이용해 처리한다.
	 * 바코드를 찍으면 실제 이 곳에서 분기하여 각각 처리
	 */
	async function handleBarcodeInput(event: Event) {
		event.preventDefault();

		const barcode = checkingBarcode.trim();

		let prefixes = ['check', 'repair', 'grade', 'basistype', 'basisunit', 'fix', 'charge'];

		if (prefixes.some((prefix) => barcode.startsWith(prefix))) {
			selectProcess(barcode);
		} else if (barcode.startsWith('change_pallet')) {
			const pallet_no = barcode.split('/')[1];
			setPalletNo(pallet_no);
		} else if (barcode.startsWith('memo')) {
			// 메모 입력
			const memo_text = barcode.split('/')[1];
			memo += `수리\n`;
		} else if (barcode === 'osinstall') {
			await checkInvoice('OS');
		} else if (barcode.startsWith('cancel')) {
			await cancelCheckIn();
		} else if (barcode === 'complete') {
			await completeCheckIn();
		} else {
			if (checkingBarcode !== '') {
				await getProductInfo();
			}
		}
	}

	// 새로운 팔레트 번호를 생성
	async function reloadLocationCode() {
		try {
			const { status, data } = await authClient.get(`/wms/locations/generate/code/${locationPlace}`);
			if (status === 200 && data.success) {
				level = data.data.level;
				column = data.data.column;

				palletCode = level + '-' + column;
				locationCode = `A-1-1-${level}-${column}`;

				const new_item = {
					pallet_no: palletCode,
					isLocation: false,
					palletGradeCode: '',
					palletProdCount: 0,
					palletRecentProducts: []
				};
				loadedStore.update((currentData) => {
					return { ...currentData, items: [...currentData.items, new_item] };
				});
				window.localStorage.setItem('worked_location_code', locationCode);

				checkingProduct = [];
				invoice3 = 0;
				invoice2 = 0;
				invoice1 = 0;
				gradeCode = '';
				repairCode = '';
				checkCode = '';

				checkView();
			} else {
				await executeMessage('생성 실패', 'error');
			}
		} catch (e: any) {
			await handleCatch(e);
		}
	}

	function setPalletNo(pallet_no: string) {
		if (pallet_no !== '' && pallet_no !== '-') {
			palletCode = pallet_no;
			
			const pallet_no_arr = pallet_no.split('-');
			level = pallet_no_arr[0];
			column = pallet_no_arr[1];

			setLocationCode();
		} else {
			level = '';
			column = '';
			palletCode = '';
			locationCode = '';
			checkingProduct = [];
			invoice3 = 0;
			invoice2 = 0;
			invoice1 = 0;
			gradeCode = '';
			repairCode = '';
			checkCode = '';

			isReadonlyCheckingBarcode = false;
			checkingBarcode = '';
			isFocusCheckingBarcode.focus();
		}
	}

	// 선택된 팔레트를 확정
	async function setLocationCode() {
		checkingProduct = [];
		invoice3 = 0;
		invoice2 = 0;
		invoice1 = 0;

		if (
			locationCountry !== '' &&
			locationCity !== '' &&
			store !== '' &&
			line !== '' &&
			rack !== '' &&
			level !== '' &&
			column !== ''
		) {
			if (!locationCode || locationCode === 'A-1-1--') {
				locationCode = store + '-' + line + '-' + rack + '-' + level + '-' + column;
			}

			try {
				const { status, data } = await authClient.get(
					`${apiUrl}/set/${locationPlace}/${locationCode}`
				);

				if (status === 200 && data.success) {
					const item = data.data;
					if (item.palletGradeCode !== '' && typeof item.palletGradeCode !== 'undefined') {
						gradeCode = item.palletGradeCode; // 팔레트 상품의 grade 코드
						palletGradeCode = item.palletGradeCode; // 팔레트의 grade 코드
						console.log(gradeCode, palletGradeCode);
						
						if (palletGradeCode === PROCESS_GRADE_XL) {
							selectProcess('grade/' + gradeCode);
						} else {
							repairCode = '';
							checkCode = '';
						}
						
						palletProdCount = item.palletProdCount;
						palletRecentProducts = item.palletRecentProducts.reverse();
					} else {
						modalErrorMessage = data.data.message;
						modal.showModal();
					}

					checkView();
				} else {
					await executeMessage(data.message, 'error');
				}
			} catch (e: any) {
				await handleCatch(e);
			}
		}
	}

	function printPalletCode() {
		const grade_name = getProcessGradeName(palletGradeCode);
		const url = `/print/label?level=${level}&column=${column}&grade_name=${grade_name}`;
		const webview = new WebviewWindow('Print', {
			url: url
		});

		// 창 생성 중 오류가 발생했을 때 이벤트 리스너 추가
		webview.once('tauri://error', () => {
			executeMessage('프린트 창을 여는데 실패 했습니다.\n프로그래머에게 문의해 주세요.', 'error');
		});
	}

	function handleError(message: string) {
		prodScanMessageOffClass = false;
		prodScanMessageOnClass = true;
		prodScanMessage = message;
		invoice3 = invoice2 = invoice1 = 0;

		checkView();
		return true; // 에러 발생 시 true 반환
	}

	const processHandlers = {
		change_pallet: (process_code: string) => {
			setPalletNo(process_code);
		},

		check: (process_code: string) => {
			if (typeof checkingProduct.id === 'undefined') {
				if (handleError('점검할 상품을 먼저 확인해주시기 바랍니다.')) {
					checkingProduct = [];
					gradeCode = repairCode = checkCode = '';
					return;
				}
			}
			checkCode = process_code;
			checkingBarcode = '';
		},

		repair: (process_code: string) => {
			if (typeof checkingProduct.id === 'undefined') {
				if (handleError('점검할 상품을 먼저 확인해주시기 바랍니다.')) {
					gradeCode = repairCode = checkCode = '';
					return;
				}
			}

			if (!checkCode && handleError('증상내용을 먼저 확인해주시기 바랍니다.')) {
				gradeCode = repairCode = '';
				return;
			}
			repairCode = process_code;
			checkingBarcode = '';
		},

		grade: (process_code: string) => {
			if (palletGradeCode !== '' && process_code !== '' && process_code !== palletGradeCode) {
				if (handleError('팔레트타입 확인 후 수리상태를 입력(스캔)해 주시기 바랍니다.')) {
					gradeCode = '';
					return;
				}
			} else {
				if (typeof checkingProduct.id === 'undefined') {
					if (handleError('점검할 상품을 먼저 확인해주시기 바랍니다.')) {
						gradeCode = repairCode = checkCode = '';
						return;
					}
				}

				if (!checkCode && handleError('증상내용을 먼저 확인해주시기 바랍니다.')) {
					gradeCode = repairCode = '';
					return;
				}

				if (!repairCode && handleError('처리내용을 먼저 확인해주시기 바랍니다.')) {
					gradeCode = '';
					return;
				}
				gradeCode = process_code;
			}
			basisRangeOptions = [];
			checkInvoice('INV');
		},

		basistype: (process_code: string) => {
			if (typeof checkingProduct.id === 'undefined') {
				if (handleError('점검할 상품을 먼저 확인해주시기 바랍니다.')) {
					gradeCode = repairCode = checkCode = '';
					return;
				}
			}

			if (!checkCode && handleError('증상내용을 먼저 확인해주시기 바랍니다.')) {
				gradeCode = repairCode = '';
				return;
			}

			if (!repairCode && handleError('처리내용을 먼저 확인해주시기 바랍니다.')) {
				gradeCode = '';
				return;
			}

			if (!gradeCode && handleError('수리상태를 먼저 확인해주시기 바랍니다.')) return;

			invoice3 = invoice2 = invoice1 = 0;
			basisType = process_code;
			basisRangeOptions = [];
			checkInvoice('INV');
		},

		// 다른 프로세스 타입에 대한 핸들러도 유사한 방식으로 추가
		basisunit: (process_code: string) => {
			if (typeof checkingProduct.id === 'undefined') {
				if (handleError('점검할 상품을 먼저 확인해주시기 바랍니다.')) {
					gradeCode = repairCode = checkCode = '';
					return;
				}
			}

			if (!checkCode && handleError('증상내용을 먼저 확인해주시기 바랍니다.')) {
				gradeCode = repairCode = '';
				return;
			}

			if (!repairCode && handleError('처리내용을 먼저 확인해주시기 바랍니다.')) {
				gradeCode = '';
				return;
			}

			if (!gradeCode && handleError('수리상태를 먼저 확인해주시기 바랍니다.')) return;
			if (!basisType && handleError('수리비용기준을 먼저 확인해주시기 바랍니다.')) return;

			invoice3 = invoice2 = invoice1 = 0;
			basisUnit = process_code;
			basisRangeOptions = [];
			checkInvoice('INV');
		},

		fix: (process_code: string) => {
			if (typeof checkingProduct.id === 'undefined') {
				if (handleError('점검할 상품을 먼저 확인해주시기 바랍니다.')) {
					gradeCode = repairCode = checkCode = '';
					return;
				}
			}

			if (!checkCode && handleError('증상내용을 먼저 확인해주시기 바랍니다.')) {
				gradeCode = repairCode = '';
				return;
			}

			if (!repairCode && handleError('처리내용을 먼저 확인해주시기 바랍니다.')) {
				gradeCode = '';
				return;
			}

			if (!gradeCode && handleError('수리상태를 먼저 확인해주시기 바랍니다.')) return;
			if (!basisType && handleError('수리비용기준을 먼저 확인해주시기 바랍니다.')) return;

			addInvoiceMemo('fix', process_code);
		},

		charge: (process_code: string) => {
			if (typeof checkingProduct.id === 'undefined') {
				if (handleError('점검할 상품을 먼저 확인해주시기 바랍니다.')) {
					gradeCode = repairCode = checkCode = '';
					return;
				}
			}

			if (!checkCode && handleError('증상내용을 먼저 확인해주시기 바랍니다.')) {
				gradeCode = repairCode = '';
				return;
			}

			if (!repairCode && handleError('처리내용을 먼저 확인해주시기 바랍니다.')) {
				gradeCode = '';
				return;
			}

			if (!gradeCode && handleError('수리상태를 먼저 확인해주시기 바랍니다.')) return;
			if (!basisType && handleError('수리비용기준을 먼저 확인해주시기 바랍니다.')) return;

			addInvoiceMemo('charge', process_code);
		}
	};

	function selectProcess(process_command: string) {
		prodScanMessage = '';

		const [process_type, process_code] = process_command.split('/');
		console.log('process_command: ', process_command);
		console.log(`process_type: ${process_type}, process_code: ${process_code}`);

		const handler = processHandlers[process_type];
		if (handler) {
			handler(process_code);
		} else {
			console.error('Unknown process type:', process_type);
		}
	}

	// 기존 addCharge2Memo, addCharge3Memo
	async function addInvoiceMemo(process_type: string, charge_fee: string) {
		const [process_code, amount] = charge_fee.split('@');

		try {
			const { status, data } = await authClient.get(
				`${apiUrl}/other-expenses/${process_type}/${process_code}`
			);

			if (status === 200 && data.success) {
				// 별도 수리비
				if (process_type === 'fix') {
					invoice2 += parseInt(amount, 10);
				}

				// 구성품
				if (process_type === 'charge') {
					invoice3 += parseInt(amount, 10);
				}

				const process_name = data.data.name ?? process_code.substring(3);
				memo += `${process_name} : ${amount} 원\n`;
			} else {
				await executeMessage(data.data.message);
			}
		} catch (e: any) {
			await handleCatch(e);
		}
	}

	async function checkInvoice(invoice_type: string) {
		const product_id = checkingProduct.id;
		
		console.log('checkInvoice 함수 - invoice_type: ', invoice_type);
		console.log(product_id, gradeCode, basisType, basisUnit);

		const filter = [product_id, gradeCode, basisType, basisUnit];
		if (filter.every((value) => value !== undefined && value !== '')) {
			try {
				let inv_type = ''; // invoice_type
				let basis_type = '';
				let basis_unit = '';
				let invoice_amount = 0;
				let basis_range: BasisType[] = [];
				
				if (gradeCode === PROCESS_GRADE_XL) {
					basis_type = GradeBasis.None;
					basis_unit = GradeBasisUnit.Won;
					invoice_amount = 0;
					
					return;
				} else {
					inv_type = 'OS';
					if (invoice_type !== 'OS') {
						inv_type = InvoiceTypes.Checked;
						if (gradeCode === PROCESS_GRADE_REFURB) {
							inv_type = InvoiceTypes.Repaired;
						}
					}
				}
				
				const { status, data } = await authClient.get(`${apiUrl}/check-invoice/${product_id}/${inv_type}`);

				if (status === 200 && data.success) {
					const result = data.data;

					if (result.isAlreadyChecked === false) {
						basis_type = result.fee_type;
						basis_unit = result.fee_unit;
						basisRangeOptions = result.price_ranges;
						basisRangeSelected = result.amount;
						invoice_amount = result.amount;

						if (invoice_amount) {
							if (invoice_type === 'OS') {
								memo += 'OS설치 : ' + invoice_amount + ' 원\n';
								invoice3 = invoice_amount;
								osReinstall = true;
							} else {
								basisType = basis_type;
								basisUnit = basis_unit;
								if (invoice_amount == 0) {
									if (invoice1Readonly) {
										invoice1Readonly.readOnly = false; // 또는 false
									}
								}
								
								invoice1 = invoice_amount;
								// basisRangeSelected = invoice_amount;
							}
						} else {
							basisType = basis_type;
							basisUnit = basis_unit;

							if (invoice1Readonly) {
								invoice1Readonly.readOnly = false; // 또는 false
							}
						}
					}
				} else {
					await executeMessage(data.data.message);
				}
			} catch (e: any) {
				await handleCatch(e);
			}
		}
		checkView();
	}

	/**
	 * 입력된 바코드가 QAID일 경우 서버에서 상품정보를 가져온다.
	 */
	async function getProductInfo() {
		try {
			const { status, data } = await authClient.get(`${apiUrl}/check-product/${checkingBarcode}`);
			if (status === 200 && data.success) {
				checkingProduct = data.data.product;

				invoice1 = 0;
				invoice2 = 0;
				invoice3 = 0;

				if (palletGradeCode === PROCESS_GRADE_XL) {
					selectProcess('grade/' + PROCESS_GRADE_XL);
					return;
				} else if (checkingProduct.carryout_at) {
					checkCode = checkingProduct.process_check_code;
					repairCode = checkingProduct.process_repair_code;
					gradeCode = checkingProduct.process_grade_code;
					invoice2 = checkingProduct?.carryout_products?.invoice2;
					memo = checkingProduct?.carryout_products?.memo;
				} else {
					gradeCode = '';
					repairCode = '';
					checkCode = '';
					checkView();
				}
			} else {
				checkingProduct = [];
				modalErrorMessage = data.data.message;
				modal.showModal();
				checkView();
			}
		} catch (e: any) {
			checkingProduct = [];
			await handleCatch(e);
		}
	}

	async function completeCheckIn() {
		const product_id = checkingProduct.id;
		const qaid = checkingProduct.qaid;

		if (
			typeof product_id !== 'undefined' &&
			checkCode !== '' &&
			repairCode !== '' &&
			gradeCode !== '' &&
			locationCountry !== '' &&
			locationCity !== '' &&
			level !== '' &&
			column !== ''
		) {
			let payload = {
				location_place: locationPlace,
				location_code: locationCode,
				product_id: product_id,
				qaid: qaid,
				checkCode: checkCode,
				repairCode: repairCode,
				gradeCode: gradeCode,
				os_reinstall: osReinstall,
				add_parts: addParts,
				invoice1: invoice1,
				invoice2: invoice2,
				invoice3: invoice3,
				memo: memo
			};

			playAudio('completing_repair_in_audio');

			try {
				const { status, data } = await authClient.post(`${apiUrl}/save-on-pallet`, payload);
				if (status === 200 && data.success) {
					// 작업중인 로케이션을 저장해 둔다.
					window.localStorage.setItem('worked_location_code', locationCode);
					await setLocationCode();
					await makeData();
					
					// 팔레트를 계속 유지하기 위해
					const pallet_no = palletCode;
					clearValues();
					
					palletCode = pallet_no;
					level = pallet_no.split('-')[0];
					column = pallet_no.split('-')[1];
				} else {
					await executeMessage(data.data.message);
					playAudio('fail_and_retry_again');
				}
			} catch (error) {
				playAudio('fail_and_retry_again');
			}
		} else {
			checkView();
			return;
		}
	}

	async function cancelCheckIn() {
		clearValues();
	}

	function clearValues() {
		level = '';
		column = '';
		palletCode = '';
		locationCode = '';
		checkingBarcode = '';
		checkingProduct = [];
		invoice3 = 0;
		invoice2 = 0;
		invoice1 = 0;
		gradeCode = '';
		repairCode = '';
		checkCode = '';
		memo = '';
		prodScanMessage = '';
		osReinstall = false;
		addParts = [];
	}

	function playAudio(audio_name: string) {
		let audio_obj;

		if (Object.prototype.hasOwnProperty.call(audioFiles, audio_name)) {
			playAudioFile(audio_obj, audioFiles[audio_name]);
		} else {
			executeMessage('오디오를 찾을 수 없습니다.', 'error');
		}
	}

	function playAudioFile(audio_obj, filename: string) {
		audio_obj = new Audio(filename);
		audio_obj.load();
		audio_obj.oncanplaythrough = () => {
			setTimeout(() => {
				audio_obj
					.play()
					.then(() => {
						audio_obj.onended = () => {
							return true;
						};
					})
					.catch((error) => {
						// Autoplay was prevented.
						// Show a "Play" button so that user can start playback.
						executeMessage(error, 'error');
					});
			}, 100);
		};
	}

	function handleClick(event: MouseEvent) {
		let target_name = event.target?.getAttribute('name');
		let target_id = event.target?.getAttribute('id');

		if (
			(typeof target_name != 'undefined' && target_name === 'checking_barcode') ||
			[
				'complete_btn',
				'cancel_btn',
				'rp_bid_id',
				'level',
				'column',
				'pallet_no',
				'check_code',
				'repair_code',
				'grade_code',
				'basis_type',
				'basis_unit',
				'invoice1',
				'invoice2',
				'memo',
				'button_barcode_box',
				'load_lc_code_btn',
				'set_lc_cd_btn',
				'command_barcode_title'
			].includes(target_id)
		) {
			const path = event.composedPath();
			const matching_element = path.find(
				(el) =>
					el.matches &&
					el.matches('input, div, select, table, tr, td, span, body, image, textarea, button')
			);
			if (matching_element) matching_element.focus();
		} else {
			checkView();
		}
	}

	let palletDetails = null;
	async function handlePalletChange() {
		try {
			// 새로운 데이터 로드
			await makeData();

			// store 구독하여 새로운 값 확인
			const unsubscribe = loadedStore.subscribe(value => {
				if (value && value.items) {
					palletDetails = value.items.find((item) => item.pallet_no === palletCode);

					if (palletDetails) {
						const split_code = palletCode.split('-');
						level = split_code[0];
						column = split_code[1];
						locationCode = `A-1-1-${level}-${column}`;

						palletGradeCode = palletDetails.palletGradeCode;
						palletProdCount = palletDetails.palletProdCount;
						palletRecentProducts = palletDetails.palletRecentProducts.reverse();

						checkView();
					}
				}
			});

			// 구독 해제
			unsubscribe();

			gradeCode = '';
			basisRangeSelected = 0;
			invoice1 = 0;
			invoice2 = 0;
			invoice3 = 0;
		} catch (error) {
			console.error('Failed to handle pallet change:', error);
		}
	}

	let processCheck: ProcessElement;
	let processRepair: ProcessElement;
	let processGrade: ProcessElement;

	let processCheckData: ProcessItem[] = $state([]);
	let processRepairData: ProcessItem[] = $state([]);
	let processGradeData: ProcessItem[] = $state([]);

	async function getProcessDataFromIndexedDB() {
		processCheck = await getData('processes', 'check');
		if (processCheck.data) {
			processCheckData = processCheck.data;
		}

		processRepair = await getData('processes', 'repair');
		if (processRepair.data) {
			processRepairData = processRepair.data;
		}

		processGrade = await getData('processes', 'grade');
		if (processGrade.data) {
			processGradeData = processGrade.data;
		}
	}

	function updateCheckingBarcode(newValue: string) {
		checkingBarcode = newValue;
	}

	$effect(() => {
		// if (check_code) selectProcess("check/" + check_code);
		// if (repair_code) selectProcess("repair/" + repair_code);
		// if (grade_code) selectProcess("grade/" + grade_code);
		// if (basis_type) selectProcess("basistype/" + basis_type);
		// if (basis_unit) selectProcess("basisunit/" + basis_unit);
		// if (pallet_no) selectProcess("change_pallet/" + pallet_no);

		const regex = /[ㄱ-ㅎ|ㅏ-ㅣ|가-힣]/;
		if (regex.exec(checkingBarcode) !== null) {
			const value = checkingBarcode.slice(0, -1);
			setTimeout(() => {
				updateCheckingBarcode(value);
			}, 0);
		}
	});

	onMount(async () => {
		loadedStore.set('');

		await makeData();
		if (palletCode) {
			loadedStore.subscribe((storeData) => {
				const pallet_details = storeData.items.find((item) => item.pallet_no === palletCode);

				if (pallet_details) {
					const split_code = palletCode.split('-');
					level = split_code[0];
					column = split_code[1];
					locationCode = `A-1-1-${level}-${column}`;

					palletGradeCode = pallet_details.palletGradeCode;
					palletProdCount = pallet_details.palletProdCount;
					palletRecentProducts = pallet_details.palletRecentProducts.reverse();
				}
			});
		}

		await getProcessDataFromIndexedDB();

		checkView();

		if (invoice1Readonly) {
			invoice1Readonly.readOnly = true;
		}

		if (invoice3Readonly) {
			invoice3Readonly.readOnly = true;
		}

		if (invoiceTotalReadonly) {
			invoiceTotalReadonly.readOnly = true;
		}

		modal = document.getElementById('my_modal_1') as HTMLDialogElement;
	});

	const breadcrumbs: Breadcrumb[] = [
		{ title: '출고', url: '/pallets/list' },
		{ title: '출고 팔레트 목록', url: '/pallets/list' },
		{ title: '출고상품 적재', url: '/pallets/create' }
	];
</script>

<style>
    .location_scan_messagebox {
        font-size: 14pt;
        font-weight: bold;
        color: #28a745;
    }

    .messagebox_off {
        font-size: 12pt;
        color: #515151;
    }

    .messagebox_on {
        font-size: 14pt;
        font-weight: bold;
        color: #e07934;
    }
</style>

<svelte:head>
	<title>출고상품 적재</title>
</svelte:head>

<AppLayout {user}>
	{#snippet main()}
		<div>
			<TitleBar {breadcrumbs} />

			<section class="main-section">
				<div class="w-full px-3 flex flex-row justify-start"
						 onclick={handleClick}
						 role="presentation"
				>
					<div class="w-[800px] flex flex-col">
						<div class="w-full">
							<div class="border border-neutral-300" id="location_box">
								<div>
									<div class="p-2 bg-neutral-300 border-b-2 border-b-neutral-400 text-black">
										<Icon data={faDolly} scale={1.5} />
										<span class="text-xl font-bold">점검완료 후 적재위치</span>
									</div>
								</div>
								<div class="p-2">
									<div
										class="p-1"
										class:location_scan_messagebox={locationScanMessageClass}
										id="location_scan_message"
									>
										아래에 점검 및 수리 완료 후 적재할 위치(창고 및 팔레트번호)를 확인해 주시기
										바랍니다.
									</div>
									<div class="flex">
										<div class="w-1/4">
											<div class="label label-text font-bold">창고 지역</div>
											<div>
												<input bind:value={locationCountry} name="location_country" type="hidden" />
												<input bind:value={locationCity} name="location_city" type="hidden" />
												<span class="flex h-12 items-center text-xl">음성나동(코너스톤)</span>
											</div>
										</div>
										<div class="w-1/4">
											<div class="label label-text font-bold">존-층(단)-칸</div>
											<div>
												<input
													bind:value={store}
													class="input input-bordered p-0 text-center text-2xl w-12 h-12 bg-neutral-700 text-neutral-300"
													id="store"
													name="store"
													readonly
													type="text"
												/>
												-
												<input
													bind:value={line}
													class="input input-bordered p-0 text-center text-2xl w-12 h-12 bg-neutral-700 text-neutral-300"
													id="line"
													name="line"
													readonly
													type="text"
												/>
												-
												<input
													bind:value={rack}
													class="input input-bordered p-0 text-center text-2xl w-12 h-12 bg-neutral-700 text-neutral-300"
													id="rack"
													name="rack"
													readonly
													type="text"
												/>
											</div>
										</div>
										<div class="w-2/4">
											<div class="w-full label label-text font-bold">팔레트 번호</div>
											<div class="w-full flex items-center">
												<select
													bind:value={palletCode}
													class="select select-bordered w-full text-2xl"
													id="pallet_no"
													onchange={async () => {
														await handlePalletChange();
													}}
												>
													<option value="">선택</option>
													{#if $loadedStore.items}
														{#each $loadedStore.items as item}
															<option value={item.pallet_no}>{item.pallet_no}</option>
														{/each}
													{/if}
												</select>

												<button class="btn btn-info" onclick={reloadLocationCode} type="button">
													<Icon data={faRedo} />
												</button>

												<button class="btn btn-primary" onclick={setLocationCode} type="button">
													팔레트확정
												</button>

												{#if level !== '' && column !== ''}
													<button class="btn btn-ghost p-1" onclick={printPalletCode}>
														<Icon data={faPrint} scale={2} lavel="프린터" />
													</button>
												{/if}
											</div>
										</div>
									</div>
								</div>
							</div>

							<div class="py-3"></div>

							<!--바코드 스캔-->
							<div class="w-full">
								<div class="mb-2 border border-neutral-300" id="product_box">
									<div>
										<div
											class="p-2 flex items-center bg-neutral-300 border-b-2 border-b-neutral-400 text-black"
										>
											<Icon data={faBarcode} />
											<span class="pl-2 text-xl font-bold">바코드 스캔</span>
										</div>
									</div>
									<div class="h-36 p-2 flex flex-col">
										<div>
											<div class="location_scan_messagebox">
												바코드값을 입력(스캔)해 주시기 바랍니다.
											</div>
											<div
												class:messagebox_off={prodScanMessageOffClass}
												class:messagebox_on={prodScanMessageOnClass}
												id="prod_scan_message"
											>
												{prodScanMessage}
											</div>
										</div>
										<div class="flex">
											<div class="flex w-3/5">
												<input
													bind:this={isFocusCheckingBarcode}
													bind:value={checkingBarcode}
													class="input input-bordered text-2xl bg-amber-300 w-96"
													id="checking_barcode"
													name="checking_barcode"
													onkeydown={(e) => {
														if (e.key === 'Enter') {
															handleBarcodeInput(e);
														}
													}}
													placeholder="바코드 입력 또는 스캔"
													readonly={isReadonlyCheckingBarcode}
													type="text"
												/>

												<button class="btn btn-primary ml-2"
																onclick={(e) => handleBarcodeInput(e)}
																type="button"
												>
													확인
												</button>
											</div>
											<div class=" w-2/5">
												{#if level && column}
													<div class="w-full h-[80px] text-5xl leading-9 text-center">
														{level}-{column}<br />
														<span class="text-warning text-xl leading-7">
															<span class="text-3xl text-error">
																{#if palletGradeCode}
																	{getProcessGradeName(palletGradeCode)}
																{/if}
															</span> 팔레트
														</span>
													</div>
												{:else}
													<div class="w-full pt-5 text-2xl text-error text-center">
														팔레트 확정 필수
													</div>
												{/if}
											</div>
										</div>
									</div>

									<div class="border"></div>

									<div class="w-full p-2">
										<div class="w-full border border-neutral-400">
											<div class="w-full flex border-0 border-b border-b-neutral-400">
												<div class="w-1/6 flex items-center justify-center bg-neutral-300 text-black font-bold">
													QAID
												</div>
												<div class="w-2/6 p-2 text-center">
													{#if checkingProduct.qaid}
														{checkingProduct.qaid}

														{#if checkingProduct.rg === 'Y'}
															<Icon data={faRocket} scale={2} class="ml-1 text-red-700" />
														{/if}
													{/if}
												</div>
												<div class="w-1/6 flex items-center justify-center bg-neutral-300 text-black font-bold">
													상태
												</div>
												<div class="w-2/6 p-2 text-center">
													{#if checkingProduct.status > 0}
														{getProductStatusName(checkingProduct.status)}
													{/if}
												</div>
											</div>
											<div class="w-full flex border-0 border-b border-b-neutral-400">
												<div class="w-1/6 flex items-center justify-center bg-neutral-300 text-black font-bold">
													카테고리
												</div>
												<div class="w-2/6 p-2 text-center">
													{#if checkingProduct.cate4}
														{checkingProduct.cate4.name}
														{#if checkingProduct.cate5}
															> {checkingProduct.cate5.name}
														{/if}
													{/if}
												</div>
												<div class="w-1/6 flex items-center justify-center bg-neutral-300 text-black font-bold">
													상품명
												</div>
												<div class="w-2/6 p-2">
													{checkingProduct.name}
												</div>
											</div>
											<div class="w-full flex border-0 border-b border-b-neutral-400">
												<div class="w-1/6 flex items-center justify-center bg-neutral-300 text-black font-bold">
													판매가
												</div>
												<div class="w-2/6 p-2 text-center">
													{#if checkingProduct.amount > 0}
														{getNumberFormat(checkingProduct.amount)}
													{/if}
												</div>
												<div class="w-1/6 flex items-center justify-center bg-neutral-300 text-black font-bold">
													점검 수량
												</div>
												<div class="w-2/6 p-2 text-center">
													{#if checkingProduct.quantity}
														{checkingProduct.quantity}
													{/if}
												</div>
											</div>
											<div class="w-full flex border-0 border-b border-b-neutral-400">
												<div class="w-1/6 flex items-center justify-center bg-neutral-300 text-black font-bold">
													증상 내용
												</div>
												<div class="w-5/6 p-2">
													<select bind:value={checkCode}
																	class="select select-bordered select-sm w-full p-0 text-base"
																	id="check_code"
													>
														<option value="">선택</option>
														{#if processCheckData}
															{#each processCheckData as item (item.code)}
																<option value={item.code}>{item.name}</option>
															{/each}
														{/if}
													</select>
												</div>
											</div>
											<div class="w-full flex border-0 border-b border-b-neutral-400">
												<div class="w-1/6 flex items-center justify-center bg-neutral-300 text-black font-bold">
													처리 내용
												</div>
												<div class="w-5/6 p-2">
													<select bind:value={repairCode}
																	class="select select-bordered select-sm w-full p-0 text-base"
																	id="repair_code"
													>
														<option value="">선택</option>
														{#if processRepairData}
															{#each processRepairData as item (item.code)}
																<option value={item.code}>{item.name}</option>
															{/each}
														{/if}
													</select>
												</div>
											</div>
											<div class="w-full flex border-0 border-b border-b-neutral-400">
												<div class="w-1/6 flex items-center justify-center bg-neutral-300 text-black font-bold">
													수리 상태
												</div>
												<div class="w-5/6 p-2">
													<select bind:value={gradeCode}
																	class="select select-bordered select-sm w-full p-0 text-base"
																	id="grade_code"
													>
														<option value="">선택</option>
														{#if processGradeData}
															{#each processGradeData as item (item.code)}
																<option value={item.code}>{item.name}</option>
															{/each}
														{/if}
													</select>
												</div>
											</div>
											<div class="w-full flex border-0 border-b border-b-neutral-400">
												<div class="w-1/6 flex items-center justify-center bg-neutral-300 text-black font-bold">
													수리비용 기준
												</div>
												<div class="w-5/6 p-2">
													<select bind:value={basisType}
																	class="select select-bordered select-sm max-w-xs py-0 pl-2 pr-10"
																	id="basis_type"
													>
														<option value="">선택</option>
														{#each basisTypeOptions as option}
															<option value={option}>
																{CategoryInvoiceClass.GRADE_BASIS_TYPE_NAME[option]}
															</option>
														{/each}
													</select>

													<select bind:value={basisRangeSelected}
																	class="select select-bordered select-sm max-w-xs py-0 pl-2 pr-10"
																	id="basis_range"
													>
														<option value="0">비용청구기준범위</option>
														{#if basisRangeOptions.length > 0}
															{#each basisRangeOptions as item}
																<option value={item.amount}>
																	{item.range}
																</option>
															{/each}
														{/if}
													</select>

													<select bind:value={basisUnit}
																	class="select select-bordered select-sm max-w-xs py-0 pl-2 pr-10"
																	id="basis_unit"
													>
														<option value="">선택</option>
														{#each basisUnitOptions as option}
															<option value={option}>
																{CategoryInvoiceClass.GRADE_BASIS_UNIT_NAME[option]}
															</option>
														{/each}
													</select>
												</div>
											</div>
											<div class="w-full flex border-0 border-b border-b-neutral-400">
												<div class="w-1/6 flex items-center justify-center bg-neutral-300 text-black font-bold">
													수리비용1
												</div>
												<div class="w-1/6 p-2">
													<input
														bind:this={invoice1Readonly}
														bind:value={invoice1}
														class="input input-bordered input-sm w-full text-right"
														class:bg-base-300={invoice1Readonly}
														max="9999999"
														min="0"
														name="invoice1"
														step="10"
														type="number"
													/>
												</div>
												<div class="w-1/6 flex items-center justify-center bg-neutral-300 text-black font-bold">
													수리비용2
												</div>
												<div class="w-1/6 p-2">
													<input
														bind:value={invoice2}
														class="input input-bordered input-sm w-full text-right"
														id="invoice2"
														max="9999999"
														min="0"
														name="invoice2"
														step="10"
														type="number"
													/>
												</div>
												<div class="w-1/6 flex items-center justify-center bg-neutral-300 text-black font-bold">
													추가비용
												</div>
												<div class="w-1/6 p-2">
													<input
														bind:this={invoice3Readonly}
														bind:value={invoice3}
														class="input input-bordered input-sm w-full text-right"
														class:bg-base-300={invoice3Readonly}
														id="invoice3"
														max="9999999"
														min="0"
														name="invoice3"
														step="10"
														type="number"
													/>
												</div>
											</div>
											<div class="w-full flex border-0 border-b border-b-neutral-400">
												<div class="w-1/6 flex items-center justify-center bg-neutral-300 text-black font-bold">
													총 청구비용
												</div>
												<div class="w-5/6 p-2">
													<input
														bind:this={invoiceTotalReadonly}
														class="input input-bordered input-sm w-full text-right"
														class:bg-base-300={invoiceTotalReadonly}
														id="total_invoice"
														max="99999999"
														min="0"
														name="total_invoice"
														step="10"
														type="number"
														value={invoice1 + invoice2 + invoice3}
													/>
												</div>
											</div>
											<div class="w-full flex">
												<div class="w-1/6 flex items-center justify-center bg-neutral-300 text-black font-bold">
													메모
												</div>
												<div class="w-5/6 p-2">
													<textarea
														bind:value={memo}
														class="textarea textarea-bordered w-full"
														id="memo"
														name="memo"
														placeholder="작성내용은 처리내용 뒤에 첨부됩니다"
													></textarea>
												</div>
											</div>
										</div>
									</div>
									<div class="w-full p-2 flex">
										<div class="w-1/2 flex items-center justify-center">
											<button
												class="btn btn-lg"
												class:btn-default={!isCompleteButtonSuccess}
												class:btn-success={isCompleteButtonSuccess}
												id="complete_btn"
												onclick={completeCheckIn}
												type="button"
											>
												<Icon data={faBoxesPacking} />
												점검완료
											</button>
										</div>
										<div class="w-1/2 flex items-center justify-center">
											<button
												class="btn btn-neutral btn-lg"
												id="cancel_btn"
												onclick={cancelCheckIn}
												type="button"
											>
												<Icon data={faBan} />
												점검취소
											</button>
										</div>
									</div>
								</div>
							</div>
						</div>

						<div class="py-3"></div>

						<div class="w-full border border-neutral-400">
							<div class="w-full p-3 flex flex-col items-center justify-center">
								<div
									class="w-full p-2 flex items-center justify-center text-2xl cursor-pointer"
									id="command_barcode_title"
									onclick={() => (commandVisible = !commandVisible)}
									role="presentation"
								>
									<Icon data={faBell} scale={2} />
									명령어 바코드
								</div>

								{#if commandVisible}
									<div class="w-full flex flex-col border border-neutral-400">
										<div class="w-full flex flex-row">
											<div class="w-1/4 flex items-center p-2 border-b border-r border-neutral-400 bg-neutral-300 text-black font-bold">
												팔레트교체
											</div>
											<div class="w-1/4 flex items-center justify-center p-2 border-b border-neutral-400">
												<Barcode
													id="barcode1"
													value="change_pallet/UX-405"
													options={{ fontOptions: 'bold' }}
												/>
											</div>
											<div class="w-2/4 flex items-center p-2 border-b border-l border-neutral-400 text-sm">
												'change_pallet/UX-405'와 같이 'change_pallet/'다음에 해당팔레트번호를 추가한
												값을 스캔(입력)하거나 그냥 'UX-405'와 같은 팔레트번호를 스캔(입력)합니다.
											</div>
										</div>

										<div class="w-full flex flex-row">
											<div class="w-1/4 flex items-center p-2 border-b border-r border-neutral-400 bg-neutral-300 text-black font-bold">
												점검완료
											</div>
											<div class="w-1/4 flex items-center justify-center p-2 border-b border-neutral-400">
												<Barcode id="barcode2" value="complete" options={{ fontOptions: 'bold' }} />
											</div>
											<div class="w-2/4 flex items-center p-2 border-b border-l border-neutral-400 text-sm">
												점검완료를 진행합니다. 해당 번호의 팔레트에 적재합니다.
											</div>
										</div>

										<div class="w-full flex flex-row">
											<div class="w-1/4 flex items-center p-2 border-b border-r border-neutral-400 bg-neutral-300 text-black font-bold">
												취소
											</div>
											<div class="w-1/4 flex items-center justify-center p-2 border-b border-neutral-400">
												<Barcode id="barcode3" value="cancel" options={{ fontOptions: 'bold' }} />
											</div>
											<div class="w-2/4 flex items-center p-2 border-b border-l border-neutral-400 text-sm">
												점검취소를 진행합니다.
											</div>
										</div>

										<div class="w-full flex flex-row">
											<div class="w-1/4 flex items-center p-2 border-b border-r border-neutral-400 bg-neutral-300 text-black font-bold">
												증상내용
											</div>
											<div class="w-1/4 flex items-center justify-center p-2 border-b border-neutral-400">
												<Barcode
													id="barcode4"
													value="check/CH_OK"
													options={{ fontOptions: 'bold' }}
												/>
											</div>
											<div class="w-2/4 flex items-center p-2 border-b border-l border-neutral-400 text-sm">
												'check/CH_OK'와 같이 'check/'다음에 증상내용코드를 추가한 값을
												스캔(입력)합니다.
											</div>
										</div>

										<div class="w-full flex flex-row">
											<div class="w-1/4 flex items-center p-2 border-b border-r border-neutral-400 bg-neutral-300 text-black font-bold">
												처리내용
											</div>
											<div class="w-1/4 flex items-center justify-center p-2 border-b border-neutral-400">
												<Barcode
													id="barcode5"
													value="repair/RP_CONFIRM"
													options={{ fontOptions: 'bold' }}
												/>
											</div>
											<div class="w-2/4 flex items-center p-2 border-b border-l border-neutral-400 text-sm">
												'repair/RP_CONFIRM'과 같이 'repair/'다음에 처리내용코드를 추가한 값을
												스캔(입력)합니다.
											</div>
										</div>

										<div class="w-full flex flex-row">
											<div class="w-1/4 flex items-center p-2 border-b border-r border-neutral-400 bg-neutral-300 text-black font-bold">
												수리상태
											</div>
											<div class="w-1/4 flex items-center justify-center p-2 border-b border-neutral-400">
												<Barcode
													id="barcode6"
													value="grade/ST_BEST"
													options={{ fontOptions: 'bold' }}
												/>
											</div>
											<div class="w-2/4 flex items-center p-2 border-b border-l border-neutral-400 text-sm">
												'grade/ST_BEST'와 같이 'grade/'다음에 수리상태코드를 추가한 값을
												스캔(입력)합니다.
											</div>
										</div>

										<div class="w-full flex flex-row">
											<div class="w-1/4 flex items-center p-2 border-b border-r border-neutral-400 bg-neutral-300 text-black font-bold">
												수리비용 사이즈기준 조회
											</div>
											<div class="w-1/4 flex items-center justify-center p-2 border-b border-neutral-400">
												<Barcode
													id="barcode7"
													value="basistype/size"
													options={{ fontOptions: 'bold' }}
												/>
											</div>
											<div class="w-2/4 flex items-center p-2 border-b border-l border-neutral-400 text-sm">
												'basistype/size'를 스캔(입력)하면 해당 상품의 크기에 따른 비용측정기준을
												조회합니다.
											</div>
										</div>

										<div class="w-full flex flex-row">
											<div class="w-1/4 flex items-center p-2 border-b border-r border-neutral-400 bg-neutral-300 text-black font-bold">
												센티(cm)단위 수리비용 조회
											</div>
											<div class="w-1/4 flex items-center justify-center p-2 border-b border-neutral-400">
												<Barcode
													id="barcode8"
													value="basisunit/cm"
													options={{ fontOptions: 'bold' }}
												/>
											</div>
											<div class="w-2/4 flex items-center p-2 border-b border-l border-neutral-400 text-sm">
												'basisunit/cm'를 스캔(입력)하면 해당 상품명에서 cm값을 읽어서 크기비용을
												조회합니다.
											</div>
										</div>

										<div class="w-full flex flex-row">
											<div class="w-1/4 flex items-center p-2 border-b border-r border-neutral-400 bg-neutral-300 text-black font-bold">
												인치(inch)단위 수리비용 조회
											</div>
											<div class="w-1/4 flex items-center justify-center p-2 border-b border-neutral-400">
												<Barcode
													id="barcode9"
													value="basisunit/inch"
													options={{ fontOptions: 'bold' }}
												/>
											</div>
											<div class="w-2/4 flex items-center p-2 border-b border-l border-neutral-400 text-sm">
												'basisunit/inch'를 스캔(입력)하면 해당 상품명에서 inch(또는 인치 또는 그냥
												숫자)값을 읽어서 크기비용을 조회합니다.
											</div>
										</div>

										<div class="w-full flex flex-row">
											<div class="w-1/4 flex items-center p-2 border-b border-r border-neutral-400 bg-neutral-300 text-black font-bold">
												수리비용 금액기준 조회
											</div>
											<div class="w-1/4 flex items-center justify-center p-2 border-b border-neutral-400">
												<Barcode
													id="barcode10"
													value="basistype/price"
													options={{ fontOptions: 'bold' }}
												/>
											</div>
											<div class="w-2/4 flex items-center p-2 border-b border-l border-neutral-400 text-sm">
												'basistype/price'를 스캔(입력)하면 해당 상품의 판매가에 따른 비용을
												조회합니다.
											</div>
										</div>

										<div class="w-full flex flex-row">
											<div class="w-1/4 flex items-center p-2 border-b border-r border-neutral-400 bg-neutral-300 text-black font-bold">
												금액(원)단위 수리비용 조회
											</div>
											<div class="w-1/4 flex items-center justify-center p-2 border-b border-neutral-400">
												<Barcode
													id="barcode11"
													value="basisunit/won"
													options={{ fontOptions: 'bold' }}
												/>
											</div>
											<div class="w-2/4 flex items-center p-2 border-b border-l border-neutral-400 text-sm">
												'basisunit/won'을 스캔(입력)하면 해당 상품의 판매가에 따른 비용을 조회를
												위한 측정단위를 변경합니다.
											</div>
										</div>

										<div class="w-full flex flex-row">
											<div class="w-1/4 flex items-center p-2 border-b border-r border-neutral-400 bg-neutral-300 text-black font-bold">
												수리비용 공통기준 조회
											</div>
											<div class="w-1/4 flex items-center justify-center p-2 border-b border-neutral-400">
												<Barcode
													id="barcode12"
													value="basistype/none"
													options={{ fontOptions: 'bold' }}
												/>
											</div>
											<div class="w-2/4 flex items-center p-2 border-b border-l border-neutral-400 text-sm">
												'basistype/none'을 스캔(입력)하면 해당 상품의 일반(기준없는)비용을
												조회합니다.
											</div>
										</div>

										<div class="w-full flex flex-row">
											<div class="w-1/4 flex items-center p-2 border-b border-r border-neutral-400 bg-neutral-300 text-black font-bold">
												OS설치비용
											</div>
											<div class="w-1/4 flex items-center justify-center p-2 border-b border-neutral-400">
												<Barcode
													id="barcode13"
													value="osinstall"
													options={{ fontOptions: 'bold' }}
												/>
											</div>
											<div class="w-2/4 flex items-center p-2 border-b border-l border-neutral-400 text-sm">
												'osinstall'을 스캔(입력)하면 운영체재 재설치를 완료한 경우 비용청구를
												추가합니다.
											</div>
										</div>

										<div class="w-full flex flex-row">
											<div class="w-1/4 flex items-center p-2 border-b border-r border-neutral-400 bg-neutral-300 text-black font-bold">
												별도수리비용
											</div>
											<div class="w-1/4 flex items-center justify-center p-2 border-b border-neutral-400">
												<Barcode
													id="barcode14"
													value="fix/FX_CABLE@3000"
													options={{ fontOptions: 'bold' }}
												/>
											</div>
											<div class="w-2/4 flex items-center p-2 border-b border-l border-neutral-400 text-sm">
												'fix/FX_CABLE@3000'와 같이 'fix/'다음에 추가비용코드 뒤에 '@'를 붙이고, 그
												뒤에 별도수리금액을 숫자값으로 추가한 바코드를 스캔(입력)합니다.
											</div>
										</div>

										<div class="w-full flex flex-row">
											<div class="w-1/4 flex items-center p-2 border-b border-r border-neutral-400 bg-neutral-300 text-black font-bold">
												기타추가비용
											</div>
											<div class="w-1/4 flex items-center justify-center p-2 border-b border-neutral-400">
												<Barcode
													id="barcode15"
													value="charge/CG_BOX@800"
													options={{ fontOptions: 'bold' }}
												/>
											</div>
											<div class="w-2/4 flex items-center p-2 border-b border-l border-neutral-400 text-sm">
												'charge/CG_BOX@800'와 같이 'charge/'다음에 추가비용코드 뒤에 '@'를 붙이고,
												그 뒤에 비용금액을 숫자값으로 추가한 바코드를 스캔(입력)합니다.
											</div>
										</div>
									</div>
								{/if}
							</div>
						</div>
					</div>

					<div class="w-[300px] pl-4 flex flex-col">
						<div class="w-full border">
							<div class="w-full p-2 bg-neutral-300 border-b-2 border-b-neutral-400 text-black font-bold">
								<Icon data={faDolly} />
								{#if palletCode}
									<span class="underline">{palletCode}</span>
								{:else}
									-
								{/if}
								최근 적재
							</div>

							<div class="p-2">
								<div class="w-full flex items-center justify-center text-[150px] font-bold text-red-600">
									{#if palletProdCount}
										{palletProdCount}
									{:else}
										0
									{/if}
								</div>
							</div>

							<div class="p-2 text-xs">
								{#if palletRecentProducts.length > 0}
									{#each palletRecentProducts as product, i}
										<p class="pt-2">[{palletProdCount - i}] {product}</p>
									{/each}
								{/if}
							</div>
						</div>
					</div>
				</div>
			</section>

			<dialog class="modal" id="my_modal_1">
				<div class="modal-box w-1/2 max-w-2xl">
					<form method="dialog">
						<button class="btn btn-sm btn-circle btn-ghost absolute right-2 top-2">✕</button>
					</form>

					<!-- 여기 내용-->
					<div class="py-5">
						<span class="text-3xl text-red-700">{modalErrorMessage}</span>
					</div>

					<div class="modal-action w-full flex items-center justify-center">
						<form method="dialog">
							<!-- if there is a button in form, it will close the modal -->
							<button
								class="btn btn-warning tooltip tooltip-top"
								data-tip="키보드의 Escape 키를 누르면 닫힙니다."
							>
								<Icon class="w-5 h-5" data={faXmark} />
								닫기
							</button>
						</form>
					</div>
				</div>
			</dialog>
		</div>
	{/snippet}
</AppLayout>
