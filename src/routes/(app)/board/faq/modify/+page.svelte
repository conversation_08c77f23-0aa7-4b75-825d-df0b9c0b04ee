<script lang="ts">
	import '$components/Tiptap/tiptap.pcss';

	import { onMount } from 'svelte';
	import { getUser, type User } from '$lib/User';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import type { Breadcrumb } from '$lib/types';

	import { authClient } from '$lib/AxiosBackend';
	import { toast } from 'svoast';

	import { executeMessage, handleCatch } from '$lib/Functions';

	import AppLayout from '$components/Layouts/AppLayout.svelte';
	import Loading from '$components/Loading/Circle2.svelte';
	import TitleBar from '$components/UI/TitleBar.svelte';
	import Back from '$components/UI/Back.svelte';
	import Tiptap from '$components/Tiptap/Tiptap.svelte';

	import Icon from 'svelte-awesome';
	import { faSave } from '@fortawesome/free-regular-svg-icons/faSave';
	import { faCancel } from '@fortawesome/free-solid-svg-icons/faCancel';

	const user: User = getUser();

	const id = page.url.searchParams.get('id');
	const searchParams = page.url.search;
	const apiUrl = `/wms/faqs/${id}`;
	const prevUrl = `/board/faq/view${searchParams}`;
	let isLoading = $state(false); // 로딩중 표시 여부

	let editor: Tiptap = $state();
	let article;
	let subject = $state('');
	let cate1 = $state('');
	let cate2 = $state('');
	let solutionCode = $state('');
	let content = $state(''); // 수정시 사용
	let editorContent = ''; // editor의 content 저장

	async function makeData() {
		isLoading = true;

		try {
			const { status, data } = await authClient.get(apiUrl);
			if (status === 200 && data.success) {
				article = data.data.article;

				subject = article.subject;
				content = article.content;
				cate1 = article.cate1;
				cate2 = article.cate2 ?? '';
				solutionCode = article.solution_code;
			}
		} catch (e: any) {
			await handleCatch(e);
		}

		isLoading = false;
	}

	async function handleSubmit(event: Event) {
		event.preventDefault();
		isLoading = true;

		if (!subject) {
			await executeMessage('제목을 입력해 주세요.');
			isLoading = false;
			return false;
		}

		if (!cate1) {
			await executeMessage('분류1을 입력해 주세요.');
			isLoading = false;
			return false;
		}

		if (!solutionCode) {
			await executeMessage('질문유형을 입력해 주세요.');
			isLoading = false;
			return false;
		}

		if (editor) {
			editorContent = editor.getHTML() as string;
		}
		if (!editorContent) {
			await executeMessage('내용을 입력해 주세요.');
			isLoading = false;
			return false;
		}

		try {
			const payload = {
				user: user,
				subject: subject,
				content: editorContent,
				cate1: cate1,
				cate2: cate2,
				'solution_code': solutionCode
			};

			const { status, data } = await authClient.put(apiUrl, payload);
			if (status === 200 && data.success) {
				toast.success('수정 되었습니다.');

				await goto(prevUrl);
			} else {
				toast.error(data.error);
			}
		} catch (e: any) {
			await handleCatch(e);
		}

		isLoading = false;
	}

	onMount(async () => {
		await makeData();
	});
	
	const breadcrumbs: Breadcrumb[] = [
		{ title: 'FAQ', url: '/board/faq' },
		{ title: 'FAQ 리스트', url: '/board/faq' },
		{ title: 'FAQ 수정', url: '/board/faq/modify' }
	];
</script>

<svelte:head>
	<title>FAQ > FAQ 수정</title>
</svelte:head>

{#if isLoading}
	<Loading size="60" unit="px" />
{/if}

<AppLayout {user}>
	{#snippet main()}
		<div >
			<TitleBar {breadcrumbs} />
			
			<section class="main-section">
				<Back />
				
				<div class="w-full px-3">
					<div class="w-full flex flex-col p-4 shadow-lg max-w-4xl">
						<div class="w-full">
							<input bind:value={subject}
										 class="input input-bordered w-full outline-none"
										 placeholder="제목"
										 type="text"
							>
						</div>
						
						<div class="py-1"></div>
						
						<div class="w-full flex flex-row">
							<div class="w-1/2 flex flex-row">
								<div class="w-1/4 flex items-center">
									* 분류 1(필수):
								</div>
								
								<div class="w-3/4">
									<select bind:value={cate1} class="select select-bordered">
										<option value="">선택</option>
										<option value="공기청정기">공기청정기</option>
										<option value="김치냉장고">김치냉장고</option>
										<option value="냉장고">냉장고</option>
										<option value="뉴트리불렛">뉴트리불렛</option>
										<option value="세탁기/건조기">세탁기/건조기</option>
										<option value="에어워셔/스포워셔">에어워셔/스포워셔</option>
										<option value="에어컨">에어컨</option>
										<option value="에어프라이어">에어프라이어</option>
										<option value="전기밥솥">전기밥솥</option>
										<option value="전기주전자">전기주전자</option>
										<option value="전자레인지/오븐">전자레인지/오븐</option>
										<option value="정수기">정수기</option>
										<option value="제습기">제습기</option>
									</select>
								</div>
							</div>
							
							<div class="w-1/2 flex flex-row">
								<div class="w-1/4 flex items-center">
									분류 2:
								</div>
								
								<div class="w-3/4">
									<select bind:value={cate2} class="select select-bordered">
										<option value="">선택</option>
										<option value="가정용건조기">가정용건조기</option>
										<option value="가정용세탁기">가정용세탁기</option>
										<option value="냉동고">냉동고</option>
										<option value="딤채쿡">딤채쿡</option>
										<option value="뚜껑형">뚜껑형</option>
										<option value="벽걸이에어컨">벽걸이에어컨</option>
										<option value="스탠드에어컨">스탠드에어컨</option>
										<option value="스탠드형">스탠드형</option>
										<option value="업소용냉장고">업소용냉장고</option>
										<option value="이동형에어컨">이동형에어컨</option>
										<option value="이온정수기">이온정수기</option>
										<option value="일반냉장고">일반냉장고</option>
										<option value="일반정수기">일반정수기</option>
										<option value="창문형에어컨">창문형에어컨</option>
										<option value="천장형에어컨">천장형에어컨</option>
										<option value="프리미엄냉장고">프리미엄냉장고</option>
									</select>
								</div>
							</div>
						</div>
						
						<div class="py-1"></div>
						
						<div class="w-1/2 flex flex-row">
							<div class="w-1/4 flex items-center">
								질문 유형:
							</div>
							
							<div class="w-3/4">
								<select bind:value={solutionCode} class="select select-bordered">
									<option value="">선택</option>
									<option value="관리방법">관리방법</option>
									<option value="기능">기능</option>
									<option value="기타">기타</option>
									<option value="내용물">내용물</option>
									<option value="사용법">사용법</option>
									<option value="성능">성능</option>
									<option value="소음">소음</option>
									<option value="오류코드">오류코드</option>
									<option value="외관/구조">외관/구조</option>
									<option value="증상">증상</option>
								</select>
							</div>
						</div>
						
						<div class="py-1"></div>
						
						<div class="w-full h-[630px] flex flex-col">
							<Tiptap bind:this={editor} content={content} />
						</div>
						
						<!-- Buttons -->
						<div class="w-full flex items-center justify-center">
							<div class="flex">
								<button class="btn btn-primary"
												onclick={handleSubmit}
												type="button"
								>
									<Icon data={faSave} />
									수정
								</button>
								
								<div class="px-2"></div>
								
								<button class="btn btn-secondary"
												onclick={() => goto(prevUrl)}
												type="button"
								>
									<Icon data={faCancel} />
									취소
								</button>
							</div>
						</div>
					</div>
				</div>
			</section>
		</div>
	{/snippet}
</AppLayout>