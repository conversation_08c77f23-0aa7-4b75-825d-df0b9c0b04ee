<script lang="ts">
	import '$components/Tiptap/tiptap.pcss';

	import { getUser, type User } from '$lib/User';
	import { onMount } from 'svelte';
	import { page } from '$app/state';
	import { goto } from '$app/navigation';
	import type { Breadcrumb } from '$lib/types';

	import { handleCatch } from '$lib/Functions';

	import { authClient } from '$lib/AxiosBackend';
	import { toast } from 'svoast';
	
	import AppLayout from '$components/Layouts/AppLayout.svelte';
	import Loading from '$components/Loading/Circle2.svelte';
	import TitleBar from '$components/UI/TitleBar.svelte';

	import Icon from 'svelte-awesome';
	import { faList } from '@fortawesome/free-solid-svg-icons/faList';
	import { faEdit } from '@fortawesome/free-solid-svg-icons/faEdit';
	import { faTrash } from '@fortawesome/free-solid-svg-icons/faTrash';
	import { faLayerGroup } from '@fortawesome/free-solid-svg-icons/faLayerGroup';
	import { faFontAwesome } from '@fortawesome/free-solid-svg-icons/faFontAwesome';

	const user: User = getUser();

	const id = page.url.searchParams.get('id');
	const searchParams = page.url.search;
	const apiUrl = `/wms/faqs/${id}`;
	const prevUrl = `/board/faq${searchParams}`;
	let isLoading = $state(false); // 로딩중 표시 여부

	let article = $state('');

	async function makeData() {
		isLoading = true;

		const { status, data } = await authClient.get(apiUrl);
		if (status === 200 && data.success) {
			article = data.data.article;
		}

		isLoading = false;
	}

	async function handleDelete() {
		isLoading = true;

		try {
			const { status, data } = await authClient.delete(apiUrl);
			
			if (status === 200 && data.success) {
				toast.success('삭제 되었습니다.');

				await goto('/board/faq');
			} else {
				toast.error(data.error);
			}
		} catch (e: any) {
			await handleCatch(e);
		}

		isLoading = false;
	}

	onMount(async () => {
		await makeData();
	});

	const breadcrumbs: Breadcrumb[] = [
		{ title: 'FAQ', url: '/board/faq' },
		{ title: 'FAQ 리스트', url: '/board/faq' },
		{ title: '솔루션 보기', url: '#' }
	];
</script>

<svelte:head>
	<title>FAQ > 솔루션 보기</title>
</svelte:head>

{#if isLoading}
	<Loading size="60" unit="px" />
{/if}

<AppLayout {user}>
	{#snippet main()}
		<div >
			<TitleBar {breadcrumbs} />
			
			<section class="main-section">
				<div class="w-full px-3">
					<div class="shadow-xl">
						&nbsp;
					</div>
					
					<div class="w-full pt-2 pb-4 flex flex-col border-0 border-b border-b-neutral-500">
						<div class="w-full pl-4 text-xl font-bold">
							{article.subject}
						</div>
						
						<div class="divider"></div>
						
						<div class="w-full flex items-center">
							<span class="px-3 text-sm">
								<Icon data={faLayerGroup} />
								<span class="align-middle">{article.cate1} > {article.cate2}</span>
							</span>
							
							<span class="px-3 text-sm">
								<Icon data={faFontAwesome} />
								<span class="align-middle">{article.solution_code}</span>
							</span>
						</div>
					</div>
					
					<div class="tiptap p-4 shadow-xl">
						{@html article.content}
					</div>
					
					<!-- 버튼 시작 -->
					<div class="w-full flex flex-col">
						<div class="w-full py-2 flex justify-end">
							<div class="px-2">
								<button class="btn btn-accent btn-sm"
												onclick={() => goto(prevUrl)}
												type="button"
								>
									<Icon data={faList} />
									목록
								</button>
							</div>
							{#if user.role === 'Super-Admin' || user.role === 'Admin'}
								<div class="px-2">
									<button class="btn btn-info btn-sm"
													onclick={() => goto(`/board/faq/modify${searchParams}`)}
													type="button"
									>
										<Icon data={faEdit} />
										수정
									</button>
								</div>
								<div class="px-2">
									<button class="btn btn-error btn-sm"
													onclick={handleDelete}
													type="button"
									>
										<Icon data={faTrash} />
										삭제
									</button>
								</div>
							{/if}
						</div>
					</div>
					<!-- 버튼 종료 -->
				</div>
			</section>
		</div>
	{/snippet}
</AppLayout>