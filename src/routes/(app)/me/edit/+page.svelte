<script lang="ts">
	import type { Breadcrumb } from '$lib/types';

	import { executeMessage, handleCatch, redirectUrl } from '$lib/Functions';
	import { getUser, removeUser, setUser } from '$lib/User';
	import { authClient } from '$lib/AxiosBackend';

	import AppLayout from '$components/Layouts/AppLayout.svelte';
	import TitleBar from '$components/UI/TitleBar.svelte';
	import Back from '$components/UI/Back.svelte';

	import Icon from 'svelte-awesome';
	import { faSave } from '@fortawesome/free-regular-svg-icons/faSave';

	const user = getUser();

	let id = user?.id;
	let name = $state(user?.name);
	let email = $state(user?.email);
	let password = $state('');
	let passwordConfirmation = $state('');
	let cellphone = $state(user?.cellphone);

	// 직원 정보 수정 Submit
	async function handleSubmit(event: SubmitEvent) {
		event.preventDefault();

		if (password !== passwordConfirmation) {
			await executeMessage('비밀번호와 확인란의 비밀번호가 다릅니다.', 'warning');

			passwordConfirmation = '';
			return false;
		}

		if (!name) {
			await executeMessage('이름을 입력해 주세요.');
			return false;
		}

		try {
			const payload = {
				id,
				password,
				password_confirmation: passwordConfirmation,
				name,
				email,
				cellphone
			};

			const endpoint = `/wms/user/profile`;
			const response = await authClient.put(endpoint, payload);

			if (response.status === 200 && response.data.success) {
				await executeMessage('수정 되었습니다.');

				// 수정된 정보를 바탕으로 세션스토리지의 정보를 업데이트
				const updateUser = {
					id: user?.id,
					company_id: user?.company_id,
					role: user?.role,
					username: user?.username,
					name,
					email,
					cellphone,
					status: user?.status,
					login_at: user?.login_at,
					login_ip: user?.login_ip,
					login_os: user?.login_os,
					created_at: user?.created_at,
					updated_at: user?.updated_at
				};

				await removeUser().then(async () => {
					await setUser(updateUser).then(() => {
						redirectUrl('/me/edit');
					});
				});
			} else {
				const errors = response.data.data.errors;
				const keys = ['password', 'name', 'email', cellphone];

				for (const key of keys) {
					if (errors[key]) {
						await executeMessage(errors[key], 'error');
					}
				}
			}
		} catch (e: any) {
			await handleCatch(e);
		}
	}

	const breadcrumbs: Breadcrumb[] = [
		{ title: '내 정보 수정', url: '/me/edit/#' },
	];
</script>

<svelte:head>
	<title>내 정보 수정</title>
</svelte:head>

<AppLayout {user}>
	{#snippet main()}
		<div >
			<TitleBar {breadcrumbs} />

			<section class="main-section">
				<Back />

				<div class="p-4 md:px-6 2xl:px-0 flex justify-start items-center 2xl:container">
					<div class="w-full 2xl:w-1/2 flex flex-col justify-start items-start space-y-9">
						<div
							class="w-full 2xl:mx-auto flex flex-col xl:flex-row justify-center space-y-6 xl:space-y-0 xl:space-x-6 bg-base-200 rounded-lg"
						>
							<form
								class="flex flex-col p-8 w-full space-y-4 md:space-y-0 items-center"
								id="user_form"
								onsubmit={handleSubmit}
							>
								<div class="pt-7 w-full flex flex-col">
									<div>
										<p class="font-extrabold text-xl underline underline-offset-8">나의 정보</p>
									</div>

									<div class="pt-2 w-full flex flex-col">
										<div class="w-full">
											<label class="label" for="username">
												<span class="label-text">아이디(ID)</span>
											</label>

											<div class="w-full inline-flex">
												<span class="p-2">
													{user?.username}
												</span>
											</div>
										</div>

										<div class="w-full pt-2">
											<label class="label justify-start" for="password">
												<span class="label-text">비밀번호(변경시에만 입력)</span>
											</label>

											<div class="w-full inline-flex">
												<input
													bind:value={password}
													class="input input-bordered w-full rounded-lg p-2 leading-4 placeholder-base-content focus:ring-base-300 focus:border-base-300"
													id="password"
													name="password"
													type="password"
												/>
											</div>

											<div>
												<span class="pl-4 text-sm">
													비밀번호는 8자 이상, 숫자 + 영문자(대) + 영문자(소) + 특수문자가 1개 이상
													포함 되어야 합니다.
												</span>
											</div>
										</div>

										<div class="w-full pt-2">
											<label class="label justify-start" for="password_confirmation">
												<span class="label-text">비밀번호 확인(변경시에만 입력)</span>
											</label>

											<div class="w-full inline-flex">
												<input
													bind:value={passwordConfirmation}
													class="input input-bordered w-full rounded-lg p-2 leading-4 placeholder-base-content focus:ring-base-300 focus:border-base-300"
													id="password_confirmation"
													name="password_confirmation"
													type="password"
												/>
											</div>
										</div>

										<div class="w-full pt-2">
											<label class="label" for="name">
												<span class="label-text">이름</span>
											</label>

											<div class="w-full inline-flex">
												<input
													bind:value={name}
													class="input input-bordered w-full rounded-lg p-2 leading-4 placeholder-base-content focus:ring-base-300 focus:border-base-300"
													id="name"
													name="name"
													required
													type="text"
												/>
											</div>
										</div>

										<div class="w-full pt-2">
											<label class="label" for="email">
												<span class="label-text">이메일(E-mail)</span>
											</label>

											<div class="w-full inline-flex">
												<input
													bind:value={email}
													class="input input-bordered w-full rounded-lg p-2 leading-4 placeholder-base-content focus:ring-base-300 focus:border-base-300"
													id="email"
													name="email"
													type="email"
												/>
											</div>
										</div>

										<div class="w-full pt-2">
											<label class="label" for="cellphone">
												<span class="label-text">연락처(휴대폰)</span>
											</label>

											<div class="w-full inline-flex">
												<input
													bind:value={cellphone}
													class="input input-bordered w-full rounded-lg p-2 leading-4 placeholder-base-content focus:ring-base-300 focus:border-base-300"
													id="cellphone"
													name="cellphone"
													required
													type="text"
												/>
											</div>
										</div>
									</div>
								</div>

								<div class="divider py-7"></div>

								<div class="w-full">
									<button class="btn btn-primary w-full rounded-lg" type="submit">
										<Icon data={faSave} />
										정보 수정
									</button>
								</div>
							</form>
						</div>
					</div>
				</div>
			</section>
		</div>
	{/snippet}
</AppLayout>
