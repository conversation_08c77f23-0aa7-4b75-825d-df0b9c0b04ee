<script lang="ts">
	import type { Breadcrumb } from '$lib/types';

	import { onMount } from 'svelte';
	import { getUser, type User } from '$lib/User';
	import { page } from '$app/state';
	import { authClient } from '$lib/AxiosBackend';
	import { isRePrint } from '$lib/BarcodeUtils';

	import { getProcessGradeColorButton } from '$stores/processStore';

	import {
		executeMessage,
		formatDateTimeToFullString,
		formatDateTimeToString,
		generateRandomNumber,
		getNumberFormat,
		handleCatch,
		isOverDueDate,
		openWindow
	} from '$lib/Functions';
	
	import { getProductCheckedStatusName, loadProduct, productStore } from '$stores/productStore';

	import AppLayout from '$components/Layouts/AppLayout.svelte';
	import TitleBar from '$components/UI/TitleBar.svelte';
	import Barcode from '$components/Snippets/Barcode.svelte';
	import Paginate from '$components/UI/Paginate.svelte';
	import DisplayKeyword from '$components/Snippets/DisplayKeyword.svelte';
	import SearchUI from '$components/UI/SearchUI.svelte';
	import TableTop from '$components/UI/TableTop.svelte';

	import Icon from 'svelte-awesome';
	import { faPrint } from '@fortawesome/free-solid-svg-icons/faPrint';
	import { faBell } from '@fortawesome/free-regular-svg-icons/faBell';
	import { faBarcode } from '@fortawesome/free-solid-svg-icons/faBarcode';
	import { faBan } from '@fortawesome/free-solid-svg-icons/faBan';
	import { faBoxesPacking } from '@fortawesome/free-solid-svg-icons/faBoxesPacking';
	import { faRocket } from '@fortawesome/free-solid-svg-icons/faRocket';
	import { faXmark } from '@fortawesome/free-solid-svg-icons/faXmark';
	import { faCopyright } from '@fortawesome/free-regular-svg-icons/faCopyright';

	import audioCheckLocationStore from '$lib/assets/audio/check_location_store.mp3';
	import audioCompleteCheckIn from '$lib/assets/audio/complete_checkin_audio.mp3';
	import audioCompletingCheckIn from '$lib/assets/audio/completing_checkin_audio.mp3';
	import audioCompletingRepairIn from '$lib/assets/audio/completing_repair_in_audio.mp3';
	import audioPrintLabel from '$lib/assets/audio/print_label_audio.mp3';
	import audioScanBarcode from '$lib/assets/audio/scan_barcode_audio.mp3';
	import audioScanLabel from '$lib/assets/audio/scan_label_audio.mp3';
	import audioScanLevel from '$lib/assets/audio/scan_level_audio.mp3';
	import audioScanPallet from '$lib/assets/audio/scan_pallet_audio.mp3';
	import audioUnputableOnPackedPallet from '$lib/assets/audio/unputable_on_packed_pallet.mp3';
	import audioWriteLabel from '$lib/assets/audio/write_label_audio.mp3';
	import audioWrongLabel from '$lib/assets/audio/wrong_label_audio.mp3';
	import audioWrongPallet from '$lib/assets/audio/wrong_pallet_audio.mp3';
	import audioFailAndRetryAgain from '$lib/assets/audio/fail_and_retry_again.mp3';

	const audioFiles: Record<string, string> = {
		check_location_store: audioCheckLocationStore,
		complete_checkin_audio: audioCompleteCheckIn,
		completing_checkin_audio: audioCompletingCheckIn,
		completing_repair_in_audio: audioCompletingRepairIn,
		print_label_audio: audioPrintLabel,
		scan_barcode_audio: audioScanBarcode,
		scan_label_audio: audioScanLabel,
		scan_level_audio: audioScanLevel,
		scan_pallet_audio: audioScanPallet,
		unputable_on_packed_pallet: audioUnputableOnPackedPallet,
		write_label_audio: audioWriteLabel,
		wrong_label_audio: audioWrongLabel,
		wrong_pallet_audio: audioWrongPallet,
		fail_and_retry_again: audioFailAndRetryAgain
	};

	const user: User = getUser();
	const apiUrl = '/wms/repairs';
	const localUrl = page.url.pathname;
	let isLoading = $state(false); // 로딩중 표시 여부

	// 검색 query string 시작 ==========
	let keyword1 = $state(''); // 검색어(QAID, 바코드, 로트 번호)
	let keyword2 = $state(''); // 검색어(상품명)
	let display_keyword1 = $state('');
	let display_keyword2 = $state('');
	let p = $state(page.url.searchParams.get('p') || '1');
	let pageSize = $state(page.url.searchParams.get('pageSize') || '10');
	let searchParams = $state('');
	let apiSearchParams = '';
	// 검색 query string 종료 ==========

	// 페이징 관련 변수 시작 =============
	let links: [] = $state([]);
	let total = $state(0);
	let startNo = $state(0);
	// 페이징 관련 변수 종료 =============

	// 필수 변수 세팅 시작==================
	let showSymptomContent = $state(false); // 증상 내용
	let showProcessContent = $state(false); // 처리 내용
	let showGradeContent = $state(false); // 수리 등급
	let showPartsContent = $state(false); // 구성품 추가

	let prodScanMessage = $state('');

	// API에서 가져온 전체 수리 부품 목록
	let repairParts: any[] = $state([]);
	let repairPartsCategories: any[] = $state([]);
	
	// 작업자가 선택한 부품(parts)의 ID, 임시 저장
	let selectedParts = $state('');
	let selectedPartsCategory = $state('');

	// 저장될 구성품 데이터 타입 정의
	interface AddPart {
		id: number;
		quantity: number;
		price: number;
	}

	// 화면 표시용 데이터 타입 정의 (기존 SelectedPart 수정)
	interface SelectedPart {
		id: number;
		name: string;
		price: number;
	}

	// 실제로 추가된 화면 표시용 부품 리스트
	let selectedPartsList: SelectedPart[] = $state([]);

	let checkingBarcode = $state('');
	let checkingProduct: any[] = $state([]);
	let selectedSymptomCode = $state('');
	let selectedProcessCode = $state('');
	let selectedGradeCode = $state('');
	let repairStatus = $state('complete');
	let osReinstall = $state(false);
	let addParts: AddPart[] = $state([]); // API 전송용
	let memo = $state('');

	// 변수::입력 포커스, 읽기 전용, 메시지 표시, 버튼 활성화 변수
	let focusInput: HTMLElement; // 입고검수 input에 커서 포커싱
	let isFocusCheckingBarcode: HTMLElement; // 기본 input 포커싱
	let prodScanMessageOffClass = $state(false); // 스타일 변환
	let prodScanMessageOnClass = $state(false); // 스타일 변환
	let isCompleteButtonSuccess = $state(false); // 점검완료 버튼
	let completeButtonTitle = $state('점검(수리)완료');
	let commandVisible = $state(false); // 명령어 바코드
	// 필수 변수 세팅 종료==================

	// 모달창: 미등록상품 등록/수정 창 시작========
	let modal: HTMLDialogElement;
	let modalErrorMessage = $state('');

	// 모달창: 미등록상품 등록/수정 창 종료========

	async function makeData(search: boolean = false) {
		if ((!display_keyword1 || display_keyword1 === '') && (!display_keyword2 || display_keyword2 === '')) {
			await executeMessage('검색어를 입력해 주세요.', 'warning');
			return false;
		}

		isLoading = true;

		if (search) {
			p = '1';
		}

		const common_params = {
			searchType: 'all', // 외부업체(guest) 일 경우 qaid 로 변경할 것
			keyword1: display_keyword1,
			keyword2: display_keyword2,
			pageSize
		};

		const params = new URLSearchParams({ p: p, ...common_params });
		const api_params = new URLSearchParams({ page: p, ...common_params });

		searchParams = params.toString();
		apiSearchParams = api_params.toString();

		await loadProduct(`/wms/products/search?${apiSearchParams}`, user);

		if ($productStore) {
			links = JSON.parse($productStore.pageLinks);
			total = $productStore.pageTotal ?? 0;
			startNo = $productStore.pageTotal - $productStore.pageFrom + 1;
		}

		focusInput.focus();
		isLoading = false;
	}

	/**
	 * 자식 컴포넌트에서 변경된 변수의 값을 매칭
	 *
	 * @param value
	 */
	function changeSearchParams(value) {
		if (value.detail.p) p = value.detail.p;
		if (value.detail.keyword1 || value.detail.keyword1 === '') keyword1 = value.detail.keyword1.trim();
		if (value.detail.keyword2 || value.detail.keyword2 === '') keyword2 = value.detail.keyword2.trim();
		if (value.detail.pageSize) pageSize = value.detail.pageSize;

		makeData();
	}

	function handleError(message: string) {
		prodScanMessageOffClass = false;
		prodScanMessageOnClass = true;
		prodScanMessage = message;

		checkView();
		return true; // 에러 발생 시 true 반환
	}

	function checkView() {
		// playAudio('scan_barcode_audio');

		if (checkingProduct && checkingProduct.id) {
			showSymptomContent = true;

			// 이미 선택된 값이 있으면 해당 필드까지 표시
			if (selectedSymptomCode) showProcessContent = true;
			if (selectedProcessCode) showGradeContent = true;
			if (selectedGradeCode) showPartsContent = true;
		} else {
			// 상품 정보가 없으면 모든 필드 숨김
			showSymptomContent = false;
			showProcessContent = false;
			showGradeContent = false;
			showPartsContent = false;
		}

		checkingBarcode = '';
		isFocusCheckingBarcode.focus();
		osReinstall = false;
	}

	/**
	 * 작업간 모두 바코드를 이용해 처리한다.
	 * 바코드를 찍으면 실제 이 곳에서 분기하여 각각 처리
	 */
	const handleBarcodeInput = async (event?: Event) => {
		if (event) event.preventDefault();

		const barcode = checkingBarcode.trim();
		let prefixes = ['check/', 'repair/', 'grade/', 'fix/', 'charge/', 'memo/'];

		if (prefixes.some((prefix) => barcode.startsWith(prefix))) {
			selectProcess(barcode);
		} else if (barcode === 'osinstall') {
			osReinstall = true;
		} else if (barcode === 'complete' || barcode === 'waiting') {
			await completeCheckIn();
		} else if (barcode.startsWith('cancel')) {
			await cancelCheckIn();
		} else if (barcode === 'print/qaid') {
			// QAID 재발행
			if (checkingProduct && checkingProduct.qaid) {
				const qaid = checkingProduct.qaid;
				const id = checkingProduct.id;
				await isRePrint(qaid, id, user);
			} else {
				handleError('QAID를 먼저 검색해 주세요.');

				checkingBarcode = '';
				isFocusCheckingBarcode.focus();
			}
		} else {
			if (checkingBarcode !== '') {
				await getProductInfo();
			}
		}
	};

	let repairSymptomCodes: any[] = $state([]);

	// 입력된 바코드가 QAID일 경우 서버에서 상품정보를 가져온다.
	async function getProductInfo() {
		try {
			const { status, data } = await authClient.get(`${apiUrl}/check-product/${checkingBarcode}`);
			if (status === 200 && data.success) {
				checkingProduct = data.data.product;
				repairSymptomCodes = data.data.symptoms;

				// 구성품 신청으로 대기중인 상품 이었다면...
				if (checkingProduct.repair_product) {
					selectedSymptomCode = checkingProduct.repair_product.repair_symptom.code;
					memo = checkingProduct.repair_product.memo;
					
					if (selectedSymptomCode) {
						await getRepairProcesses();
						showProcessContent = true;
						selectedProcessCode = checkingProduct.repair_product.repair_process.code;

						// 증상내용이 구성품 부족일 경우에만 보일 것
						if (selectedProcessCode === 'CH_COMPO') {
							showPartsContent = true;
						}

						if (selectedProcessCode) {
							await getRepairGrades();
							showGradeContent = true;
							selectedGradeCode = checkingProduct.repair_product.repair_grade.code;
						}
					}
				} else {
					selectedSymptomCode = '';
					selectedProcessCode = '';
					selectedGradeCode = '';

					// 상품 정보를 가져 왔을 때 증상 내용만 보이게 설정
					showSymptomContent = true;
					showProcessContent = false;
					showGradeContent = false;
					showPartsContent = false;
				}

				// 검색도 같이 실행
				display_keyword1 = checkingBarcode;
				display_keyword2 = '';
				await makeData(true);

				keyword1 = '';
				keyword2 = '';
				checkView();
			} else {
				checkingProduct = [];
				showSymptomContent = false;
				showProcessContent = false;
				showGradeContent = false;
				showPartsContent = false;
				modalErrorMessage = data.data.message;
				modal.showModal();
				checkView();
			}
		} catch (e: any) {
			checkingProduct = [];
			showSymptomContent = false;
			showProcessContent = false;
			showGradeContent = false;
			showPartsContent = false;
			await handleCatch(e);
		}
	}
	
	async function getPartsCategories() {
		try {
			const { status, data } = await authClient.get(`${apiUrl}/parts-categories`);
			if (status === 200 && data.success) {
				repairPartsCategories = data.data.categories;
			}
		} catch (e) {
			await handleCatch(e);
		}
	}

	async function getParts() {
		if (selectedPartsCategory === '') {
			return;
		}
		
		try {
			const { status, data } = await authClient.get(`${apiUrl}/parts/${selectedPartsCategory}`);
			if (status === 200 && data.success) {
				repairParts = data.data.parts;
			}
		} catch (e) {
			await handleCatch(e);
		}
	}

	// 구성품 추가
	function handleAddParts() {
		if (selectedParts === '') {
			return;
		}

		// 선택된 구성품 정보 찾기
		const selectedPart = repairParts.find(p => p.id === selectedParts);
		if (selectedPart) {
			if (selectedPart.is_purchasable === 'N') {
				executeMessage('구매 불가능한 구성품으로 가공불가 처리됩니다.');

				// 가공불가로 처리
				selectedProcessCode = 'RP_ABANDON';
				getRepairGrades();
				
				// 변수 초기화
				selectedParts = '';
				selectedPartsCategory = '';
				repairParts = [];
				return;
			}

			// API 전송용 데이터 추가
			if (!addParts.some(p => p.id === selectedPart.id)) {
				addParts = [...addParts, {
					id: selectedPart.id,
					quantity: 1,
					price: selectedPart.price
				}];

				// 화면 표시용 데이터 추가
				selectedPartsList = [...selectedPartsList, {
					id: selectedPart.id,
					name: selectedPart.name,
					price: selectedPart.price
				}];
			}
		}

		// 선택 초기화
		selectedParts = '';
		selectedPartsCategory = '';
		repairParts = [];
	}

	async function cancelCheckIn() {
		clearValues();
	}

	async function completeCheckIn() {
		const product_id = checkingProduct.id;
		const repair_product_id = checkingProduct.repair_product?.id;
		const qaid = checkingProduct.qaid;

		if (
			typeof product_id !== 'undefined' &&
			selectedSymptomCode !== '' &&
			selectedProcessCode !== '' &&
			selectedGradeCode !== ''
		) {
			if (selectedProcessCode === 'RP_WAITING') {
				repairStatus = 'waiting';
			}
			
			let payload = {
				repair_product_id,
				product_id: product_id,
				qaid: qaid,
				status: repairStatus,
				symptom_code: selectedSymptomCode,
				process_code: selectedProcessCode,
				grade_code: selectedGradeCode,
				os_reinstall: osReinstall,
				add_parts: addParts,
				memo: memo
			};

			// playAudio('completing_repair_in_audio');

			try {
				const { status, data } = await authClient.post(`${apiUrl}/store`, payload);
				if (status === 200 && data.success) {
					await makeData();

					clearValues();
				} else {
					await executeMessage(data.data.message);
					// playAudio('fail_and_retry_again');
				}
			} catch (error) {
				// playAudio('fail_and_retry_again');
			}
		} else {
			await executeMessage('점검하려는 상품, 증상, 처리, 수리 상태를 모두 선택해 주세요.');
			checkView();
			return;
		}
	}

	let repairProcessCodes: any[] = $state([]);
	let defaultRepairProcessId = $state(0);
	let defaultRepairGradeId = $state(0);

	// 선택된 증상에 해당하는 처리 내용을 가져온다.
	async function getRepairProcesses() {
		try {
			selectedGradeCode = ''; // 선택된 증상이 바뀌면 수리 등급도 초기화
			
			const symptom = repairSymptomCodes.find((symptom) => symptom.code === selectedSymptomCode);
			defaultRepairProcessId = symptom?.default_repair_process_id || 0;
			defaultRepairGradeId = symptom?.default_repair_grade_id || 0;

			const { status, data } = await authClient.get(`${apiUrl}/code/processes/symptom/${symptom?.id}`);
			if (status === 200 && data.success) {
				repairProcessCodes = data.data.processes;

				// 기본 처리내용이 등록되어 있을 경우
				selectedProcessCode = repairProcessCodes.find((process) => process.id === defaultRepairProcessId)?.code || '';
				if (selectedProcessCode) {
					showGradeContent = true;
					await getRepairGrades();
				} else {
					showGradeContent = false;
				}
			}
		} catch (e: any) {
			showGradeContent = false;
			showGradeContent = false;
			await handleCatch(e);
		}
	}

	let repairGradeCodes: any[] = $state([]);

	// 선택된 처리 내용에 해당하는 수리 등급을 가져온다.
	async function getRepairGrades() {
		try {
			if (selectedProcessCode === 'RP_WAITING') {
				showPartsContent = true;
				completeButtonTitle = '구성품 (청구)대기';
			} else if (selectedProcessCode === 'RP_COMPO') {
				showPartsContent = true;
				completeButtonTitle = '점검(수리)완료';
			} else if (selectedProcessCode === 'RP_ABANDON') {
				showPartsContent = false;
				addParts = [];
				selectedPartsList = [];
			}

			const process_id = repairProcessCodes.find((process) => process.code === selectedProcessCode)?.id;
			const { status, data } = await authClient.get(`${apiUrl}/code/grade/process/${process_id}`);
			if (status === 200 && data.success) {
				repairGradeCodes = data.data.grades;

				// 기본 등급이 등록되어 있을 경우
				selectedGradeCode = repairGradeCodes.find((grade) => grade.id === defaultRepairGradeId)?.code || '';
			}
		} catch (e: any) {
			await handleCatch(e);
		}
	}

	// 핸들러 변수 초기화
	function clearHandler1() {
		selectedGradeCode = '';
		selectedProcessCode = '';
		selectedSymptomCode = '';
		showSymptomContent = false;
		showProcessContent = false;
		showGradeContent = false;
		showPartsContent = false;
	}

	function clearHandler2() {
		selectedGradeCode = '';
		selectedProcessCode = '';
		showProcessContent = false;
		showGradeContent = false;
		showPartsContent = false;
	}

	function clearHandler3() {
		selectedGradeCode = '';
		showGradeContent = false;
		showPartsContent = false;
	}

	function selectProcess(process_command: string) {
		prodScanMessage = '';

		const [process_type, process_code] = process_command.split('/');

		const handler = processHandlers[process_type];
		if (handler) {
			handler(process_code);
		} else {
			console.error('Unknown process type:', process_type);
		}
	}

	const errMessages = {
		check: '점검할 상품을 먼저 확인해 주시기 바랍니다.',
		symptom: '증상내용을 먼저 확인해 주시기 바랍니다.',
		process: '처리내용을 먼저 확인해 주시기 바랍니다.',
		grade: '수리상태를 먼저 확인해 주시기 바랍니다.'
	};

	// 수리 작업 처리 핸들러
	const processHandlers = {
		check: (process_code: string) => {
			if (typeof checkingProduct.id === 'undefined') {
				if (handleError(errMessages.check)) {
					checkingProduct = [];
					clearHandler1();
					return;
				}
			}

			selectedSymptomCode = process_code;
			checkingBarcode = '';
			// 증상 내용이 선택되면 처리 내용 표시
			showProcessContent = true;
			showGradeContent = false;
			showPartsContent = false;
			getRepairProcesses();
		},

		repair: (process_code: string) => {
			if (typeof checkingProduct.id === 'undefined') {
				if (handleError(errMessages.check)) {
					clearHandler1();
					return;
				}
			}

			if (!selectedSymptomCode && handleError(errMessages.symptom)) {
				clearHandler2();
				return;
			}

			selectedProcessCode = process_code;
			checkingBarcode = '';
			// 처리 내용이 선택되면 수리 상태 표시
			showGradeContent = true;
			console.log(selectedProcessCode);
			if (selectedProcessCode === 'RP_COMPO') {
				showPartsContent = true;
			}
			getRepairGrades();
		},

		grade: (process_code: string) => {
			if (typeof checkingProduct.id === 'undefined') {
				if (handleError(errMessages.check)) {
					clearHandler1();
					return;
				}
			}

			if (!selectedSymptomCode && handleError(errMessages.symptom)) {
				clearHandler2();
				return;
			}

			if (!selectedProcessCode && handleError(errMessages.process)) {
				clearHandler3();
				return;
			}

			selectedGradeCode = process_code;

			// checkInvoice('INV');
			// 수리 상태가 선택되면 구성품 표시
			if (selectedProcessCode === 'RP_COMPO') {
				showPartsContent = true;
			}
		},

		fix: (process_code: string) => {
			if (typeof checkingProduct.id === 'undefined') {
				if (handleError(errMessages.check)) {
					selectedGradeCode = selectedProcessCode = selectedSymptomCode = '';
					return;
				}
			}

			if (!selectedSymptomCode && handleError(errMessages.symptom)) {
				selectedGradeCode = selectedProcessCode = '';
				return;
			}

			if (!selectedProcessCode && handleError(errMessages.process)) {
				selectedGradeCode = '';
				return;
			}

			if (!selectedGradeCode && handleError(errMessages.grade)) return;

			addInvoiceMemo('fix', process_code);
		},

		charge: (process_code: string) => {
			if (typeof checkingProduct.id === 'undefined') {
				if (handleError(errMessages.check)) {
					selectedGradeCode = selectedProcessCode = selectedSymptomCode = '';
					return;
				}
			}

			if (!selectedSymptomCode && handleError(errMessages.symptom)) {
				selectedGradeCode = selectedProcessCode = '';
				return;
			}

			if (!selectedProcessCode && handleError(errMessages.process)) {
				selectedGradeCode = '';
				return;
			}

			if (!selectedGradeCode && handleError(errMessages.grade)) return;

			addInvoiceMemo('charge', process_code);
		},
		
		memo: (process_code: string) => {
			if (process_code === 'repaired') {
				addMemo('수리완료');
			}
		}
	};
	
	function addMemo(message: string) {
		memo = memo.trim();
		if (memo !== '') memo += '\n';
		memo += `${message}`;
		
		checkingBarcode = '';
		isFocusCheckingBarcode.focus();
	}

	// 기존 addCharge2Memo, addCharge3Memo
	async function addInvoiceMemo(process_type: string, charge_fee: string) {
		const [process_code, amount] = charge_fee.split('@');

		try {
			const { status, data } = await authClient.get(
				`${apiUrl}/other-expenses/${process_type}/${process_code}`
			);

			if (status === 200 && data.success) {
				const process_name = data.data.name ?? process_code.substring(3);
				memo += `${process_name} : ${amount} 원\n`;
			} else {
				await executeMessage(data.data.message);
			}
		} catch (e: any) {
			await handleCatch(e);
		}
	}

	function clearValues() {
		checkingBarcode = '';
		checkingProduct = [];
		selectedGradeCode = '';
		selectedProcessCode = '';
		selectedSymptomCode = '';
		memo = '';
		prodScanMessage = '';
		osReinstall = false;
		addParts = [];
		selectedPartsList = [];

		// 순차적 표시 상태 초기화
		showSymptomContent = false;
		showProcessContent = false;
		showGradeContent = false;
		showPartsContent = false;

		completeButtonTitle = '점검(수리)완료';
	}

	// function playAudio(audio_name: string) {
	// 	let audio_obj;
	//
	// 	if (Object.prototype.hasOwnProperty.call(audioFiles, audio_name)) {
	// 		playAudioFile(audio_obj, audioFiles[audio_name]);
	// 	} else {
	// 		executeMessage('오디오를 찾을 수 없습니다.', 'error');
	// 	}
	// }
	//
	// function playAudioFile(audio_obj, filename: string) {
	// 	audio_obj = new Audio(filename);
	// 	audio_obj.load();
	// 	audio_obj.oncanplaythrough = () => {
	// 		setTimeout(() => {
	// 			audio_obj
	// 				.play()
	// 				.then(() => {
	// 					audio_obj.onended = () => {
	// 						return true;
	// 					};
	// 				})
	// 				.catch((error) => {
	// 					// Autoplay was prevented.
	// 					// Show a "Play" button so that user can start playback.
	// 					executeMessage(error, 'error');
	// 				});
	// 		}, 100);
	// 	};
	// }
	//
	// let processCheck: ProcessElement;
	// let processRepair: ProcessElement;
	// let processGrade: ProcessElement;
	//
	// let processCheckData: ProcessItem[] = $state([]);
	// let processRepairData: ProcessItem[] = $state([]);
	// let processGradeData: ProcessItem[] = $state([]);
	//
	// async function getProcessDataFromIndexedDB() {
	// 	processCheck = await getData('processes', 'check');
	// 	if (processCheck.data) {
	// 		processCheckData = processCheck.data;
	// 	}
	//
	// 	processRepair = await getData('processes', 'repair');
	// 	if (processRepair.data) {
	// 		processRepairData = processRepair.data;
	// 	}
	//
	// 	processGrade = await getData('processes', 'grade');
	// 	if (processGrade.data) {
	// 		processGradeData = processGrade.data;
	// 	}
	// }

	async function handleSearchEnter(e: KeyboardEvent, type: number) {
		if (e.key === 'Enter') {
			if (type === 1) {
				keyword1 = keyword1.trim();
				display_keyword1 = keyword1;
				display_keyword2 = '';
			} else if (type === 2) {
				keyword2 = keyword2.trim();
				display_keyword1 = '';
				display_keyword2 = keyword2;
			}
			await makeData(true);

			focusInput.focus();
			keyword1 = '';
			keyword2 = '';
		}
	}

	function updateCheckingBarcode(newValue: string) {
		checkingBarcode = newValue;
	}
	
	$effect(() => {
		const regex = /[ㄱ-ㅎ|ㅏ-ㅣ|가-힣]/;
		if (regex.exec(checkingBarcode) !== null) {
			const value = checkingBarcode.slice(0, -1);
			setTimeout(() => {
				updateCheckingBarcode(value);
			}, 0);
		}
		
		// 수리 등급이 선택되면 버튼 활성화
		isCompleteButtonSuccess = !!selectedGradeCode;
	});

	onMount(async () => {
		productStore.set('');
		await getPartsCategories();
		checkView();

		modal = document.getElementById('my_modal_1') as HTMLDialogElement;
	});

	const breadcrumbs: Breadcrumb[] = [
		{ title: '수리/점검', url: '/works/repairs' }
	];
</script>

<style>
    .location_scan_messagebox {
        font-size: 14pt;
        font-weight: bold;
        color: #28a745;
    }

    .messagebox_off {
        font-size: 12pt;
        color: #515151;
    }

    .messagebox_on {
        font-size: 14pt;
        font-weight: bold;
        color: #e07934;
    }
</style>

<svelte:head>
	<title>수리/점검</title>
</svelte:head>

<AppLayout {user}>
	{#snippet main ()}
		<div>
			<TitleBar {breadcrumbs} />
			
			<section class="main-section">
				<div class="w-full flex flex-row justify-start">
					<div class="w-1/3 flex flex-col">
						<div class="w-full">
							<!--바코드 스캔-->
							<div class="w-full">
								<div class="mb-2 border border-neutral-300">
									<div>
										<div class="p-2 flex items-center bg-neutral-300 border-b-2 border-b-neutral-400 text-black">
											<Icon data={faBarcode} />
											<span class="pl-2 text-xl font-bold">바코드 스캔</span>
										</div>
									</div>
									<div class="p-2 flex flex-col">
										<div>
											<div class="location_scan_messagebox">
												바코드값을 입력(스캔)해 주시기를 바랍니다.
											</div>
											<div
												class:messagebox_off={prodScanMessageOffClass}
												class:messagebox_on={prodScanMessageOnClass}
												id="prod_scan_message"
											>
												{prodScanMessage}
											</div>
										</div>
										<div class="w-full">
											<div class="w-full">
												<input
													bind:this={isFocusCheckingBarcode}
													bind:value={checkingBarcode}
													class="input input-bordered text-2xl bg-amber-300 text-black"
													onkeydown={(e) => e.key === 'Enter' && handleBarcodeInput(e)}
													placeholder="바코드 입력 또는 스캔"
													type="text"
												/>
												
												<button
													class="btn btn-primary ml-2"
													onclick={handleBarcodeInput}
													type="button"
												>
													확인
												</button>
											</div>
										</div>
									</div>
									
									<div class="border"></div>
									
									<div class="w-full p-2">
										<div class="w-full border border-neutral-400">
											<div class="w-full flex border-0 border-b border-b-neutral-400">
												<div class="w-1/6 flex items-center justify-center bg-neutral-300 text-black font-bold">
													QAID
												</div>
												<div class="w-5/6 p-2">
													{#if checkingProduct.qaid}
														{checkingProduct.qaid}
														
														{#if checkingProduct.rg === 'Y'}
															<Icon data={faRocket} scale={2} class="ml-1 text-red-700" />
														{/if}
														
														<span class="cursor-pointer tooltip tooltip-top"
																	data-tip="QAID 재발행"
																	onclick={async () => await isRePrint(checkingProduct.qaid, checkingProduct.id, user)}
																	role="presentation"
														>
																<Icon data={faPrint} class="mx-0.5 text-primary-700" />
															</span>
													{/if}
												</div>
											</div>
											<div class="w-full flex border-0 border-b border-b-neutral-400">
												<div class="w-1/6 flex items-center justify-center bg-neutral-300 text-black font-bold">
													상품명
												</div>
												<div class="w-5/6 p-2">
													{checkingProduct.name}
												</div>
											</div>
											{#if showSymptomContent && repairSymptomCodes}
												<div class="w-full flex border-0 border-b border-b-neutral-400">
													<div
														class="w-1/6 flex items-center justify-center bg-neutral-300 text-sm text-black font-bold">
														증상 내용
													</div>
													<div class="w-5/6 p-2">
														<select bind:value={selectedSymptomCode}
																		class="select select-bordered select-sm w-full p-0 text-base"
																		id="check_code"
																		onchange={async () => {
																			if(selectedSymptomCode) showProcessContent = true;
																			await getRepairProcesses();
																		}}
														>
															<option value="">선택</option>
															{#if repairSymptomCodes}
																{#each repairSymptomCodes as item (item.code)}
																	<option value={item.code}>{item.name}</option>
																{/each}
															{/if}
														</select>
													</div>
												</div>
											{/if}
											
											{#if showProcessContent && repairProcessCodes}
												<div class="w-full flex border-0 border-b border-b-neutral-400">
													<div
														class="w-1/6 flex items-center justify-center bg-neutral-300 text-sm text-black font-bold">
														처리 내용
													</div>
													<div class="w-5/6 p-2">
														<select bind:value={selectedProcessCode}
																		class="select select-bordered select-sm w-full p-0 text-base"
																		id="repair_code"
																		onchange={async () => {
																				if(selectedProcessCode) {
																					showGradeContent = true;
																					showPartsContent = selectedProcessCode === 'RP_COMPO';
																				}
																				await getRepairGrades();
																		}}
														>
															<option value="">선택</option>
															{#if repairProcessCodes}
																{#each repairProcessCodes as item (item.code)}
																	<option value={item.code}>{item.name}</option>
																{/each}
															{/if}
														</select>
													</div>
												</div>
											{/if}
											
											{#if showPartsContent}
												<div class="w-full flex border-0 border-b border-b-neutral-400">
													<div class="w-1/6 flex items-center justify-center bg-neutral-300 text-xs text-black font-bold">
														구성품 추가
													</div>
													<div class="w-5/6 p-2">
														<select bind:value={selectedPartsCategory}
																		class="select select-bordered select-sm text-base"
																		onchange={async () => {
																			await getParts();
																		}}
														>
															<option value="">분류 선택</option>
															{#if repairPartsCategories}
																{#each repairPartsCategories as item (item.id)}
																	<option value={item.id}>{item.name}</option>
																{/each}
															{/if}
														</select>
														
														<select bind:value={selectedParts}
																	class="select select-bordered select-sm text-base"
																	disabled={selectedPartsCategory === ''}
														>
															<option value="">구성품 선택</option>
															{#if repairParts}
																{#each repairParts as item (item.id)}
																	<option value={item.id}>{item.name}</option>
																{/each}
															{/if}
														</select>
														
														<button class="btn btn-primary btn-sm"
																		onclick={handleAddParts}
																		type="button"
																	>추가</button>
													</div>
												</div>
												<div class="w-full p-1 border border-neutral-300">
													<div class="p-0.5 flex items-center bg-neutral-200 border-b-2 border-b-neutral-400 text-black">
														<span class="text-sm font-bold">선택된 구성품</span>
													</div>
													{#if selectedPartsList.length > 0}
														<ul class="list-disc pl-6 py-2">
															{#each selectedPartsList as part}
																<li class="text-base">
																	{part.name} - {part.price.toLocaleString()}원
																	<button
																		class="btn btn-xs btn-error ml-2"
																		onclick={() => {
																			// 두 배열에서 모두 제거
																			addParts = addParts.filter(p => p.id !== part.id);
																			selectedPartsList = selectedPartsList.filter(p => p.id !== part.id);
																		}}
																	>
																		삭제
																	</button>
																</li>
															{/each}
														</ul>
													{:else}
														<p class="p-2 text-gray-500">선택된 구성품이 없습니다.</p>
													{/if}
												</div>
											{/if}
											
											{#if showGradeContent && repairGradeCodes}
												<div class="w-full flex border-0 border-b border-b-neutral-400">
													<div
														class="w-1/6 flex items-center justify-center bg-neutral-300 text-sm text-black font-bold">
														수리 상태
													</div>
													<div class="w-5/6 p-2">
														<select bind:value={selectedGradeCode}
																		class="select select-bordered select-sm w-full p-0 text-base"
																		id="grade_code"
																		onchange={async () => {
																			if(selectedGradeCode) {
																				showPartsContent = selectedProcessCode === 'RP_COMPO';
																			}
																		}}
														>
															<option value="">선택</option>
															{#if repairGradeCodes}
																{#each repairGradeCodes as item (item.code)}
																	<option value={item.code}>{item.name}</option>
																{/each}
															{/if}
														</select>
													</div>
												</div>
											{/if}
											<div class="w-full flex">
												<div class="w-1/6 flex items-center justify-center bg-neutral-300 text-black font-bold">
													메모
												</div>
												<div class="w-5/6 p-2">
													<textarea
														bind:value={memo}
														class="textarea textarea-bordered w-full"
														id="memo"
														name="memo"
														placeholder="작성내용은 처리내용 뒤에 첨부됩니다"
													></textarea>
												</div>
											</div>
										</div>
									</div>
									<div class="w-full p-2 flex">
										<div class="w-1/2 flex items-center justify-center">
											<button
												class="btn"
												class:btn-default={!isCompleteButtonSuccess}
												class:btn-success={isCompleteButtonSuccess}
												id="complete_btn"
												onclick={completeCheckIn}
												type="button"
											>
												<Icon data={faBoxesPacking} />
												{completeButtonTitle}
											</button>
										</div>
										<div class="w-1/2 flex items-center justify-center">
											<button
												class="btn btn-neutral"
												id="cancel_btn"
												onclick={cancelCheckIn}
												type="button"
											>
												<Icon data={faBan} />
												취소
											</button>
										</div>
									</div>
								</div>
							</div>
						</div>
						
						<div class="py-3"></div>
						
						<div class="w-full border border-neutral-400">
							<div class="w-full p-3 flex flex-col items-center justify-center">
								<div
									class="w-full p-2 flex items-center justify-center text-2xl cursor-pointer"
									id="command_barcode_title"
									onclick={() => (commandVisible = !commandVisible)}
									role="presentation"
								>
									<Icon data={faBell} scale={2} />
									명령어 바코드
								</div>
								
								{#if commandVisible}
									<div class="w-full flex flex-col border border-neutral-400">
										<div class="w-full flex flex-row">
											<div class="w-1/3 flex flex-col border-b border-r border-neutral-400">
												<div class="w-full flex items-center justify-center p-2 bg-neutral-300 text-black font-bold">
													점검완료
												</div>
												<div class="w-full flex items-center justify-center p-2">
													<Barcode id="barcode1" value="complete" options={{ fontOptions: 'bold' }} />
												</div>
											</div>
											<div class="w-2/3 flex items-center p-2 border-b border-l border-neutral-400 text-sm">
												점검완료를 진행합니다. 해당 번호의 팔레트에 적재합니다.
											</div>
										</div>
										<div class="w-full flex flex-row">
											<div class="w-1/3 flex flex-col border-b border-r border-neutral-400">
												<div class="w-full flex items-center justify-center p-2 bg-neutral-300 text-black font-bold">
													취소
												</div>
												<div class="w-full flex items-center justify-center p-2">
													<Barcode id="barcode2" value="cancel" options={{ fontOptions: 'bold' }} />
												</div>
											</div>
											<div class="w-2/3 flex items-center p-2 border-b border-l border-neutral-400 text-sm">
												점검취소를 진행합니다.
											</div>
										</div>
										<div class="w-full flex flex-row">
											<div class="w-1/3 flex flex-col border-b border-r border-neutral-400">
												<div class="w-full flex items-center justify-center p-2 bg-neutral-300 text-black font-bold">
													증상내용
												</div>
												<div class="w-full flex items-center justify-center p-2">
													<Barcode id="barcode3" value="check/CH_OK" options={{ fontOptions: 'bold' }} />
												</div>
											</div>
											<div class="w-2/3 flex items-center p-2 border-b border-l border-neutral-400 text-sm">
												'check/CH_OK'와 같이 'check/'다음에 증상내용코드를 추가한 값을 스캔(입력)합니다.
											</div>
										</div>
										<div class="w-full flex flex-row">
											<div class="w-1/3 flex flex-col border-b border-r border-neutral-400">
												<div class="w-full flex items-center justify-center p-2 bg-neutral-300 text-black font-bold">
													처리내용
												</div>
												<div class="w-full flex items-center justify-center p-2">
													<Barcode id="barcode4" value="repair/RP_CONFIRM" options={{ fontOptions: 'bold' }} />
												</div>
											</div>
											<div class="w-2/3 flex items-center p-2 border-b border-l border-neutral-400 text-sm">
												'repair/RP_CONFIRM'과 같이 'repair/'다음에 처리내용코드를 추가한 값을 스캔(입력)합니다.
											</div>
										</div>
										<div class="w-full flex flex-row">
											<div class="w-1/3 flex flex-col border-b border-r border-neutral-400">
												<div class="w-full flex items-center justify-center p-2 bg-neutral-300 text-black font-bold">
													수리상태
												</div>
												<div class="w-full flex items-center justify-center p-2">
													<Barcode id="barcode5" value="grade/ST_BEST" options={{ fontOptions: 'bold' }} />
												</div>
											</div>
											<div class="w-2/3 flex items-center p-2 border-b border-l border-neutral-400 text-sm">
												'grade/ST_BEST'와 같이 'grade/'다음에 수리상태코드를 추가한 값을 스캔(입력)합니다.
											</div>
										</div>
										<div class="w-full flex flex-row">
											<div class="w-1/3 flex flex-col border-b border-r border-neutral-400">
												<div class="w-full flex items-center justify-center p-2 bg-neutral-300 text-black font-bold">
													OS설치비용
												</div>
												<div class="w-full flex items-center justify-center p-2">
													<Barcode id="barcode6" value="osinstall" options={{ fontOptions: 'bold' }} />
												</div>
											</div>
											<div class="w-2/3 flex items-center p-2 border-b border-l border-neutral-400 text-sm">
												'osinstall'을 스캔(입력)하면 운영체재 재설치를 완료한 경우 비용청구를 추가합니다.
											</div>
										</div>
									</div>
								{/if}
							</div>
						</div>
					</div>
					<div class="pl-2 w-2/3">
						<SearchUI>
							<div class="w-full flex">
								<div class="flex flex-grow items-center pl-5 py-1 text-sm">
									<span class="mr-2">QAID/바코드/로트번호:</span>
									<label class="input input-bordered input-sm flex items-center justify-center gap-2">
										<input
											bind:this={focusInput}
											bind:value={keyword1}
											class="grow bg-base-100"
											onkeydown={async (e) => await handleSearchEnter(e, 1)}
											placeholder="QAID/바코드/로트번호"
											type="text"
										/>
									</label>
									
									<span class="ml-4 mr-2">상품명:</span>
									<label class="input input-bordered input-sm flex items-center justify-center gap-2">
										<input bind:value={keyword2}
													 class="grow bg-base-100"
													 onkeydown={async (e) => await handleSearchEnter(e, 2)}
													 placeholder="상품명"
													 type="text"
										/>
									</label>
								</div>
							</div>
							
							<DisplayKeyword
								display_keyword1={display_keyword1}
								display_keyword2={display_keyword2}
							/>
						</SearchUI>
						
						<!-- 리스트 시작 -->
						<div class="px-2">
							<TableTop onUpdate={changeSearchParams} {pageSize} {total} />
							
							<table class="table text-xs table-pin-rows table-zebra">
								<thead class="uppercase relative">
								<tr class="bg-base-content text-base-300 text-center h-[60px]">
									<th class="p-0.5">번호</th>
									<th class="p-0.5">입고날짜</th>
									<th class="p-0.5">QAID</th>
									<th class="p-0.5">상품명<br>바코드</th>
									<th class="p-0.5">검수상태</th>
									<th class="p-0.5">검수일자</th>
									<th class="p-0.5">점검(수리)<br>상태</th>
									<th class="p-0.5">점검자</th>
									<th class="p-0.5">점검(수리)<br>일자</th>
									<th class="p-0.5">출고일자</th>
								</tr>
								</thead>
								
								<tbody>
								{#if $productStore.products}
									{#each $productStore.products as item, index}
										<tr class="hover:bg-base-content/10">
											<td class="w-[50px] min-w-[50px] max-w-[50px] p-0.5 text-center">
												{getNumberFormat(startNo - index)}
											</td>
											<td class="w-[80px] min-w-[80px] max-w-[80px] p-0.5 text-center">
												<span class={item.status === 10 && isOverDueDate(item.req.req_at, item.rg)
													? 'font-bold text-red-700'
													: ''}>
													{item.req.req_at}
												</span>
											</td>
											<td class="w-[100px] min-w-[100px] max-w-[140px] p-0.5 text-base text-center">
												<div class="flex text-sm">
													{item.qaid}
													
													{#if item.rg === 'Y'}
														<span>
															<Icon data={faRocket} class="mx-0.5 text-error" />
														</span>
													{/if}
													
													{#if item.qaid === display_keyword1}
														<span class="cursor-pointer"
																	onclick={() => isRePrint(item.qaid, item.id, user)}
																	role="presentation"
														>
															<Icon data={faPrint} class="mx-0.5 text-primary-700" />
														</span>
													{/if}
												</div>
											</td>
											<td class="p-0.5">
												<div>
													{item.name}
													{#if item.link.product_id && item.link.product_id.slice(-8) !== '00000000'}
														<a href="https://www.coupang.com/"
															 onclick={(e) => {
													 e.preventDefault();
													 
													 const id = generateRandomNumber();
													 openWindow( `coupang-${id}`, `https://www.coupang.com/vp/products/${item.link.product_id}?itemId=${item.link.item_id}&vendorItemId=${item.link.vendor_item_id}`
													 );
												 }}
														>
															<Icon data={faCopyright} class="text-error" />
														</a>
													{/if}
												</div>
												<div class="text-base-content/50">{item.barcode}</div>
											</td>
											<td class="w-[76px] min-w-[76px] max-w-[76px] p-0.5 text-center">
												{getProductCheckedStatusName(item.checked_status)}
											</td>
											<td class="w-[90px] min-w-[86px] max-w-[90px] p-0.5 text-center">
												{#if item.checked_at}
													{formatDateTimeToFullString(item.checked_at)}
												{:else}
													-
												{/if}
											</td>
											<td class="w-[50px] min-w-[50px] max-w-[50px] p-0.5 text-center">
												{#if item.repair_product}
													{@html getProcessGradeColorButton(item.repair_product.repair_grade)}
												{:else}
													-
												{/if}
											</td>
											<td class="w-[50px] min-w-[50px] max-w-[50px] p-0.5 text-center">
												{#if item.repair_product}
													{item.repair_product.completed_user.username}
												{:else}
													-
												{/if}
											</td>
											<td class="w-[90px] min-w-[86px] max-w-[90px] p-0.5 text-center">
												{#if item.repair_product}
													{formatDateTimeToFullString(item.repair_product.completed_at)}
												{:else}
													-
												{/if}
											</td>
											<td class="w-[90px] min-w-[86px] max-w-[90px] p-0.5 text-center">
												{#if item.pallet_products.length > 0}
													{formatDateTimeToString(item.pallet_products[0].pallet.exported_at)}
												{:else}
													-
												{/if}
											</td>
										</tr>
									{/each}
								{:else}
									<tr>
										<td colspan="14" class="text-center"> 검색 결과가 없습니다.</td>
									</tr>
								{/if}
								</tbody>
							</table>
						</div>
						
						<!-- Pagination -->
						<Paginate
							{links}
							{localUrl}
							onUpdate={changeSearchParams}
							pageCurrentPage={$productStore.pageCurrentPage}
							pageNextPageUrl={$productStore.pageNextPageUrl}
							pagePrevPageUrl={$productStore.pagePrevPageUrl}
							{searchParams}
						/>
					</div>
				</div>
			</section>
			
			<dialog class="modal" id="my_modal_1">
				<div class="modal-box w-1/2 max-w-2xl">
					<form method="dialog">
						<button class="btn btn-sm btn-circle btn-ghost absolute right-2 top-2">✕</button>
					</form>
					
					<!-- 여기 내용-->
					<div class="py-5">
						<span class="text-3xl text-red-700">{modalErrorMessage}</span>
					</div>
					
					<div class="modal-action w-full flex items-center justify-center">
						<form method="dialog">
							<!-- if there is a button in form, it will close the modal -->
							<button
								class="btn btn-warning tooltip tooltip-top"
								data-tip="키보드의 Escape 키를 누르면 닫힙니다."
							>
								<Icon class="w-5 h-5" data={faXmark} />
								닫기
							</button>
						</form>
					</div>
				</div>
			</dialog>
		</div>
	{/snippet}
</AppLayout>
