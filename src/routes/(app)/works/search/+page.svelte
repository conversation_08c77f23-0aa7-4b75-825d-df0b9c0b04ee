<script lang="ts">
	import { getUser, type User } from '$lib/User';
	import { onMount, onDestroy } from 'svelte';
	import { page } from '$app/state';
	import type { Breadcrumb } from '$lib/types';
	
	import { isRePrint } from '$lib/BarcodeUtils';
	import {
		executeMessage,
		formatDateTimeToFullString,
		formatDateTimeToString,
		generateRandomNumber,
		getNumberFormat,
		isOverDueDate,
		openWindow
	} from '$lib/Functions';
	
	import { getProductCheckedStatusName, loadProduct, productStore } from '$stores/productStore';
	import { getProcessGradeColorButton } from '$stores/processStore';

	import AppLayout from '$components/Layouts/AppLayout.svelte';
	import Loading from '$components/Loading/Circle2.svelte';
	import TitleBar from '$components/UI/TitleBar.svelte';
	import SearchUI from '$components/UI/SearchUI.svelte';
	import TableTop from '$components/UI/TableTop.svelte';
	import Paginate from '$components/UI/Paginate.svelte';
	import Barcode from '$components/Snippets/Barcode.svelte';
	import DisplayKeyword from '$components/Snippets/DisplayKeyword.svelte';

	import Icon from 'svelte-awesome';
	import { faCopyright } from '@fortawesome/free-regular-svg-icons/faCopyright';
	import { faRocket } from '@fortawesome/free-solid-svg-icons/faRocket';
	import { faPrint } from '@fortawesome/free-solid-svg-icons/faPrint';

	let user: User = getUser();
	const apiUrl = '/wms/products/search';
	const localUrl = page.url.pathname;
	let isLoading = $state(false); // 로딩중 표시 여부

	// 검색 query string 시작 ==========
	let keyword1 = $state(''); // 검색어(QAID, 바코드, 로트 번호)
	let keyword2 = $state(''); // 검색어(상품명)
	let display_keyword1 = $state('');
	let display_keyword2 = $state('');
	let p = $state(page.url.searchParams.get('p') || '1');
	let pageSize = $state(page.url.searchParams.get('pageSize') || '15');
	let searchParams = $state('');
	let apiSearchParams = '';
	// 검색 query string 종료 ==========

	// 페이징 관련 변수 시작 =============
	let links: [] = $state([]);
	let total = $state(0);
	let startNo = $state(0);
	// 페이징 관련 변수 종료 =============

	// =============== 페이지 내 필요한 변수들 시작 ===============
	let pdfLoaded = $state(false);
	let printQaid = $state('');
	let focusInput: HTMLElement; // 페이지 로딩시 입고검수 input에 커서 포커싱
	// =============== 페이지 내 필요한 변수들 종료 ===============

	/**
	 * API로 부터 데이터를 가져온다.
	 */
	async function makeData(search = false) {
		if ((!display_keyword1 || display_keyword1 === '') && (!display_keyword2 || display_keyword2 === '')) {
			await executeMessage('검색어를 입력해 주세요.', 'warning');
			return false;
		}

		isLoading = true;

		if (search) {
			p = '1'
		}

		const common_params = {
			searchType: 'all', // 외부업체(guest) 일 경우 qaid 로 변경할 것
			keyword1: display_keyword1,
			keyword2: display_keyword2,
			pageSize
		};

		const params = new URLSearchParams({ p: p, ...common_params });
		const api_params = new URLSearchParams({ page: p, ...common_params });

		searchParams = params.toString();
		apiSearchParams = api_params.toString();

		await loadProduct(`${apiUrl}?${apiSearchParams}`, user);

		if ($productStore) {
			links = JSON.parse($productStore.pageLinks);
			total = $productStore.pageTotal ?? 0;
			startNo = $productStore.pageTotal - $productStore.pageFrom + 1;
		}

		focusInput.focus();
		isLoading = false;
	}

	/**
	 * 자식 컴포넌트에서 변경된 변수의 값을 매칭
	 *
	 * @param value
	 */
	function changeSearchParams(value) {
		if (value.detail.p) p = value.detail.p;
		if (value.detail.keyword1 || value.detail.keyword1 === '') keyword1 = value.detail.keyword1.trim();
		if (value.detail.keyword2 || value.detail.keyword2 === '') keyword2 = value.detail.keyword2.trim();
		if (value.detail.pageSize) pageSize = value.detail.pageSize;

		makeData();
	}

	async function handleSearchEnter(e: KeyboardEvent, type: number) {
		if (e.key === 'Enter') {
			if (type === 1) {
				keyword1 = keyword1.trim();
				display_keyword1 = keyword1;
				display_keyword2 = '';
			} else if (type === 2) {
				keyword2 = keyword2.trim();
				display_keyword1 = '';
				display_keyword2 = keyword2;
			}
			await makeData(true);

			focusInput.focus();
			keyword1 = '';
			keyword2 = '';
		}
	}

	onMount(async () => {
		productStore.set('');
		focusInput.focus();
	});

	const breadcrumbs: Breadcrumb[] = [{ title: '상품검색', url: '/works/search' }];
</script>

<svelte:head>
	<title>작업 > 상품검색</title>
</svelte:head>

{#if isLoading}
	<Loading size="60" unit="px" />
{/if}

<AppLayout {user}>
	{#snippet main()}
		<div class="h-screen">
			<TitleBar {breadcrumbs} />
			
			<section class="main-section">
				<SearchUI>
					<div class="w-full flex">
						<div class="flex flex-grow items-center pl-5 py-1 text-sm">
							<span class="mr-2">QAID/바코드/로트번호:</span>
							<label class="input input-bordered input-sm flex items-center justify-center gap-2">
								<input
									bind:this={focusInput}
									bind:value={keyword1}
									class="grow bg-base-100"
									onkeydown={async (e) => await handleSearchEnter(e, 1)}
									placeholder="QAID/바코드/로트번호"
									type="text"
								/>
							</label>
							
							<span class="ml-4 mr-2">상품명:</span>
							<label class="input input-bordered input-sm flex items-center justify-center gap-2">
								<input bind:value={keyword2}
											 class="grow bg-base-100"
											 onkeydown={async (e) => await handleSearchEnter(e, 2)}
											 placeholder="상품명"
											 type="text"
								/>
							</label>
							
							<span class="ml-2 text-xs">
								* 키보드의 <span class="text-error font-bold">F5 키</span>를 누르면 초기화면으로 이동합니다.
							</span>
						</div>
					</div>
					
					<DisplayKeyword
						display_keyword1={display_keyword1}
						display_keyword2={display_keyword2}
					/>
				</SearchUI>
				
				<!-- 리스트 시작 -->
				<div class="px-2">
					<TableTop onUpdate={changeSearchParams} {pageSize} {total} />
					
					<table class="table text-xs table-pin-rows table-zebra">
						<thead class="uppercase relative">
						<tr class="bg-base-content text-base-300 text-center h-[60px]">
							<th class="p-0.5">번호</th>
							<th class="p-0.5">입고날짜</th>
							<th class="p-0.5">로트번호</th>
							<th class="p-0.5">카테고리</th>
							<th class="p-0.5">QAID</th>
							<th class="p-0.5">바코드</th>
							<th class="p-0.5">상품명</th>
							<th class="p-0.5">중복</th>
							<th class="p-0.5">단가</th>
							<th class="p-0.5">검수상태</th>
							<th class="p-0.5">검수일자</th>
							<th class="p-0.5">점검(수리)<br>상태</th>
							<th class="p-0.5">점검자</th>
							<th class="p-0.5">점검(수리)<br>일자</th>
							<th class="p-0.5">출고일자</th>
						</tr>
						</thead>
						
						<tfoot class="uppercase relative">
						<tr class="bg-base-content text-base-300 text-center">
							<th class="p-0.5">번호</th>
							<th class="p-0.5">입고날짜</th>
							<th class="p-0.5">로트번호</th>
							<th class="p-0.5">카테고리</th>
							<th class="p-0.5">QAID</th>
							<th class="p-0.5">바코드</th>
							<th class="p-0.5">상품명</th>
							<th class="p-0.5">중복</th>
							<th class="p-0.5">단가</th>
							<th class="p-0.5">검수상태</th>
							<th class="p-0.5">검수일자</th>
							<th class="p-0.5">점검(수리)<br>상태</th>
							<th class="p-0.5">점검자</th>
							<th class="p-0.5">점검(수리)<br>일자</th>
							<th class="p-0.5">출고일자</th>
						</tr>
						</tfoot>
						
						<tbody>
						{#if $productStore.products}
							{#each $productStore.products as item, index}
								<tr class="hover:bg-base-content/10">
									<td class="w-[50px] min-w-[50px] max-w-[50px] p-0.5 text-center">
										{getNumberFormat(startNo - index)}
									</td>
									<td class="w-[80px] min-w-[80px] max-w-[80px] p-0.5 text-center">
										<span class={item.status === 10 && isOverDueDate(item.req.req_at, item.rg)
											? 'font-bold text-red-700'
											: ''}>
											{item.req.req_at}
										</span>
									</td>
									<td class="w-[110px] min-w-[70px] max-w-[110px] p-0.5 text-center">
										{item.lot.name}
									</td>
									<td class="w-[180px] min-w-[120px] max-w-[180px] p-0.5 text-center">
										<div class="flex items-center">
											<div class="w-1/2 p-0">{item.cate4.name}</div>
											{#if item.cate5}
												<div class="w-1/2 p-0">{item.cate5.name}</div>
											{/if}
										</div>
									</td>
									<td class="w-[100px] min-w-[100px] max-w-[140px] p-0.5 text-base text-center">
										<div class="flex text-sm">
											{item.qaid}
																
											{#if item.rg === 'Y'}
												<span>
													<Icon data={faRocket} class="mx-0.5 text-error" />
												</span>
											{/if}
											
											{#if item.qaid === display_keyword1}
											<span class="cursor-pointer"
														onclick={async () => await isRePrint(item.qaid, item.id, user)}
														role="presentation"
											>
												<Icon data={faPrint} class="mx-0.5 text-primary-700" />
											</span>
											{/if}
										</div>
									</td>
									<td class="w-[120px] min-w-[120px] max-w-[120px] flex justify-center items-center p-0.5">
										<Barcode id="barcode_{index}" value={item.barcode} />
									</td>
									<td class="p-0.5">
										{item.name}
										{#if item.link.product_id && item.link.product_id.slice(-8) !== '00000000'}
											<a href="https://www.coupang.com/"
												 onclick={(e) => {
													 e.preventDefault();
													 
													 const id = generateRandomNumber();
													 openWindow( `coupang-${id}`, `https://www.coupang.com/vp/products/${item.link.product_id}?itemId=${item.link.item_id}&vendorItemId=${item.link.vendor_item_id}`
													 );
												 }}
											>
												<Icon data={faCopyright} class="text-error" />
											</a>
										{/if}
									</td>
									<td class="w-[30px] min-w-[30px] max-w-[30px] p-0.5 text-center">
										{#if item.duplicated === "N"}
											-
										{:else}
											중복
										{/if}
									</td>
									<td class="w-[60px] min-w-[60px] max-w-[60px] p-0.5 text-right">
										{getNumberFormat(item.amount)}
									</td>
									<td class="w-[76px] min-w-[76px] max-w-[76px] p-0.5 text-center">
										{getProductCheckedStatusName(item.checked_status)}
									</td>
									<td class="w-[90px] min-w-[86px] max-w-[90px] p-0.5 text-center">
										{#if item.checked_at}
											{formatDateTimeToFullString(item.checked_at)}
										{:else}
											-
										{/if}
									</td>
									<td class="w-[50px] min-w-[50px] max-w-[50px] p-0.5 text-center">
										{#if item.repair_product}
											{@html getProcessGradeColorButton(item.repair_product.repair_grade)}
										{:else}
											-
										{/if}
									</td>
									<td class="w-[50px] min-w-[50px] max-w-[50px] p-0.5 text-center">
										{#if item.repair_product}
											{item.repair_product.completed_user.username}
										{:else}
											-
										{/if}
									</td>
									<td class="w-[90px] min-w-[86px] max-w-[90px] p-0.5 text-center">
										{#if item.repair_product}
											{formatDateTimeToFullString(item.repair_product.completed_at)}
										{:else}
											-
										{/if}
									</td>
									<td class="w-[90px] min-w-[86px] max-w-[90px] p-0.5 text-center">
										{#if item.pallet_products.length > 0}
											{formatDateTimeToString(item.pallet_products[0].pallet.exported_at)}
										{:else}
											-
										{/if}
									</td>
								</tr>
							{/each}
						{:else}
							<tr>
								<td colspan="14" class="text-center"> 검색 결과가 없습니다. </td>
							</tr>
						{/if}
						</tbody>
					</table>
				</div>
				
				<!-- Pagination -->
				<Paginate
					{links}
					{localUrl}
					onUpdate={changeSearchParams}
					pageCurrentPage={$productStore.pageCurrentPage}
					pageNextPageUrl={$productStore.pageNextPageUrl}
					pagePrevPageUrl={$productStore.pagePrevPageUrl}
					{searchParams}
				/>
			</section>
		</div>
	{/snippet}
</AppLayout>
