<script lang="ts">
	import type { Breadcrumb } from '$lib/types';

	import { onMount } from 'svelte';
	import { getUser, type User } from '$lib/User';
	import { page } from '$app/state';

	import { WebviewWindow } from '@tauri-apps/api/webviewWindow';
	import { authClient } from '$lib/AxiosBackend';

	import { getProductStatusName } from '$stores/productStore';

	import { executeMessage, getNumberFormat, handleCatch } from '$lib/Functions';

	import AppLayout from '$components/Layouts/AppLayout.svelte';
	import TitleBar from '$components/UI/TitleBar.svelte';

	import Icon from 'svelte-awesome';
	import { faRedo } from '@fortawesome/free-solid-svg-icons/faRedo';
	import { faPrint } from '@fortawesome/free-solid-svg-icons/faPrint';
	import { faBarcode } from '@fortawesome/free-solid-svg-icons/faBarcode';
	import { faDolly } from '@fortawesome/free-solid-svg-icons/faDolly';
	import { faBan } from '@fortawesome/free-solid-svg-icons/faBan';
	import { faBoxesPacking } from '@fortawesome/free-solid-svg-icons/faBoxesPacking';
	import { faRocket } from '@fortawesome/free-solid-svg-icons/faRocket';
	import { faXmark } from '@fortawesome/free-solid-svg-icons/faXmark';

	const user: User = getUser();
	const apiUrl = '/wms/warehouse/loading';

	// 필수 변수 세팅 시작==================
	let apiSearchParams = '';

	// 작업 중이던 location code
	let warehousePalletNumber = $state(window.localStorage.getItem('warehouse_pallet_number'));
	let palletNumber = $state(page.url.searchParams.get('palletNumber') ?? '');
	let availablePallets = $state([]); // 사용 가능한 팔레트 리스트
	let prodScanMessage = $state('');
	let checkingBarcode = $state('');
	let checkingProduct: any[] = $state([]);
	let memo = $state('');

	let palletDetails = null;
	let palletProdCount = $state(0);
	let palletRecentProducts: any[] = $state([]);

	let modal: HTMLDialogElement;
	let modalErrorMessage = $state('');
	// 필수 변수 세팅 종료==================

	/**
	 * 페이지 최초 접속시 실행
	 * 적재중인 팔레트 리스트
	 */
	async function makeData() {
		try {
			const { status, data } = await authClient.get(`/wms/warehouse/loading`);
			if (status === 200 && data.success) {
				availablePallets = data.data.pallets;

				if (availablePallets.length < 1) {
					await executeMessage("적재중인 팔레트가 없습니다.\n새로운 팔레트를 생성해 주세요.");
					return;
				}

				// 작업 중이던 팔레트 번호를 셋팅한다.
				warehousePalletNumber = window.localStorage.getItem('warehouse_pallet_number');
				if (warehousePalletNumber) {
					palletNumber = warehousePalletNumber;
				}

				palletDetails = availablePallets.find((item) => item.pallet_number === palletNumber);
				if (palletDetails) {
					console.log(palletDetails);
					palletProdCount = palletDetails.items_count;
					palletRecentProducts = palletDetails.recent_items;
					console.log(palletRecentProducts);
				}
			} else {
				await executeMessage('적재중인 팔레트를 가져오는데 실패했습니다.', 'error');
			}
		} catch (e: any) {
			checkingProduct = [];
			await handleCatch(e);
		}
	}

	let isFocusCheckingBarcode: HTMLElement;
	let isReadonlyCheckingBarcode = $state(false);
	let locationScanMessageClass = $state(true); // 스타일 변환
	let prodScanMessageOffClass = $state(false); // 스타일 변환
	let prodScanMessageOnClass = $state(false); // 스타일 변환
	let isCompleteButtonSuccess = $state(false); // 점검완료 버튼

	// 새로운 팔레트 번호를 생성
	async function generatePalletNumber() {
		try {
			const { status, data } = await authClient.get(`/wms/warehouse/pallets/generate`);
			if (status === 200 && data.success) {
				palletNumber = data.data.pallet_number;
				const palletInfo = {
					id: '',
					pallet_number: palletNumber,
					items_count: 0,
					recent_items: [],
					max_capacity: 0,
					current_capacity: 0
				};

				availablePallets = [...availablePallets, palletInfo];
				checkingProduct = [];

				// 새 팔레트 번호를 localStorage에 저장하고 서버에 저장
				window.localStorage.setItem('warehouse_pallet_number', palletNumber);
				await savePalletNumber();
			} else {
				await executeMessage('생성 실패', 'error');
			}
		} catch (e: any) {
			await handleCatch(e);
		}
	}

	async function savePalletNumber() {
		try {
			const { status, data } = await authClient.post(`/wms/warehouse/pallets/save`, {
				pallet_number: palletNumber
			});
			if (status === 200 && data.success) {
				await executeMessage("새로운 팔레트 번호가 생성되었습니다.");
				await makeData();
			} else {
				await executeMessage('저장 실패', 'error');
			}
		} catch (e: any) {
			await handleCatch(e);
		}
	}

	/**
	 * 입력된 바코드가 QAID일 경우 서버에서 상품정보를 가져온다.
	 */
	async function getProductInfo(event: Event) {
		event.preventDefault();
		
		if (!palletNumber) {
			await executeMessage('적재할 팔레트를 먼저 선택해 주세요.');
			return;
		}

		let barcode = checkingBarcode.trim();
		let regex = /[ㄱ-ㅎ|ㅏ-ㅣ|가-힣]/g
		if (barcode === '') {
			await executeMessage('상품 바코드를 입력해 주세요.');
			return;
		} else if (barcode === 'complete') {
			await completeCheckIn();
			return;
		}

		// 바코드 첫 글자가 특정 조건에 맞으면 Q로 변경
		if (regex.test(barcode)) {
			barcode = 'Q' + barcode.slice(1);
		}

		try {
			const { status, data } = await authClient.get(`/wms/warehouse/check-product/${barcode}`);
			if (status === 200 && data.success) {
				checkingBarcode	= '';
				checkingProduct = data.data.product;
				isCompleteButtonSuccess = true;
			} else {
				checkingBarcode	= '';
				checkingProduct = [];
				isCompleteButtonSuccess = false;
				modalErrorMessage = data.data.message;
				modal.showModal();
			}
		} catch (e: any) {
			checkingBarcode	= '';
			checkingProduct = [];
			isCompleteButtonSuccess = false;
			await handleCatch(e);
		}
	}

	function printPalletNumber() {
		const url = `/print/warehouse?pallet_number_list=${palletNumber}`;
		const webview = new WebviewWindow('Print', {
			url: url
		});

		// 창 생성 중 오류가 발생했을 때 이벤트 리스너 추가
		webview.once('tauri://error', () => {
			executeMessage('프린트 창을 여는데 실패 했습니다.\n프로그래머에게 문의해 주세요.', 'error');
		});
	}

	function handleError(message: string) {
		prodScanMessageOffClass = false;
		prodScanMessageOnClass = true;
		prodScanMessage = message;

		return true; // 에러 발생 시 true 반환
	}

	async function completeCheckIn() {
		if (!palletNumber) {
			await executeMessage('적재할 팔레트를 먼저 선택해 주세요.');
			return;
		}

		if (!isCompleteButtonSuccess) {
			await executeMessage('입고 검수 완료한 상품을 선택해 주세요.');
			return;
		}

		const product_id = checkingProduct.id;
		const qaid = checkingProduct.qaid;

		if (typeof product_id !== 'undefined') {
			let payload = {
				pallet_id: palletDetails.id,
				product_id: product_id,
				qaid: qaid,
				memo: memo
			};

			try {
				const { status, data } = await authClient.post(`/wms/warehouse/pallet-items/save`, payload);
				if (status === 200 && data.success) {
					// 작업중인 로케이션을 저장해 둔다.
					window.localStorage.setItem('warehouse_pallet_number', palletNumber);
					await makeData();

					checkingBarcode = '';

					// 팔레트를 계속 유지하기 위해
					const pallet_no = palletNumber;
					clearValues();

					palletNumber = pallet_no;
				} else {
					await executeMessage(data.data.message);
				}
			} catch (error) {
			}
		} else {
			return;
		}
	}

	async function cancelCheckIn() {
		clearValues();
	}

	function clearValues() {
		palletNumber = '';
		checkingBarcode = '';
		checkingProduct = [];
		memo = '';
		prodScanMessage = '';
	}

	/**
	 * 팔레트를 변경하면 데이터를 다시 가져온다.
	 */
	async function handlePalletChange() {
		try {
			window.localStorage.setItem('warehouse_pallet_number', palletNumber);
			await makeData();
		} catch (error) {
			console.error('팔레트 변경에 오류가 있습니다:', error);
		}
	}

	onMount(async () => {
		modal = document.getElementById('my_modal_1') as HTMLDialogElement;
		
		await makeData();
		isFocusCheckingBarcode.focus();
	});

	const breadcrumbs: Breadcrumb[] = [
		{ title: '입고', url: '/works' },
		{ title: '입고 목록', url: '/works/inspects' },
		{ title: '입고 상품 적재', url: '/works/inspect_loading' }
	];
</script>

<style>
    .location_scan_messagebox {
        font-size: 14pt;
        font-weight: bold;
        color: #28a745;
    }

    .messagebox_off {
        font-size: 12pt;
        color: #515151;
    }

    .messagebox_on {
        font-size: 14pt;
        font-weight: bold;
        color: #e07934;
    }
</style>

<svelte:head>
	<title>입고 상품 적재</title>
</svelte:head>

<AppLayout {user}>
	{#snippet main()}
		<div>
			<TitleBar {breadcrumbs} />

			<section class="main-section">
				<div class="w-full px-3 flex flex-row justify-start">
					<div class="w-[800px] flex flex-col">
						<div class="w-full">
							<div class="border border-neutral-300" id="location_box">
								<div>
									<div class="p-2 bg-neutral-300 border-b-2 border-b-neutral-400 text-black">
										<Icon data={faDolly} scale={1.5} />
										<span class="text-xl font-bold">입고검수 완료 후 상품적재(창고)</span>
									</div>
								</div>
								<div class="p-2">
									<div class="p-1"
											 class:location_scan_messagebox={locationScanMessageClass}
											 id="location_scan_message"
									>
										팔레트번호를 확인해 주시기 바랍니다.
									</div>
									<div class="flex">
										<div class="w-2/4">
											<div class="w-full label label-text font-bold">팔레트 번호</div>
											<div class="w-full flex items-center">
												<select bind:value={palletNumber}
																class="select select-bordered w-full text-2xl"
																id="pallet_no"
																onchange={async () => {
																	await handlePalletChange();
																}}
												>
													<option value="">선택</option>
													{#if availablePallets.length > 0}
														{#each availablePallets as item}
															<option value={item.pallet_number}>{item.pallet_number}</option>
														{/each}
													{/if}
												</select>

												<button class="btn btn-info tooltip" onclick={generatePalletNumber} type="button" data-tip="새 팔레트 생성">
													<Icon data={faRedo} />
												</button>

												{#if palletNumber !== ''}
													<button class="btn btn-ghost p-1" onclick={printPalletNumber}>
														<Icon data={faPrint} scale={2} lavel="프린터" />
													</button>
												{/if}
											</div>
										</div>
									</div>
								</div>
							</div>

							<div class="py-3"></div>

							<!--바코드 스캔-->
							<div class="w-full">
								<div class="mb-2 border border-neutral-300" id="product_box">
									<div>
										<div class="p-2 flex items-center bg-neutral-300 border-b-2 border-b-neutral-400 text-black">
											<Icon data={faBarcode} />
											<span class="pl-2 text-xl font-bold">바코드 스캔</span>
										</div>
									</div>
									<div class="h-36 p-2 flex flex-col">
										<div>
											<div class="location_scan_messagebox">
												바코드값을 입력(스캔)해 주시기 바랍니다.
											</div>
											<div class:messagebox_off={prodScanMessageOffClass}
													 class:messagebox_on={prodScanMessageOnClass}
													 id="prod_scan_message"
											>
												{prodScanMessage}
											</div>
										</div>
										<div class="flex">
											<div class="flex w-3/5">
												<input bind:this={isFocusCheckingBarcode}
															 bind:value={checkingBarcode}
															 class="input input-bordered text-2xl bg-amber-300 w-96"
															 id="checking_barcode"
															 name="checking_barcode"
															 onkeydown={(e) => {
																 if (e.key === 'Enter') {
																	 getProductInfo(e);
																 }
															 }}
															 placeholder="바코드 입력 또는 스캔"
															 readonly={isReadonlyCheckingBarcode}
															 type="text"
												/>

												<button class="btn btn-primary ml-2"
																onclick={(e) => getProductInfo(e)}
																type="button"
												>
													확인
												</button>
											</div>
											<div class=" w-2/5">
												{#if palletNumber}
													<div class="w-full h-[80px] text-5xl leading-9 text-center">
														{palletNumber}
													</div>
												{:else}
													<div class="w-full pt-5 text-2xl text-error text-center">
														팔레트 선택 필수
													</div>
												{/if}
											</div>
										</div>
									</div>

									<div class="border"></div>

									<div class="w-full p-2">
										<div class="w-full border border-neutral-400">
											<div class="w-full flex border-0 border-b border-b-neutral-400">
												<div class="w-1/6 flex items-center justify-center bg-neutral-300 text-black font-bold">
													입고일
												</div>
												<div class="w-2/6 p-2 text-center">
													{#if checkingProduct.req}
														{checkingProduct.req.req_at}
													{/if}
												</div>
												<div class="w-1/6 flex items-center justify-center bg-neutral-300 text-black font-bold">
													검수일
												</div>
												<div class="w-2/6 p-2 text-center">
													{#if checkingProduct.checked_at}
														{checkingProduct.checked_at}
													{/if}
												</div>
											</div>
											<div class="w-full flex border-0 border-b border-b-neutral-400">
												<div class="w-1/6 flex items-center justify-center bg-neutral-300 text-black font-bold" >
													QAID
												</div>
												<div class="w-2/6 p-2 text-center">
													{#if checkingProduct.qaid}
														<span class="text-2xl font-bold">{checkingProduct.qaid}</span>

														{#if checkingProduct.rg === 'Y'}
															<Icon data={faRocket} scale={2} class="ml-1 text-red-700" />
														{/if}
													{/if}
												</div>
												<div class="w-1/6 flex items-center justify-center bg-neutral-300 text-black font-bold" >
													상태
												</div>
												<div class="w-2/6 p-2 text-center">
													{#if checkingProduct.status > 0}
														{getProductStatusName(checkingProduct.status)}
													{/if}
												</div>
											</div>
											<div class="w-full flex border-0 border-b border-b-neutral-400">
												<div class="w-1/6 flex items-center justify-center bg-neutral-300 text-black font-bold">
													카테고리
												</div>
												<div class="w-5/6 p-2 text-center">
													{#if checkingProduct.cate4}
														{checkingProduct.cate4.name}
														{#if checkingProduct.cate5}
															> {checkingProduct.cate5.name}
														{/if}
													{/if}
												</div>
											</div>
											<div class="w-full flex border-0 border-b border-b-neutral-400">
												<div class="w-1/6 flex items-center justify-center bg-neutral-300 text-black font-bold">
													상품명
												</div>
												<div class="w-5/6 p-2 text-2xl font-bold">
													{checkingProduct.name}
												</div>
											</div>
											<div class="w-full flex border-0 border-b border-b-neutral-400">
												<div class="w-1/6 flex items-center justify-center bg-neutral-300 text-black font-bold">
													판매가
												</div>
												<div class="w-2/6 p-2 text-center">
													{#if checkingProduct.amount > 0}
														{getNumberFormat(checkingProduct.amount)}
													{/if}
												</div>
												<div class="w-1/6 flex items-center justify-center bg-neutral-300 text-black font-bold">
													입고 수량
												</div>
												<div class="w-2/6 p-2 text-center">
													{#if checkingProduct.quantity}
														{checkingProduct.quantity}
													{/if}
												</div>
											</div>
											<div class="w-full flex">
												<div class="w-1/6 flex items-center justify-center bg-neutral-300 text-black font-bold">
													메모
												</div>
												<div class="w-5/6 p-2">
													<textarea bind:value={memo}
																		class="textarea textarea-bordered w-full"
																		id="memo"
																		name="memo"
																		placeholder="특이사항이 있다면 입력해 주세요."
													></textarea>
												</div>
											</div>
										</div>
									</div>
									<div class="w-full p-2 flex">
										<div class="w-1/2 flex items-center justify-center">
											<button class="btn btn-lg"
															class:btn-default={!isCompleteButtonSuccess}
															class:btn-success={isCompleteButtonSuccess}
															id="complete_btn"
															onclick={completeCheckIn}
															type="button"
											>
												<Icon data={faBoxesPacking} />
												팔레트에 추가
											</button>
										</div>
										<div class="w-1/2 flex items-center justify-center">
											<button class="btn btn-neutral btn-lg"
															id="cancel_btn"
															onclick={cancelCheckIn}
															type="button"
											>
												<Icon data={faBan} />
												등록 취소
											</button>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>

					<div class="w-[300px] pl-4 flex flex-col">
						<div class="w-full border">
							<div class="w-full p-2 bg-neutral-300 border-b-2 border-b-neutral-400 text-black font-bold">
								<Icon data={faDolly} />
								{#if palletNumber}
									<span class="underline">{palletNumber}</span>
								{:else}
									-
								{/if}
								최근 적재
							</div>

							<div class="p-2">
								<div class="w-full flex items-center justify-center text-[150px] font-bold text-red-600">
									{#if palletProdCount}
										{palletProdCount}
									{:else}
										0
									{/if}
								</div>
							</div>

							<div class="p-2 text-xs">
								{#if palletRecentProducts.length > 0}
									{#each palletRecentProducts as product, i}
										<p class="pt-2">[{palletProdCount - i}] {product.product_name}</p>
									{/each}
								{/if}
							</div>
						</div>
					</div>
				</div>
			</section>

			<dialog class="modal" id="my_modal_1">
				<div class="modal-box w-1/2 max-w-2xl">
					<form method="dialog">
						<button class="btn btn-sm btn-circle btn-ghost absolute right-2 top-2">✕</button>
					</form>

					<!-- 여기 내용-->
					<div class="py-5">
						<span class="text-3xl text-red-700">{modalErrorMessage}</span>
					</div>

					<div class="modal-action w-full flex items-center justify-center">
						<form method="dialog">
							<!-- if there is a button in form, it will close the modal -->
							<button
								class="btn btn-warning tooltip tooltip-top"
								data-tip="키보드의 Escape 키를 누르면 닫힙니다."
							>
								<Icon class="w-5 h-5" data={faXmark} />
								닫기
							</button>
						</form>
					</div>
				</div>
			</dialog>
		</div>
	{/snippet}
</AppLayout>
