<script lang="ts">
	import { onMount } from 'svelte';
	import { getUser, type User } from '$lib/User';
	import { page } from '$app/state';

	import { authClient } from '$lib/AxiosBackend';
	import { toast } from 'svoast';
	import { WebviewWindow } from '@tauri-apps/api/webviewWindow';

	import {
		executeAsk,
		executeMessage,
		formatDateTimeToString,
		getNumberFormat,
		getPayload,
		handleCatch,
		handleDownload
	} from '$lib/Functions';
	import {
		warehousePalletItemStatusName,
		loadItems,
		warehousePalletInfoStore,
		warehousePalletItemStore
	} from '$stores/warehousePalletItemStore';
	import {
		WAREHOUSE_PALLET_STATUS_COMPLETED,
		WAREHOUSE_PALLET_STATUS_PENDING,
	} from '$stores/warehousePalletStore';

	import AppLayout from '$components/Layouts/AppLayout.svelte';
	import SearchUI from '$components/UI/SearchUI.svelte';
	import Loading from '$components/Loading/Circle2.svelte';
	import ButtonExcelDownload from '$components/Button/ExcelDownload.svelte';

	import Icon from 'svelte-awesome';
	import { faPrint } from '@fortawesome/free-solid-svg-icons/faPrint';
	import { faDeleteLeft } from '@fortawesome/free-solid-svg-icons/faDeleteLeft';
	import { faBackspace } from '@fortawesome/free-solid-svg-icons/faBackspace';
	import { faLock } from '@fortawesome/free-solid-svg-icons/faLock';
	import { faUnlock } from '@fortawesome/free-solid-svg-icons/faUnlock';
	import { faRocket } from '@fortawesome/free-solid-svg-icons/faRocket';
	import { faFolder } from '@fortawesome/free-regular-svg-icons/faFolder';
	import SearchButton from '$components/Button/Search.svelte';

	let user: User = getUser();
	const apiUrl = `/wms/warehouse/pallet/items`;
	const localUrl = page.url.pathname;
	let isLoading = $state(false); // 로딩중 표시 여부

	// 검색 query string 시작 ==========
	let palletId = $state(page.url.searchParams.get('pallet_id') ?? '');
	let keyword = $state(''); // 검색어
	let p = $state(page.url.searchParams.get('p') || '1');
	let pageSize = $state(page.url.searchParams.get('pageSize') || '1000');
	let searchParams = $state('');
	let apiSearchParams = $state('');
	// 검색 query string 종료 ==========

	// 페이징 관련 변수 시작 =============
	let links: [] = $state([]);
	let total = $state(0);
	let startNo = $state(0);
	// 페이징 관련 변수 종료 =============

	// =============== 페이지 내 필요한 변수들 시작 ===============
	let allChecked = $state(false); // 전체 선택: 체크박스
	let idChecked: [] = $state([]); // 전체 선택: 모든 체크박스의 상태를 참조하는 reactive 변수
	let ids: [] = $state([]); // 전체 선택: 상품의 id를 모으는 변수

	let palletNumber = $state('');
	let palletStatus = $state('');
	
	let pageExportDate = $state('');
	let pageTotalQuantity = $state(0);
	let pageTotalAmount = $state(0);

	let focusInput: HTMLElement;
	let checkQaid = $state(''); // 검수할 QAID
	let checkboxes: [] = $state([]); // 선택된 항목 찾기
	let productIds: number[] = $state([]); // 점검 취소할 때 상품 id를 담는 배열
	// =============== 페이지 내 필요한 변수들 종료 ===============

	/**
	 * API로 부터 데이터를 가져온다.
	 */
	async function makeData() {
		isLoading = true;

		const common_params = {
			pallet_id: palletId,
			keyword: keyword,
			pageSize: pageSize
		};

		const params = new URLSearchParams({ p: p, ...common_params });
		const api_params = new URLSearchParams({ page: p, ...common_params });

		searchParams = params.toString(); // local
		apiSearchParams = api_params.toString(); // api

		await loadItems(`${apiUrl}?${apiSearchParams}`, user);

		if ($warehousePalletInfoStore) {
			palletNumber = $warehousePalletInfoStore.pallet_number;
			palletStatus = $warehousePalletInfoStore.status;
			pageExportDate = formatDateTimeToString($warehousePalletInfoStore.shipped_in_at);
			pageTotalQuantity = $warehousePalletInfoStore.totalQuantity;
			pageTotalAmount = $warehousePalletInfoStore.totalAmount;
		}

		if ($warehousePalletItemStore) {
			startNo = $warehousePalletItemStore.items.length;
		}

		focusInput.focus();
		isLoading = false;
	}

	// 전체 선택
	const toggleAllCheck = (items: any) => {
		allChecked = !allChecked;
		idChecked = items.map(() => allChecked);
		ids = allChecked ? items.map((item: any) => item.id) : [];
	};

	async function cancelCheckedPalletProduct(e: MouseEvent, index: number) {
		idChecked[index] = true;

		const item = $warehousePalletItemStore.items[index];
		if (item && !ids.includes(item.id)) {
			ids.push(item.id);
		}

		productIds = [];
		productIds.push(e.target?.getAttribute('data-product-id') * 1);

		await cancelPalletProducts();
	}

	async function cancelPalletProducts() {
		const count = ids.length;
		if (count > 0) {
			const message =
				count > 1
					? '선택한 ' + count + '개의 상품을 [입고 취소] 하시겠습니까?'
					: '이 상품의 입고를 취소하시겠습니까?';
			const ask = await executeAsk(
				`팔레트[ ${palletNumber} ]에서 상품을 삭제합니다.\n\n${message}`
			);

			if (!ask) {
				return false;
			}

			productIds = [];
			checkboxes.forEach((checkbox) => {
				if (checkbox && checkbox.checked) {
					productIds.push(checkbox.getAttribute('data-product-id') * 1);
				}
			});

			try {
				const payload = {
					_method: 'PATCH',
					productIds: productIds
				};

				const { status, data } = await authClient.post(`/wms/warehouse/pallets/products/exclude-from-pallet`, payload);
				if (status === 200 && data.success) {
					toast.success(`점검 취소 완료`);

					idChecked = Array($warehousePalletItemStore.items.length).fill(false);
					ids = [];
					productIds = [];

					await makeData();
				} else {
					const message = data.data.message.replace(/\\n/g, '\n');
					await executeMessage(message);
				}
			} catch (e: any) {
				await handleCatch(e);
			}
		} else {
			await executeMessage('삭제(점검취소)할 상품을 선택해주시기 바랍니다.');
		}
	}

	async function closePallet() {
		const ask = await executeAsk(`팔레트[ ${palletNumber} ]의 모든 상품을 마감하시겠습니까?`);
		if (!ask) {
			return false;
		}

		try {
			const payload = {
				pallet_id: palletId
			};
			const { status, data } = await authClient.put(`${apiUrl}/close`, payload);

			if (status === 200 && data.success) {
				toast.success(`팔레트[ ${palletNumber} ] 마감 완료`);

				await makeData();
			} else {
				const message = data.data.message.replace(/\\n/g, '\n');
				await executeMessage(message);
			}
		} catch (e: any) {
			await handleCatch(e);
		}
	}

	async function openPallet() {
		const ask = await executeAsk(`팔레트[ ${palletNumber} ]의 마감을 취소하시겠습니까?`);
		if (!ask) {
			return false;
		}

		try {
			const payload = {
				pallet_id: palletId
			};
			const { status, data } = await authClient.put(`${apiUrl}/open`, payload);

			if (status === 200 && data.success) {
				toast.success(`팔레트[ ${palletNumber} ] 마감 취소 완료`);

				await makeData();
			} else {
				const message = data.data.message.replace(/\\n/g, '\n');
				await executeMessage(message);
			}
		} catch (e: any) {
			await handleCatch(e);
		}
	}

	function printPalletCode(event: MouseEvent) {
		if (typeof event !== 'undefined') {
			const printInfo = event.target;

			let grade_name = printInfo?.getAttribute('data-grade-name');
			let level = printInfo?.getAttribute('data-level');
			let column = printInfo?.getAttribute('data-column');
			let export_date = printInfo?.getAttribute('data-export-date');

			let url = `/print/label?level=${level}&column=${column}&grade_name=${grade_name}&export_date=${export_date}`;

			const webview = new WebviewWindow('Print', {
				url: url
			});

			// 창 생성 중 오류가 발생했을 때 이벤트 리스너 추가
			webview.once('tauri://error', (e) => {
				console.error('Error creating webview window', e);
			});
		}
	}

	onMount(async () => {
		warehousePalletInfoStore.set('');
		warehousePalletItemStore.set('');

		await makeData();
		focusInput.focus();
	});
</script>

<svelte:head>
	<title>입고 > 입고(창고) 팔레트 상품목록</title>
</svelte:head>

{#if isLoading}
	<Loading size="60" unit="px" />
{/if}

<AppLayout {user}>
	{#snippet main()}
		<section class="pl-4">
			<div class="breadcrumbs">
				<ul>
					<li>
						<a href="/works">
							<Icon data={faFolder} /> 입고
						</a>
					</li>
					<li>
						<a href="/works/inspections/pallets">
							<Icon data={faFolder} /> 입고(창고) 팔레트 상품 목록
						</a>
					</li>
					<li>
						<span>{palletNumber}</span>
					</li>
				</ul>
			</div>
		</section>
		
		<section class="main-section">
			<SearchUI>
				<div class="w-full flex">
					<div class="flex items-center w-48 p-1 bg-base-content text-base-300">팔레트 위치</div>
					<div class="flex flex-grow items-center pl-5 py-0.5">
						<button
							class="btn btn-ghost"
							data-export-date={pageExportDate}
							onclick={printPalletCode}
						>
							{palletNumber}
							<Icon data={faPrint} />
						</button>
					</div>
				</div>
				<div class="w-full flex">
					<div class="flex items-center w-48 p-1 bg-base-content text-base-300">검색 항목</div>
					<div class="flex flex-grow items-center pl-5 py-0.5">
						<label class="input input-bordered input-sm flex items-center justify-center gap-2 mx-2">
							<input bind:value={keyword}
										 bind:this={focusInput}
										 class="grow bg-base-100"
										 onkeydown={async (e) => {
											 if (e.key === "Enter") {
												 await makeData();
												 focusInput.focus();
											 }
										 }}
										 type="text"
							/>
						</label>
						
						<SearchButton onclick={makeData} tooltipData="검색" useTooltip={true} />
						
						<ButtonExcelDownload
							onclick={async (e) => {
							e.preventDefault();
							isLoading = true;

							const payload = getPayload(apiSearchParams);
							payload.pallet_number = palletNumber;
							payload.pallet_ids = [Number(palletId)];

							const url = `/wms/warehouse/pallets/download`;
							await handleDownload(url, payload);
							isLoading = false;
						}}
							useTooltip={true}
						/>
					</div>
				</div>
			</SearchUI>
			
			<!-- 리스트 시작 -->
			<div class="px-2">
				<!--
				@todo: 입고는 창고에서 바로 하는 건데...이 부분을 어떻게 처리해야 할까?
				-->
				<div class="row" style="padding-bottom: 5px;">
					{#if palletStatus === WAREHOUSE_PALLET_STATUS_PENDING}
						<button onclick={closePallet} class="btn btn-info btn-xs">
							<Icon data={faLock} />
							입고 준비(적재 마감)
						</button>
					{:else}
						<button onclick={openPallet} class="btn btn-warning btn-xs">
							<Icon data={faUnlock} />
							입고 취소(적재가능)
						</button>
					{/if}
				</div>
				
				<table class="table text-xs table-pin-rows table-zebra">
					<thead class="uppercase">
					<tr class="h-8 bg-base-content text-base-300 text-center">
						{#if palletStatus !== WAREHOUSE_PALLET_STATUS_COMPLETED}
							<th class="p-0.5">
								<input
									checked={allChecked}
									onchange={() => toggleAllCheck($warehousePalletItemStore.items)}
									type="checkbox"
								/>
							</th>
						{/if}
						<th class="p-0.5">번호</th>
						<th class="p-0.5">입고일</th>
						<th class="p-0.5">QAID</th>
						<th class="p-0.5">카테고리</th>
						<th class="p-0.5">상품명</th>
						<th class="p-0.5">판매가</th>
						<th class="p-0.5">상태</th>
						<th class="p-0.5">검수일자</th>
						<th class="p-0.5">검수자</th>
						{#if palletStatus !== WAREHOUSE_PALLET_STATUS_COMPLETED}
							<th class="min-w-24 p-0.5 text-right">
								<button type="button" class="btn btn-error btn-xs" onclick={cancelPalletProducts}>
									<Icon data={faBackspace} />
									선택취소
								</button>
							</th>
						{/if}
					</tr>
					</thead>
					
					<tfoot class="uppercase">
					<tr class="h-12 bg-base-content text-base-300">
						{#if palletStatus !== WAREHOUSE_PALLET_STATUS_COMPLETED}
							<th class="p-0.5 text-right" colspan="5">합계</th>
						{:else}
							<th class="p-0.5 text-right" colspan="4">합계</th>
						{/if}
						<th class="p-0.5 text-center">{getNumberFormat(pageTotalQuantity)} 개 상품</th>
						<th class="p-0.5 text-right">{getNumberFormat(pageTotalAmount)} 원</th>
						<th colspan="4"></th>
					</tr>
					</tfoot>
					
					<tbody>
					{#if $warehousePalletItemStore.items}
						{#each $warehousePalletItemStore.items as item, index}
							<tr class="hover:bg-base-content/10">
								{#if palletStatus !== WAREHOUSE_PALLET_STATUS_COMPLETED}
									<td class="w-[20px] min-w-[20px] max-w-[20px] p-0.5 text-center">
										<input bind:checked={idChecked[index]}
													 bind:group={ids}
													 bind:this={checkboxes[index]}
													 data-product-id={item.product.id}
													 value={item.id}
													 type="checkbox"
										/>
									</td>
								{/if}
								<td class="w-[30px] min-w-[30px] max-w-[30px] p-0.5 text-center">
									<input type="hidden" name="pallet_product_id" bind:value={item.product_id} />
									<input type="hidden"
												 name="pallet_product_name"
												 bind:value={item.product.name}
									/> {getNumberFormat(startNo - index)}
								</td>
								<td class="text-center">
									{item.product.req.req_at}
								</td>
								<td class="min-w-[100px] max-w-[120px] p-0.5 text-base text-center">
										<span class="flex">
											{item.product.qaid}
											
											{#if item.product.rg === 'Y'}
												<Icon data={faRocket} class="mx-0.5 text-red-700" />
											{/if}
										</span>
								</td>
								<td class="min-w-[120px] max-w-[200px] p-0.5 text-center">
									<div class="flex items-center">
										<div class="w-1/2 p-0">{item.product.cate4.name}</div>
										{#if item.product.cate5}
											<div class="w-1/2 p-0">{item.product.cate5.name}</div>
										{/if}
									</div>
								</td>
								<td class="min-w-[100px] p-0.5">
									{item.product.name}
								</td>
								<td class="min-w-[80px] max-w-[80px] p-0.5 text-right">
									{getNumberFormat(item.product.amount)} 원
								</td>
								<td class="min-w-[75px] max-w-[75px] p-0.5 text-center">
									{warehousePalletItemStatusName(item.status)}
								</td>
								<td class="min-w-[80px] max-w-[80px] p-0.5 text-center">{formatDateTimeToString(item.product.checked_at)}</td>
								<td class="min-w-[45px] max-w-[45px] p-0.5 text-center">
									{#if item.product.checked_user}
										{item.product.checked_user.name}
									{/if}
								</td>
								{#if palletStatus !== WAREHOUSE_PALLET_STATUS_COMPLETED}
									<td class="min-w-24 p-0.5 text-right">
										<button
											class="btn btn-warning btn-xs"
											data-product-id={item.product.id}
											onclick={(e) => cancelCheckedPalletProduct(e, index)}
										>
											<Icon data={faDeleteLeft} />
											입고취소
										</button>
									</td>
								{/if}
							</tr>
						{/each}
					{/if}
					</tbody>
				</table>
			</div>
		</section>
	{/snippet}
</AppLayout>
