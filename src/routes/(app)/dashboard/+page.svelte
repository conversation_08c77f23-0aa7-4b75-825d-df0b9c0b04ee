<script lang="ts">
	import { onMount } from 'svelte';
	import { getUser, type User } from '$lib/User';
	import type { Breadcrumb } from '$lib/types';

	import { authClient } from '$lib/AxiosBackend';
	import { initDB, saveData } from '$lib/indexedDBHelper';

	import AppLayout from '$components/Layouts/AppLayout.svelte';
	import TitleBar from '$components/UI/TitleBar.svelte';

	let user: User = getUser();

	// 서버로 부터 출고점검에 필요한 "process data" 들을 가져옴
	async function getProcessesFromApi() {
		try {
			const storeName = 'processes';
			await initDB(storeName);

			const { status, data } = await authClient.get(`/resources/precesses`);

			if (status && data.success) {
				const items = data.data.items;

				await saveData(storeName, items);
			}
		} catch (e) {
			console.error('Error: ', e);
		}
	}

	onMount(async () => {
		await getProcesses<PERSON>rom<PERSON>pi();
	});

	const breadcrumbs: Breadcrumb[] = [
		{ title: '대시보드', url: '#' },
	];
</script>

<svelte:head>
	<title>대시보드</title>
</svelte:head>

<AppLayout {user}>
	{#snippet main()}
		<div >
			<TitleBar {breadcrumbs} />
			
			<section class="main-section">
				<div class="w-full p-2 flex flex-col-reverse lg:flex-row items-center justify-center xl:justify-between">
					
					<ul class="text-sm list-disc list-inside">
						<li>페이지 새로고침: F5키 또는 ESC키</li>
						<li>이전 페이지로 이동: Alt + Left Arrow(왼쪽 방향 화살표) 키 / 마우스 좌측 1 버튼</li>
						<li>다음 페이지로 이동: Alt + Right Arrow(오른쪽 방향 화살표) 키 / 마우스 좌측 2 버튼</li>
						<li>
							오류가 난다면 어떤 작업을 하다가 어떻게 오류가 났는지 상세하게 알려 주세요.(가능하면 오류 메시지를 캡쳐해서 보여 주세요.)
						</li>
					</ul>
				
				</div>
			</section>
		</div>
	{/snippet}
</AppLayout>