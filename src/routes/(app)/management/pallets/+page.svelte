<script lang="ts">
	import { getUser, type User } from '$lib/User';
	import { onMount, tick } from 'svelte';
	import { page } from '$app/state';
	import type { Breadcrumb } from '$lib/types';

	import { authClient } from '$lib/AxiosBackend';
	import { toast } from 'svoast';

	import { getLocationEnableName, loadItems, locationStore } from '$stores/locationStore';
	import { executeMessage, getNumberFormat, handleCatch } from '$lib/Functions';

	import AppLayout from '$components/Layouts/AppLayout.svelte';
	import Loading from '$components/Loading/Circle2.svelte';
	import TitleBar from '$components/UI/TitleBar.svelte';
	import SearchUI from '$components/UI/SearchUI.svelte';
	import TableTop from '$components/UI/TableTop.svelte';
	import Paginate from '$components/UI/Paginate.svelte';
	import SearchLocation from '$components/Snippets/SearchLocation.svelte';
	import SearchLocationEnable from '$components/Snippets/SearchLocationEnable.svelte';

	import Icon from 'svelte-awesome';
	import { faSave } from '@fortawesome/free-regular-svg-icons/faSave';
	import { faXmark } from '@fortawesome/free-solid-svg-icons/faXmark';

	let user: User = getUser();
	const apiUrl = '/wms/settings/locations';
	const localUrl = page.url.pathname;
	let isLoading = $state(false); // 로딩중 표시 여부

	// 검색 query string 시작 ==========
	let enable = $state('Y');
	let store = $state('');
	let line = $state('');
	let rack = $state('');
	let level = $state('');
	let column = $state('');
	let p = $state(page.url.searchParams.get('p') || '1');
	let pageSize = $state(page.url.searchParams.get('pageSize') || '100');
	let searchParams = $state('');
	let apiSearchParams = '';
	// 검색 query string 종료 ==========

	// 페이징 관련 변수 시작 =============
	let links: [] = $state([]);
	let total = $state(0);
	let startNo = $state(0);
	// 페이징 관련 변수 종료 =============

	// 모달창: 미등록상품 등록/수정 창 시작========
	let modal: HTMLDialogElement;
	let modalTypeText = $state('수정');

	let modalId = 0;
	let modalStore = $state('A');
	let modalLine = $state('1');
	let modalRack = $state('1');
	let modalLevel = $state('');
	let modalColumn = $state('');
	let modalLocationName = $state('');
	let modalEnable = $state('Y');

	// 모달창: 미등록상품 등록/수정 창 종료========

	async function makeData() {
		isLoading = true;

		const common_params = {
			enable: enable,
			store: store,
			line: line,
			rack: rack,
			level: level,
			column: column,
			pageSize: pageSize
		};

		const params = new URLSearchParams({ p: p, ...common_params });
		const api_params = new URLSearchParams({ page: p, ...common_params });

		searchParams = params.toString(); // local
		apiSearchParams = api_params.toString(); // api

		await loadItems(`${apiUrl}?${apiSearchParams}`, user);

		if ($locationStore) {
			links = JSON.parse($locationStore.pageLinks);
			total = $locationStore.pageTotal ?? 0;
			startNo = $locationStore.pageTotal - $locationStore.pageFrom + 1;
		}

		isLoading = false;
	}

	/**
	 * 자식 컴포넌트에서 변경된 변수의 값을 매칭
	 *
	 * @param value
	 */
	function changeSearchParams(value) {
		if (value.detail.p) p = value.detail.p;
		if (value.detail.pageSize) pageSize = value.detail.pageSize;
		if (value.detail.locationEnableGroup) enable = value.detail.locationEnableGroup;
		if (value.detail.store || value.detail.store === '') store = value.detail.store;
		if (value.detail.line || value.detail.line === '') line = value.detail.line;
		if (value.detail.rack || value.detail.rack === '') rack = value.detail.rack;
		if (value.detail.level || value.detail.level === '') level = value.detail.level;
		if (value.detail.column || value.detail.column === '') column = value.detail.column;

		makeData();
	}

	async function updateModalLocation(id: number) {
		const loc = $locationStore.items.find(item => item.id === id);

		modalTypeText = '수정';

		modalId = loc.id;
		modalStore = loc.pallet_info.store;
		modalLine = loc.pallet_info.line;
		modalRack = loc.pallet_info.rack;
		modalLevel = loc.pallet_info.level;
		modalColumn = loc.pallet_info.column;
		modalLocationName = loc.name;
		modalEnable = loc.enable;
		await tick();

		modal.showModal();
	}

	async function handleUpdateModal(e) {
		e.preventDefault();

		if (!modalStore) modalStore = 'A';
		if (!modalLine) modalLine = '1';
		if (!modalRack) modalRack = '1';

		if (!modalLevel) {
			await executeMessage('팔레트를 입력해 주세요.', 'error');
			return false;
		}

		if (!modalColumn) {
			await executeMessage('팔레트 번호를 입력해 주세요.', 'error');
			return false;
		}

		if (!modalLocationName) {
			await executeMessage('팔레트 설명을 입력해 주세요.', 'error');
			return false;
		}

		try {
			const payload = {
				id: modalId,
				store: modalStore,
				line: modalLine,
				rack: modalRack,
				level: modalLevel,
				column: modalColumn,
				name: modalLocationName,
				enable: modalEnable
			};

			const result = await authClient.put(`${apiUrl}`, payload);

			if (result.status === 200 && result.data.success) {
				modal.close();
				toast.success(`${modalTypeText} 되었습니다.`);

				await makeData();
			} else {
				await executeMessage(result.data.data.message, 'error');
			}
		} catch (e) {
			toast.error('팔레트 정보수정에 문제가 있습니다.\n프로그래머에게 문의 해 주세요.');
			await handleCatch(e);
		}
	}

	onMount(async () => {
		locationStore.set('');

		await makeData();

		modal = document.getElementById('my_modal_1') as HTMLDialogElement;
	});

	const breadcrumbs: Breadcrumb[] = [
		{ title: '관리', url: '/management/locations' },
		{ title: '입고 팔레트 관리', url: '/management/locations' },
	];
</script>

<svelte:head>
	<title>관리 > 입고(창고) 팔레트 관리</title>
</svelte:head>

{#if isLoading}
	<Loading size="60" unit="px" />
{/if}

<AppLayout {user}>
	{#snippet main()}
		<div >
			<TitleBar {breadcrumbs} />
			
			<section class="main-section">
				<SearchUI>
					<SearchLocation column={column} level={level} line={line}
													onUpdate={changeSearchParams}
													rack={rack} store={store} />
					<SearchLocationEnable locationEnableGroup={enable} onUpdate={changeSearchParams} />
				</SearchUI>
				
				<!-- 리스트 시작 -->
				<div class="overflow-x-auto px-2">
					<TableTop onUpdate={changeSearchParams} {pageSize} {total}>
					</TableTop>
					
					<table class="table text-xs table-pin-rows">
						<thead class="uppercase">
						<tr class="bg-base-content text-base-300 text-center">
							<th rowspan="2">번호</th>
							<th colspan="3">위치</th>
							<th rowspan="2">팔레트번호</th>
							<th rowspan="2">설명</th>
							<th rowspan="2">적재수량</th>
							<th rowspan="2">유효</th>
							<th rowspan="2">수정</th>
						</tr>
						<tr class="bg-base-content text-base-300 text-center">
							<th>존</th>
							<th>층</th>
							<th>번</th>
						</tr>
						</thead>
						
						<tbody>
						{#if $locationStore.items}
							{#each $locationStore.items as item, index}
								<tr class="text-center">
									<td>
										<input name="id" type="hidden" value="14981">
										<div class="num">
											{getNumberFormat(startNo - index)}
										</div>
									</td>
									<td>
										{item.pallet_info.store}
									</td>
									<td>
										{item.pallet_info.line}
									</td>
									<td>
										{item.pallet_info.rack}
									</td>
									<td class="text-center border-r-0">
										{item.pallet_info.level}-{item.pallet_info.column}
									</td>
									<td class="border-l-0">
										{item.name}
									</td>
									<td class="text-right">
										{item.pcount}
									</td>
									<td>
										{getLocationEnableName(item.enable)}
									</td>
									<td>
										<button class="btn btn-primary py-0 px-2 btn-sm w-12"
														onclick={() => updateModalLocation(item.id)}
														type="button">
											수정
										</button>
									</td>
								</tr>
							{/each}
						{/if}
						</tbody>
					</table>
				</div>
				
				<!-- Pagination -->
				<Paginate links={links}
									localUrl={localUrl}
									on:change={changeSearchParams}
									pageCurrentPage={$locationStore.pageCurrentPage}
									pageNextPageUrl={$locationStore.pageNextPageUrl}
									pagePrevPageUrl={$locationStore.pagePrevPageUrl}
									searchParams={searchParams}
				/>
			</section>
			
			<dialog class="modal" id="my_modal_1">
				<div class="modal-box w-1/2 max-w-2xl">
					<form method="dialog">
						<button class="btn btn-sm btn-circle btn-ghost absolute right-2 top-2">✕</button>
					</form>
					
					<h3 class="font-bold text-lg">팔레트 위치 {modalTypeText}</h3>
					
					<div class="mt-4 grid grid-cols-3 gap-4">
						<div>
							<label class="label text-sm font-bold" for="modal_store">존</label>
							<input bind:value={modalStore}
										 class="w-full py-0 border-0 border-b-2 border-b-neutral-300 focus:ring-0 focus:outline-none"
										 id="modal_store" type="text" />
						</div>
						<div>
							<label class="label text-sm font-bold" for="modal_line">층</label>
							<input bind:value={modalLine}
										 class="w-full py-0 border-0 border-b-2 border-b-neutral-300 focus:ring-0 focus:outline-none"
										 id="modal_line"
										 min="1" step="1" type="number">
						</div>
						<div>
							<label class="label text-sm font-bold" for="modal_rack">번</label>
							<input bind:value={modalRack}
										 class="w-full py-0 border-0 border-b-2 border-b-neutral-300 focus:ring-0 focus:outline-none"
										 id="modal_rack"
										 min="1" step="1" type="number">
						</div>
					</div>
					
					<div class="mt-4 grid grid-cols-2 gap-4">
						<div>
							<label class="label text-sm font-bold" for="modal_level">팔레트</label>
							<input bind:value={modalLevel}
										 class="w-full py-0 border-0 border-b-2 border-b-neutral-300 focus:ring-0 focus:outline-none"
										 id="modal_level" type="text" />
						</div>
						<div>
							<label class="label text-sm font-bold" for="modal_column">번호</label>
							<input bind:value={modalColumn}
										 class="w-full py-0 border-0 border-b-2 border-b-neutral-300 focus:ring-0 focus:outline-none"
										 id="modal_column"
										 min="1" step="1" type="number">
						</div>
					</div>
					
					<div class="mt-2">
						<label class="label text-sm font-bold" for="modal_location_name">설명</label>
						<input bind:value={modalLocationName}
									 class="w-full py-0 border-0 border-b-2 border-b-neutral-300 focus:ring-0 focus:outline-none"
									 id="modal_location_name" type="text">
					</div>
					
					<div class="mt-2">
						<label class="label text-sm font-bold" for="modal_enable">사용 가능 여부</label>
						<select bind:value={modalEnable} id="modal_enable" name="modal_enable">
							<option value="Y">적재가능</option>
							<option value="N">사용불가</option>
						</select>
					</div>
					
					<div class="modal-action flex justify-between">
						<div>
							<button class="btn btn-primary"
											onclick={async (e) => await handleUpdateModal(e)}
											type="submit"
							>
								<Icon class="w-5 h-5" data={faSave} />
								팔레트 {modalTypeText}
							</button>
						</div>
						<div>
							<form method="dialog">
								<!-- if there is a button in form, it will close the modal -->
								<button class="btn btn-warning tooltip tooltip-left"
												data-tip="키보드의 Escape 키를 누르면 닫힙니다."
								>
									<Icon class="w-5 h-5" data={faXmark} />
									닫기
								</button>
							</form>
						</div>
					</div>
				</div>
			</dialog>
		</div>
	{/snippet}
</AppLayout>