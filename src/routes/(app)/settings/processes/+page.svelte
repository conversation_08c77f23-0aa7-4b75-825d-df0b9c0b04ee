<script lang="ts">
	import { getUser } from '$lib/User';
	import { onMount } from 'svelte';
	import { page } from '$app/state';
	import type { Breadcrumb } from '$lib/types';

	import { authClient } from '$lib/AxiosBackend';
	import { toast } from 'svoast';

	import { checkAdminAndRedirect, executeAsk, executeMessage, getNumberFormat, handleCatch, redirectUrl } from '$lib/Functions';
	import { loadProcesses, processStore, processTypes } from '$stores/processStore';

	import AppLayout from '$components/Layouts/AppLayout.svelte';
	import TitleBar from '$components/UI/TitleBar.svelte';

	import Icon from 'svelte-awesome';
	import { faSearch } from '@fortawesome/free-solid-svg-icons/faSearch';
	import { faPlus } from '@fortawesome/free-solid-svg-icons/faPlus';
	import { faRotate } from '@fortawesome/free-solid-svg-icons/faRotate';
	import { faPenToSquare } from '@fortawesome/free-regular-svg-icons/faPenToSquare';
	import { faTrashCan } from '@fortawesome/free-regular-svg-icons/faTrashCan';

	const user = getUser();

	// 관리자인지 체크
	checkAdminAndRedirect(user, '/dashboard');

	const apiUrl = '/wms/settings/processes';
	const localUrl = page.url.pathname;
	let isLoading = $state(false); // 로딩중 표시 여부

	let total = $state(0);

	// 검색관련 시작==========
	let typeSelected = $state('');
	let processCode = $state('');
	let processName = $state('');
	let p = $state(page.url.searchParams.get('p') || '1');
	let pageSize = $state(page.url.searchParams.get('pageSize') || '200');
	let apiSearchParams = '';

	// 검색관련 끝==========

	async function makeData() {
		isLoading = true;

		const common_params = {
			type: typeSelected,
			code: processCode,
			name: processName,
			pageSize: pageSize
		};

		const api_params = new URLSearchParams({ page: p, ...common_params });

		apiSearchParams = api_params.toString(); // api

		await loadProcesses(`${apiUrl}?${apiSearchParams}`, user);

		if ($processStore) {
			total = $processStore.length;
		}

		isLoading = false;
	}

	// 점검코드 등록
	async function handleStore(event: Event) {
		event.preventDefault();

		if (!processCode) {
			await executeMessage('등록하실 코드를 입력해 주세요.');

			return false;
		}

		if (!processName) {
			await executeMessage('등록하실 코드의 설명(이름)을 입력해 주세요.');
			return false;
		}

		try {
			const payload = {
				type: typeSelected,
				code: processCode,
				name: processName
			};
			const { status, data } = await authClient.post(apiUrl, payload);

			if (status === 200 && data.success) {
				toast.success('저장 되었습니다.');

				typeSelected = '';
				processCode = '';
				processName = '';

				await makeData();
			}
		} catch (e) {
			await handleCatch(e);
		}
	}

	async function handleModify(item: object) {
		if (!item.id) {
			await executeMessage('수정할 점검코드에 오류가 있습니다.\n프로그래머에게 문의해 주세요.', 'error');
			return false;
		}

		isLoading = true;
		
		try {
			const payload = {
				type: item.type,
				code: item.code,
				name: item.name
			};
			const { status, data } = await authClient.put(`${apiUrl}/${item.id}`, payload);

			if (status === 200 && data.success) {
				await executeMessage('수정 되었습니다.');
			} else {
				await executeMessage(data.data.message, 'error');
			}
		} catch (e) {
			await handleCatch(e);
		}
		
		isLoading = false;
	}

	async function handleDelete(id: string) {
		const result = await executeAsk('정말 삭제 하시겠습니까', 'warning');
		if (!result) {
			return false;
		}
		
		if (!id) {
			await executeMessage('점검코드 삭제에 오류가 있습니다. 프로그래머에게 문의해 주세요.', 'error');

			return false;
		}

		isLoading = true;
		
		try {
			const { status, data } = await authClient.delete(`${apiUrl}/${id}`);

			if (status === 200 && data.success) {
				await executeMessage('점검코드가 삭제 되었습니다.');
				await redirectUrl(localUrl);
			} else {
				await executeMessage(data.data.message, 'error');
			}
		} catch (e) {
			await handleCatch(e);
		}
		
		isLoading = false;
	}

	onMount(async () => {
		await makeData();
	});

	const breadcrumbs: Breadcrumb[] = [
		{ title: '설정', url: '/settings/processes' },
		{ title: '점검 코드 설정', url: '/settings/processes' },
	];
</script>

<svelte:head>
	<title>설정 > 점검 코드 설정</title>
</svelte:head>

<AppLayout {user}>
	{#snippet main()}
		<div >
			<TitleBar {breadcrumbs} />
			
			<section class="main-section">
				<div class="w-full px-3 flex flex-col-reverse lg:flex-row items-center justify-center xl:justify-between">
					<div class="flex py-1.5 space-y-3 md:space-y-0 md:space-x-3">
						<div class="flex flex-col md:flex-row md:space-y-0 md:space-x-3">
							<div class="relative">
								<select bind:value={typeSelected}
												class="select select-bordered select-sm"
												onchange={makeData}
								>
									<option value="">검수코드(전체)</option>
									{#each $processTypes as type}
										<option value={type.value}>{type.text}</option>
									{/each}
								</select>
							</div>
							
							<div class="relative">
								<input bind:value={processCode}
											 class="input input-bordered input-sm w-40 rounded-lg"
											 onkeydown={e => { if (e.key === "Enter") { makeData(); }}}
											 placeholder="코드"
											 type="text"
								/>
							</div>
							
							<label class="sr-only" for="table-search">Search</label>
							<div>
								<input bind:value={processName}
											 class="input input-bordered input-sm w-40 rounded-lg"
											 onkeydown={e => { if (e.key === "Enter") { makeData(); }}}
											 placeholder="설명(이름)"
											 type="text"
								/>
							</div>
							<div>
								<button class="btn btn-accent btn-sm p-1.5 w-20"
												id="search_button"
												onclick={makeData}
												type="button"
								>
									<Icon data={faSearch} />
									검색
								</button>
								
								<button class="btn btn-error btn-sm p-1.5 w-20"
												onclick={() => redirectUrl(localUrl)}
												type="button"
								>
									<Icon data={faRotate} />
									초기화
								</button>
								
								<button class="btn btn-primary btn-sm p-1.5 w-20"
												onclick={handleStore}
												type="button"
								>
									<Icon data={faPlus} />
									추가
								</button>
							</div>
						</div>
					</div>
				</div>
				
				<div class="overflow-x-auto w-full">
					
					<div class="w-full px-4 py-2">
						<h5>
							<span>점검코드 전체 </span>
							<span>{total}</span>
						</h5>
					</div>
					
					<table class="table text-xs table-pin-rows">
						<thead class="uppercase">
						<tr class="bg-base-content text-base-300 text-center">
							<th>번호</th>
							<th>구분</th>
							<th>코드</th>
							<th>설명(이름)</th>
							<th></th>
						</tr>
						</thead>
						
						<tfoot class="uppercase">
						<tr class="bg-base-300">
							<th>번호</th>
							<th>구분</th>
							<th>코드</th>
							<th>설명(이름)</th>
							<th></th>
						</tr>
						</tfoot>
						
						<tbody>
						{#if $processStore}
							{#each $processStore as item, index}
								<tr class="hover:bg-base-content/10">
									<td class="px-4 py-0 text-center">
										{getNumberFormat(total - index)}
									</td>
									<td class="px-4 py-0">
										<select bind:value={item.type} class="select select-bordered select-sm">
											{#each $processTypes as type}
												{#if type.value === item.type}
													<option value={type.value} selected>{type.text}</option>
												{:else}
													<option value={type.value}>{type.text}</option>
												{/if}
											{/each}
										</select>
									</td>
									<td class="px-4 py-0">
										<input bind:value={item.code}
													 type="text"
													 class="input input-bordered input-sm"
										/>
									</td>
									<td class="px-4 py-0">
										<input bind:value={item.name}
													 type="text"
													 class="input input-bordered input-sm w-full max-w-xs"
										/>
									</td>
									<td class="px-4 py-0">
										<div class="w-full flex justify-center">
											<button class="btn btn-ghost btn-sm px-1 py-0 m-0"
															onclick={() => handleModify(item)}
											>
												<Icon data={faPenToSquare} />
											</button>
											
											<button class="btn btn-ghost btn-sm px-1 py-0 m-0"
															onclick={() => handleDelete(String(item.id))}
											>
												<Icon data={faTrashCan} />
											</button>
										</div>
									</td>
								</tr>
							{/each}
						{/if}
						</tbody>
					</table>
				</div>
			</section>
		</div>
	{/snippet}
</AppLayout>
