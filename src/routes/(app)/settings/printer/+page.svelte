<script lang="ts">
	import type { Breadcrumb } from '$lib/types';

	import AppLayout from '$components/Layouts/AppLayout.svelte';
	import TitleBar from '$components/UI/TitleBar.svelte';
	import SelectPrinter from '$components/UI/SelectPrinter.svelte';
	import { getUser, type User } from '$lib/User';

	let user: User = getUser();

	const breadcrumbs: Breadcrumb[] = [
		{ title: '설정', url: '/settings/printer' },
		{ title: '라벨 프린터 설정', url: '/settings/printer' }
	];
</script>

<svelte:head>
	<title>설정 > 라벨 프린터 설정</title>
</svelte:head>

<AppLayout {user}>
	{#snippet main ()}
		<div>
			<TitleBar {breadcrumbs} />
			
			<section class="main-section">
				<section class="main-section">
					<SelectPrinter className="pb-1.5" />
				</section>
			</section>
		</div>
	{/snippet}
</AppLayout>
