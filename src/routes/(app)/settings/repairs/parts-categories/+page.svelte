<script lang="ts">
	import { getUser } from '$lib/User';
	import { onMount } from 'svelte';
	import { page } from '$app/state';
	import type { Breadcrumb } from '$lib/types';

	import { authClient } from '$lib/AxiosBackend';
	import { toast } from 'svoast';

	import {
		executeAsk,
		executeMessage,
		handleCatch
	} from '$lib/Functions';

	import AppLayout from '$components/Layouts/AppLayout.svelte';
	import TitleBar from '$components/UI/TitleBar.svelte';

	import Icon from 'svelte-awesome';
	import { faPlus } from '@fortawesome/free-solid-svg-icons/faPlus';
	import { faPenToSquare } from '@fortawesome/free-regular-svg-icons/faPenToSquare';
	import { faTrashCan } from '@fortawesome/free-regular-svg-icons/faTrashCan';
	import { faSave } from '@fortawesome/free-regular-svg-icons/faSave';

	const user = getUser();
	const apiUrl = '/wms/settings/repairs/parts-categories';
	const localUrl = page.url.pathname;
	let isLoading = $state(false);
	let modal: HTMLDialogElement;

	// 카테고리 데이터
	let categories = $state([]);
	let categoryName = $state('');
	let categoryParentId = $state(null);
	let categoryLevel = $state(1);
	let categoryOrderNo = $state(0);

	// 카테고리 목록 로드
	async function loadCategories() {
		isLoading = true;
		try {
			const { status, data } = await authClient.get(`${apiUrl}`);
			if (status === 200 && data.success) {
				categories = data.data.items;
			}
		} catch (e) {
			await handleCatch(e);
		}
		isLoading = false;
	}

	// 카테고리 생성
	async function handleCreate() {
		if (!categoryName) {
			await executeMessage('카테고리 이름을 입력해 주세요.');
			return false;
		}

		try {
			const calculatedLevel = calculateLevel(categoryParentId);
			const payload = {
				name: categoryName,
				parent_id: categoryParentId,
				level: calculatedLevel,
				order_no: categoryOrderNo
			};

			const { status, data } = await authClient.post(`${apiUrl}`, payload);

			if (status === 200 && data.success) {
				await executeMessage('등록되었습니다.');

				categoryName = '';
				categoryParentId = null;
				categoryLevel = 1;
				categoryOrderNo = 0;

				modal.close();
				await loadCategories();
			}
		} catch (e) {
			await handleCatch(e);
		}
	}

	// 카테고리 수정 시 레벨 자동 업데이트
	async function handleUpdate(category: any) {
		if (!category.name) {
			await executeMessage('카테고리 이름을 입력해 주세요.');
			return false;
		}

		try {
			// 상위 카테고리가 변경되었다면 레벨도 함께 업데이트
			const newLevel = calculateLevel(category.parent_id);

			const payload = {
				name: category.name,
				parent_id: category.parent_id,
				level: newLevel,
				order_no: category.order_no
			};

			const { status, data } = await authClient.put(`${apiUrl}/${category.id}`, payload);

			if (status === 200 && data.success) {
				toast.success('수정되었습니다.');
				await loadCategories();
			}
		} catch (e) {
			await handleCatch(e);
		}
	}

	// 카테고리 삭제
	async function handleDelete(id: number) {
		const ask = await executeAsk('카테고리를 삭제하시겠습니까?\n\n하위 카테고리도 모두 삭제됩니다.');
		if (!ask) return false;

		try {
			const { status, data } = await authClient.delete(`${apiUrl}/${id}`);

			if (status === 200 && data.success) {
				toast.success('삭제되었습니다.');
				await loadCategories();
			}
		} catch (e) {
			await handleCatch(e);
		}
	}

	// 카테고리 생성 시 레벨 자동 계산
	function calculateLevel(parentId: number | null): number {
		if (!parentId) return 1;
		const parentCategory = categories.find(c => c.id === parentId);
		return parentCategory ? parentCategory.level + 1 : 1;
	}

	// 부모 카테고리 선택 가능 여부 확인
	function isValidParent(categoryId: number, parentId: number): boolean {
		// 자기 자신을 부모로 선택할 수 없음
		if (categoryId === parentId) return false;

		// 자신의 하위 카테고리를 부모로 선택할 수 없음
		const childCategories = categories.filter(c => c.parent_id === categoryId);
		return !childCategories.some(child =>
			child.id === parentId || isValidParent(categoryId, child.id)
		);
	}

	// 부모 카테고리 변경 시 처리
	function handleParentChange(category: any, newParentId: number | null) {
		if (newParentId && !isValidParent(category.id, newParentId)) {
			executeMessage('올바르지 않은 상위 카테고리 선택입니다.');
			category.parent_id = null;
			return;
		}
		category.parent_id = newParentId;
	}

	onMount(async () => {
		await loadCategories();
		modal = document.getElementById('category_modal') as HTMLDialogElement;
	});

	const breadcrumbs: Breadcrumb[] = [
		{ title: '설정', url: '/settings/repairs/parts' },
		{ title: '구성품 카테고리 관리', url: '/settings/repairs/parts-categories' },
	];
</script>

<svelte:head>
	<title>설정 > 구성품 카테고리 관리</title>
</svelte:head>

<AppLayout {user}>
	{#snippet main()}
		<div>
			<TitleBar {breadcrumbs} />
			
			<section class="main-section">
				<div class="px-2">
					<div class="flex justify-end mb-4">
						<button class="btn btn-primary btn-sm"
										onclick={() => modal.showModal()}
										type="button">
							<Icon class="w-4 h-4 mr-2" data={faPlus} />
							카테고리 등록
						</button>
					</div>
					
					<div class="overflow-x-auto">
						<table class="table">
							<thead>
							<tr>
								<th class="w-16">순서</th>
								<th class="w-16">레벨</th>
								<th>카테고리명</th>
								<th class="w-32">상위 카테고리</th>
								<th class="w-32">관리</th>
							</tr>
							</thead>
							<tbody>
							{#each categories as category}
								<tr>
									<td>
										<input bind:value={category.order_no}
													 class="input input-bordered input-sm w-16 text-center"
													 type="number" />
									</td>
									<td class="text-center">
										{category.level}
									</td>
									<td>
										<input bind:value={category.name}
													 class="input input-bordered input-sm w-full"
													 type="text" />
									</td>
									<td>
										<select bind:value={category.parent_id}
														onchange={() => handleParentChange(category, category.parent_id)}
														class="select select-bordered select-sm w-full">
											<option value={null}>없음</option>
											{#each categories.filter(c => c.id !== category.id) as parent}
												{#if isValidParent(category.id, parent.id)}
													<option value={parent.id}>{parent.name}</option>
												{/if}
											{/each}
										</select>
									</td>
									<td class="text-center">
										<button class="btn btn-ghost btn-sm"
														onclick={() => handleUpdate(category)}>
											<Icon data={faPenToSquare} />
										</button>
										<button class="btn btn-ghost btn-sm"
														onclick={() => handleDelete(category.id)}>
											<Icon data={faTrashCan} />
										</button>
									</td>
								</tr>
							{/each}
							</tbody>
						</table>
					</div>
				
				</div>
			</section>
			
			<dialog class="modal" id="category_modal">
				<div class="modal-box w-1/2 max-w-2xl">
					<form method="dialog">
						<button class="btn btn-sm btn-circle btn-ghost absolute right-2 top-2">✕</button>
					</form>
					
					<h3 class="font-bold text-lg">카테고리 등록</h3>
					
					<div class="mt-4 space-y-4">
						<div>
							<label class="label text-sm font-bold">카테고리명</label>
							<input bind:value={categoryName}
										 class="input input-bordered w-full"
										 placeholder="카테고리명을 입력하세요"
										 type="text" />
						</div>
						
						<div class="grid grid-cols-2 gap-4">
							<div>
								<label class="label text-sm font-bold">상위 카테고리</label>
								<select bind:value={categoryParentId}
												class="select select-bordered w-full">
									<option value={null}>없음</option>
									{#each categories as category}
										<option value={category.id}>{category.name}</option>
									{/each}
								</select>
							</div>
							
							<div>
								<label class="label text-sm font-bold">정렬 순서</label>
								<input bind:value={categoryOrderNo}
											 class="input input-bordered w-full"
											 type="number" />
							</div>
						</div>
					</div>
					
					<div class="modal-action flex justify-between">
						<button class="btn btn-primary"
										onclick={() => handleCreate()}
										type="button">
							<Icon class="w-5 h-5 mr-2" data={faSave} />
							등록
						</button>
						<form method="dialog">
							<button class="btn">닫기</button>
						</form>
					</div>
				</div>
			</dialog>
		</div>
	{/snippet}
</AppLayout>