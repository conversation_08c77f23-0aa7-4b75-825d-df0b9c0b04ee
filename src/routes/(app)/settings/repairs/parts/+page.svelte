<script lang="ts">
	import { getUser } from '$lib/User';
	import { onMount } from 'svelte';
	import { page } from '$app/state';
	import type { Breadcrumb } from '$lib/types';

	import { authClient } from '$lib/AxiosBackend';
	import { toast } from 'svoast';

	import {
		executeAsk,
		executeMessage,
		getNumberFormat,
		handleCatch
	} from '$lib/Functions';
	import { loadRepairParts, repairPartsStore } from '$stores/repairPartsStore';

	import AppLayout from '$components/Layouts/AppLayout.svelte';
	import TitleBar from '$components/UI/TitleBar.svelte';
	import TableTop from '$components/UI/TableTop.svelte';
	import Paginate from '$components/UI/Paginate.svelte';

	import Icon from 'svelte-awesome';
	import { faPlus } from '@fortawesome/free-solid-svg-icons/faPlus';
	import { faPenToSquare } from '@fortawesome/free-regular-svg-icons/faPenToSquare';
	import { faTrashCan } from '@fortawesome/free-regular-svg-icons/faTrashCan';
	import { faSearch } from '@fortawesome/free-solid-svg-icons/faSearch';
	import { faXmark } from '@fortawesome/free-solid-svg-icons/faXmark';
	import { faSave } from '@fortawesome/free-regular-svg-icons/faSave';

	const user = getUser();

	const apiUrl = '/wms/settings/repairs/parts';
	const localUrl = page.url.pathname;
	let isLoading = $state(false); // 로딩중 표시 여부
	let modal1: HTMLDialogElement;

	// 검색관련 시작==========
	let p = $state(page.url.searchParams.get('p') || '1');
	let pageSize = $state(page.url.searchParams.get('pageSize') || '15');
	let searchParams = $state('');
	let apiSearchParams = $state('');

	let keyword = $state('');
	// 검색관련 끝==========

	// 페이징 관련 변수 시작 =============
	let links: [] = $state([]);
	let total = $state(0);
	let startNo = $state(0);
	// 페이징 관련 변수 종료 =============

	// 카테고리와 구성품 관련 상태 추가
	let categories = $state([]);
	let selectedCategoryId = $state('');
	let parts = $state([]);

	let partsBarcode = $state('');
	let partsName = $state('');
	let partsCategory = $state('');
	let partsModelNumber = $state('');
	let partsPrice = $state(0);
	let partsStock = $state(0);
	let partsReorderStock = $state(10);
	let partsIsPurchasable = $state('Y');
	let partsMemo = $state('');

	async function makeData(search = false) {
		isLoading = true;

		if (search) {
			p = '1';
		}

		const common_params = {
			keyword: keyword,
			pageSize: pageSize
		};

		const params = new URLSearchParams({ p: p, ...common_params });
		const api_params = new URLSearchParams({ page: p, ...common_params });

		searchParams = params.toString(); // local
		apiSearchParams = api_params.toString(); // api

		await loadRepairParts(`${apiUrl}?${apiSearchParams}`, user);

		if ($repairPartsStore) {
			links = JSON.parse($repairPartsStore.pageLinks);
			total = $repairPartsStore.pageTotal ?? 0;
			startNo = $repairPartsStore.pageTotal - $repairPartsStore.pageFrom + 1;
		}

		isLoading = false;
	}

	function changeSearchParams(value) {
		if (value.detail.p) p = value.detail.p;
		if (value.detail.pageSize) pageSize = value.detail.pageSize;

		makeData();
	}

	// 카테고리 목록을 가져오는 함수
	async function loadCategories() {
		try {
			const { data } = await authClient.get('/wms/settings/repairs/parts-categories');
			if (data.success) {
				categories = data.data.items;
			}
		} catch (e) {
			await handleCatch(e);
		}
	}

	// 구성품 등록
	async function handleCreate() {
		if (!partsName) {
			await executeMessage('구성품 이름을 입력해 주세요.');

			return false;
		}

		if (!partsPrice || partsPrice < 0) {
			await executeMessage('구성품의 가격을 입력해 주세요.');

			return false;
		}

		try {
			const payload = {
				category_id: partsCategory,
				barcode: partsBarcode,
				name: partsName,
				model_number: partsModelNumber,
				price: partsPrice,
				stock: partsStock,
				reorder_stock: partsReorderStock,
				is_purchasable: partsIsPurchasable,
				memo: partsMemo
			};
			const { status, data } = await authClient.post(`${apiUrl}`, payload);

			if (status === 200 && data.success) {
				toast.success('등록 되었습니다.');
				
				partsCategory = '';
				partsBarcode = '';
				partsName = '';
				partsModelNumber = '';
				partsPrice = 0;
				partsStock = 0;
				partsReorderStock = 10;
				partsIsPurchasable = 'Y';
				partsMemo = '';
				
				modal1.close();

				await makeData();
			}
		} catch (e) {
			await handleCatch(e);
		}
	}

	// 구성품 수정
	async function handleUpdate(item: any) {
		if (!item.name) {
			await executeMessage('구성품 이름을 입력해 주세요.');

			return false;
		}

		if (!item.price) {
			await executeMessage('구성품의 가격을 입력해 주세요.');

			return false;
		}

		try {
			const payload = {
				category_id: item.category_id,
				barcode: item.barcode,
				name: item.name,
				model_number: item.model_number,
				price: item.price,
				stock: item.stock,
				reorder_stock: item.reorder_stock,
				is_purchasable: item.is_purchasable,
				memo: item.memo
			};
			const { status, data } = await authClient.put(`${apiUrl}/${item.id}`, payload);

			if (status === 200 && data.success) {
				toast.success('수정 되었습니다.');

				await makeData();
			}
		} catch (e) {
			await handleCatch(e);
		}
	}

	// 구성품 삭제
	async function handleDelete(id: number) {
		const ask = await executeAsk("구성품 가격 정보를 정말 삭제하시겠습니까?\n\n가격 정보가 삭제되면 엑셀 출력시 문제가 생길 수 있습니다.\n\n삭제 전 프로그래머와 상의해 주세요.");

		if (!ask) {
			return false;
		}

		try {
			const { status, data } = await authClient.delete(`${apiUrl}/${id}`);

			if (status === 200 && data.success) {
				toast.success('삭제 되었습니다.');

				window.location.reload();
			}
		} catch (e) {
			await handleCatch(e);
		}
	}

	onMount(async () => {
		repairPartsStore.set('');

		await makeData();
		await loadCategories();

		modal1 = document.getElementById('my_modal_1') as HTMLDialogElement;
	});

	const breadcrumbs: Breadcrumb[] = [
		{ title: '설정', url: '/settings/repairs/parts' },
		{ title: '구성품 설정', url: '/settings/repairs/parts' },
	];
</script>

<svelte:head>
	<title>설정 > 구성품 설정</title>
</svelte:head>

<AppLayout {user}>
	{#snippet main()}
		<div >
			<TitleBar {breadcrumbs} />

			<section class="main-section">
				<div class="w-full px-3 flex flex-col items-center justify-center xl:justify-between">
					<div class="w-full flex py-1.5 space-y-3 md:space-y-0 md:space-x-3">
						<div class="w-full p-4">
							<div>
								<label class="input input-bordered flex items-center justify-center gap-2 mx-2">
									<Icon data={faSearch} class="mb-2" />
									
									<input bind:value={keyword}
												 class="grow bg-base-100"
												 onkeydown={e => {if (e.key === "Enter") { makeData(); }}}
												 placeholder="바코드/구성품 검색"
												 type="text"
									/>
									<span onclick={() => {keyword = ''; makeData();}} role="presentation">
										<Icon class="cursor-pointer" data={faXmark} />
									</span>
								</label>
							</div>
						</div>
					</div>
				</div>

				<div class="px-2">
					<TableTop onUpdate={changeSearchParams} {pageSize} {total}>
						{#snippet right ()}
							<div class="pl-3">
								<button class="btn btn-primary btn-sm"
												onclick={() => {
													modal1.showModal();
												}}
												type="button"
								>
									<Icon class="w-4 h-4 mr-2" data={faPlus} />
									구성품 등록
								</button>
							</div>
						{/snippet}
					</TableTop>
					
					<div class="grid">
						<!-- 헤더 -->
						<div class="grid text-xs uppercase p-2 bg-base-content text-base-300 text-center"
								 style="grid-template-columns: 60px 170px 1.6fr 1fr 120px 110px 80px 100px 80px;">
							<div>번호</div>
							<div>카테고리<br>바코드</div>
							<div>구성품</div>
							<div>모델 번호</div>
							<div>가격</div>
							<div>재고</div>
							<div>구매 알림</div>
							<div>누적 사용량<br>구매 불가</div>
							<div></div>
						</div>
						
						<!-- 아이템 목록 -->
						{#if $repairPartsStore.items}
							{#each $repairPartsStore.items as item, index}
								<div class="grid gap-1 py-3 {index % 2 === 1 ? 'bg-base-200/50' : ''}">
									<!-- 첫 번째 행 -->
									<div class="grid items-center"
											 style="grid-template-columns: 60px 170px 1.6fr 1fr 120px 110px 80px 100px 80px;">
										<div class="text-center row-span-2">
											{getNumberFormat(startNo - index)}
										</div>
										<div class="px-1 row-span-2">
											<select
												bind:value={item.category_id}
												class="select select-bordered select-sm"
											>
												<option value={null}>카테고리 선택</option>
												{#each categories as category}
													<option value={category.id}>{category.name}</option>
												{/each}
											</select>
										</div>
										<div class="px-1">
											<input bind:value={item.name}
														 class="input input-bordered input-sm w-11/12"
														 placeholder="구성품 이름"
														 type="text"
											>
										</div>
										<div class="px-1">
											<input bind:value={item.model_number}
														 class="input input-bordered input-sm w-11/12"
														 placeholder="모델 번호"
														 type="text"
											>
										</div>
										<div class="px-1">
											<input bind:value={item.price}
														 class="input input-bordered input-sm w-10/12 text-right"
														 type="text"
											>
										</div>
										<div class="px-1">
											<input bind:value={item.stock}
														 class="input input-bordered input-sm w-10/12 text-right"
														 type="number"
											>
										</div>
										<div class="px-1">
											<input bind:value={item.reorder_stock}
														 class="input input-bordered input-sm w-10/12 text-right"
														 type="number"
											>
										</div>
										<div class="text-right px-1">
											{getNumberFormat(item.acc_count)} 개
										</div>
										<div class="text-center row-span-2 flex justify-center items-center gap-1">
											<button class="btn btn-ghost btn-sm px-1 py-0"
															onclick={() => handleUpdate(item)}>
												<Icon data={faPenToSquare} />
											</button>
											<button class="btn btn-ghost btn-sm px-1 py-0"
															onclick={() => handleDelete(item.id)}>
												<Icon data={faTrashCan} />
											</button>
										</div>
									</div>
									
									<!-- 두 번째 행 (메모) -->
									<div class="grid"
											 style="grid-template-columns: 60px 170px 1.6fr 1fr 120px 110px 80px 100px 80px;">
										<div></div>
										<div class="px-1">
											<input bind:value={item.barcode}
														 class="input input-bordered input-sm w-11/12"
														 placeholder="바코드"
														 type="text"
											>
										</div>
										<div class="col-span-5 col-start-3 px-1">
											<input bind:value={item.memo}
														 class="input input-bordered input-sm w-11/12"
														 placeholder="메모"
														 type="text">
										</div>
										<div class="text-right">
											<input checked={item.is_purchasable === 'N'}
														 onchange={() => item.is_purchasable = item.is_purchasable === 'Y' ? 'N' : 'Y'}
														 class="checkbox checkbox-sm"
														 type="checkbox">
										</div>
									</div>
								</div>
							{/each}
						{/if}
					</div>

					<!-- Pagination -->
					<Paginate links={links}
										localUrl={localUrl}
										onUpdate={changeSearchParams}
										pageCurrentPage={$repairPartsStore.pageCurrentPage}
										pageNextPageUrl={$repairPartsStore.pageNextPageUrl}
										pagePrevPageUrl={$repairPartsStore.pagePrevPageUrl}
										searchParams={searchParams}
					/>
				</div>
			</section>
			
			<dialog class="modal" id="my_modal_1">
				<div class="modal-box w-1/2 max-w-2xl">
					<form method="dialog">
						<button class="btn btn-sm btn-circle btn-ghost absolute right-2 top-2">✕</button>
					</form>
					
					<h3 class="font-bold text-lg">구성품 등록</h3>
					
					<div class="mt-2 grid grid-cols-2 gap-2">
						<div>
							<label class="label text-sm font-bold" for="parts_name">구성품명</label>
							<input bind:value={partsName}
										 class="input input-bordered w-11/12 rounded-lg p-2 leading-4 placeholder-base-content focus:ring-base-300 focus:border-base-300"
										 id="parts_name"
										 required
										 type="text"
							>
						</div>
						<div>
							<label class="label text-sm font-bold" for="parts_category">카테고리</label>
							<select
								bind:value={partsCategory}
								class="select select-bordered"
								id="parts_category"
							>
								<option value=''>카테고리 선택</option>
								{#each categories as category}
									<option value={category.id}>{category.name}</option>
								{/each}
							</select>
						</div>
					</div>
					
					<div class="mt-2 grid grid-cols-2 gap-2">
						<div>
							<label class="label text-sm font-bold" for="parts_barcode">바코드</label>
							<input bind:value={partsBarcode}
										 class="input input-bordered w-11/12 rounded-lg p-2 leading-4 placeholder-base-content focus:ring-base-300 focus:border-base-300"
										 id="parts_barcode"
										 type="text"
							>
						</div>
						<div>
							<label class="label text-sm font-bold" for="parts_model_number">모델 번호</label>
							<input bind:value={partsModelNumber}
										 class="input input-bordered w-11/12 rounded-lg p-2 leading-4 placeholder-base-content focus:ring-base-300 focus:border-base-300"
										 id="parts_model_number"
										 type="text"
							>
						</div>
					</div>
					
					<div class="mt-2 grid grid-cols-3 gap-2">
						<div>
							<label class="label text-sm font-bold" for="parts_price">금액</label>
							<input bind:value={partsPrice}
										 class="input input-bordered w-11/12 rounded-lg p-2 leading-4 placeholder-base-content focus:ring-base-300 focus:border-base-300"
										 id="parts_price"
										 required
										 type="number"
							/>
						</div>
						<div>
							<label class="label text-sm font-bold" for="parts_stock">
								재고 개수
							</label>
							<input bind:value={partsStock}
										 class="input input-bordered w-11/12 rounded-lg p-2 leading-4 placeholder-base-content focus:ring-base-300 focus:border-base-300"
										 id="parts_stock"
										 type="number"
							/>
						</div>
						<div>
							<label class="label text-sm font-bold" for="parts_reorder_stock">재구매 알림 개수</label>
							<input bind:value={partsReorderStock}
										 class="input input-bordered w-11/12 rounded-lg p-2 leading-4 placeholder-base-content focus:ring-base-300 focus:border-base-300"
										 id="parts_reorder_stock"
										 type="number"
							/>
						</div>
					</div>
					
					<div class="mt-2">
						<div>
							<label class="label text-sm font-bold" for="parts_memo">메모</label>
							<textarea bind:value={partsMemo}
												class="textarea textarea-bordered textarea-lg w-11/12"
												id="parts_memo"
												placeholder="메모를 입력해 주세요."
							></textarea>
						</div>
					</div>
					
					<div class="modal-action flex justify-between">
						<div>
							<button class="btn btn-primary"
											onclick={() => handleCreate()}
											type="button"
							>
								<Icon class="w-5 h-5" data={faSave} />
								구성품 등록
							</button>
						</div>
						<div>
							<form method="dialog">
								<!-- if there is a button in form, it will close the modal -->
								<button class="btn btn-warning tooltip tooltip-left"
												data-tip="키보드의 Escape 키를 누르면 닫힙니다."
								>
									<Icon class="w-5 h-5" data={faXmark} />
									닫기
								</button>
							</form>
						</div>
					</div>
				</div>
			</dialog>
		</div>
	{/snippet}
</AppLayout>
