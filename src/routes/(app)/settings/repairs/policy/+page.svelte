<script lang="ts">
	import type { Breadcrumb } from '$lib/types';
	import { onMount } from 'svelte';
	import { page } from '$app/state';
	import { getUser, type User } from '$lib/User';
	import { executeAsk, executeMessage, handleCatch } from '$lib/Functions';
	
	import AppLayout from '$components/Layouts/AppLayout.svelte';
	import TitleBar from '$components/UI/TitleBar.svelte';
	
	import Icon from 'svelte-awesome';
	import { faLink, faPenToSquare, faPlus, faTrashCan } from '@fortawesome/free-solid-svg-icons';
	import { authClient } from '$lib/AxiosBackend';

	// 각 모델에 대한 타입 정의 (실제 프로젝트에 맞게 조정 필요)
	interface RepairGrade {
		id: number;
		name: string;
		code: string;
	}

	interface RepairProcess {
		id: number;
		name: string;
		code: string;
		repair_grade_id: number;
	}

	interface RepairSymptom {
		id: number;
		name: string;
		code: string;
		type?: string;
		default_repair_process_id?: number;
		default_repair_grade_id?: number;
	}

	interface ProcessWithGrade extends RepairProcess {
		gradeName: string;
	}

	const user: User = getUser();
	const apiUrl = '/wms/settings/repairs';
	const localUrl = page.url.pathname;

	// 탭 관련 상태 변수
	let activeTab = $state<number>(0); // 0: 수리 등급, 1: 처리 내용, 2: 증상 내용, 3: 매핑

	// 데이터 상태 변수
	let grades = $state<RepairGrade[]>([]);
	let processes = $state<ProcessWithGrade[]>([]);
	let symptoms = $state<RepairSymptom[]>([]);
	let filteredSymptoms = $state<RepairSymptom[]>([]);
	let symptomTypeFilter = $state<string>('all'); // 'all', 'general', 'apple'

	// 변수::선택 및 매핑 관련 상태
	let selectedSymptom = $state<RepairSymptom | null>(null);
	let initialProcessIds = $state<Set<number>>(new Set()); // 서버에서 받은 초기 처리 내용 매핑 정보
	let flippedProcessIds = $state<Set<number>>(new Set()); // 임시로 선택된 처리 내용 매핑 정보

	// 변수::처리 내용 및 수리 등급 매핑 관련 상태
	let selectedProcess = $state<RepairProcess | null>(null);
	let initialGradeIds = $state<Set<number>>(new Set());
	let flippedGradeIds = $state<Set<number>>(new Set());

	// 증상 타입에 따라 필터링하는 함수
	function filterSymptomsByType() {
		if (symptomTypeFilter === 'all') {
			filteredSymptoms = symptoms;
		} else {
			filteredSymptoms = symptoms.filter(symptom => symptom.type === symptomTypeFilter);
		}

		// Add sequential IDs in reverse order
		filteredSymptoms = filteredSymptoms.map((symptom, index, array) => ({
			...symptom,
			sequentialId: array.length - index // Reverse order: total count down to 1
		}));
	}

	async function loadAllData() {
		try {
			// Promise.all을 사용하여 데이터 병렬 로드
			const [gradesRes, processesRes, symptomsRes] = await Promise.all([
				authClient.get(`${apiUrl}/grades`),
				authClient.get(`${apiUrl}/processes`),
				authClient.get(`${apiUrl}/symptoms`)
			]);

			grades = gradesRes.data.data.items ?? [];
			symptoms = symptomsRes.data.data.items ?? [];

			// 증상 필터링 적용
			filterSymptomsByType();

			const rawProcesses = processesRes.data.data.items ?? [];
			if (rawProcesses.length > 0) {
				processes = rawProcesses.map(p => ({
					...p,
					gradeName: grades.find(g => g.id === p.repair_grade_id)?.name ?? '미지정'
				}));
			} else {
				processes = [];
			}
		} catch (e) {
			await handleCatch(e);
		}
	}

	// 증상 선택 시, 연결된 처리 내용 로드
	async function selectSymptom(symptom: RepairSymptom) {
		selectedSymptom = symptom;

		let tempProcessIds: Set<number> = new Set();
		try {
			// API: 특정 증상에 연결된 처리 내용 ID 목록을 가져옵니다.
			const { data } = await authClient.get(`${apiUrl}/symptoms/${symptom.id}/processes`);
			if (data.success && data.data.item?.repair_processes) {
				tempProcessIds = new Set(data.data.item.repair_processes.map(p => p.id));
			}
		} catch (e) {
			await handleCatch(e);
		} finally {
			initialProcessIds = tempProcessIds;
			flippedProcessIds = new Set();
		}
	}

	// 처리 내용의 현재 선택 상태를 반환하는 헬퍼 함수
	function isProcessSelected(id: number): boolean {
		const initiallySelected = initialProcessIds.has(id);
		const hasBeenFlipped = flippedProcessIds.has(id);
		// 초기 상태와 클릭으로 인한 변경 상태가 다르면 선택된 것 (XOR)
		return initiallySelected !== hasBeenFlipped;
	}

	// 처리 내용 버튼 클릭 핸들러
	function handleProcessClick(processId: number) {
		const newFlipped = new Set(flippedProcessIds);
		if (newFlipped.has(processId)) {
			newFlipped.delete(processId); // 다시 클릭하면 변경 취소
		} else {
			newFlipped.add(processId); // 클릭하면 변경된 상태로 추가
		}
		flippedProcessIds = newFlipped;
	}

	// 매핑 정보 저장
	async function saveProcessMapping() {
		if (!selectedSymptom) return;

		const difference = getIds(initialProcessIds, flippedProcessIds);
		try {
			// API: 증상 ID와 연결할 처리 내용 ID 배열을 서버로 보냅니다 (Laravel의 sync 메소드 활용).
			await authClient.post(`${apiUrl}/symptoms/${selectedSymptom.id}/sync-processes`, {
				process_ids: Array.from(difference)
			});
			await executeMessage('매핑 정보가 저장되었습니다.');
		} catch (e) {
			await handleCatch(e);
		} finally {
			initialProcessIds = difference;
			flippedProcessIds.clear();
		}
	}

	// 처리 내용 선택 시, 연결된 수리 등급 로드
	async function selectProcess(process: RepairProcess) {
		selectedProcess = process;
		
		let tempGradeIds: Set<number> = new Set();
		try {
			// API: 특정 처리 내용에 연결된 수리 등급 ID 목록을 가져옵니다.
			const { data } = await authClient.get(`${apiUrl}/processes/${process.id}/grades`);
			if (data.success && data.data.item?.repair_grades) {
				tempGradeIds = new Set(data.data.item.repair_grades.map(g => g.id));
			}
		} catch (e) {
			await handleCatch(e);
		} finally {
			initialGradeIds = tempGradeIds;
			flippedGradeIds = new Set();
		}
	}

	// 수리 등급의 현재 선택 상태를 반환하는 헬퍼 함수
	function isGradeSelected(id: number): boolean {
		const initiallySelected = initialGradeIds.has(id);
		const hasBeenFlipped = flippedGradeIds.has(id);
		// 초기 상태와 클릭으로 인한 변경 상태가 다르면 선택된 것 (XOR)
		return initiallySelected !== hasBeenFlipped;
	}

	// 수리 등급 체크박스 변경 핸들러
	function handleGradeClick(id: number) {
		const newFlipped = new Set(flippedGradeIds);
		if (newFlipped.has(id)) {
			newFlipped.delete(id); // 다시 클릭하면 변경 취소
		} else {
			newFlipped.add(id); // 클릭하면 변경된 상태로 추가
		}
		flippedGradeIds = newFlipped;

		console.log('initialGradeIds', initialGradeIds);
		console.log('flippedGradeIds', flippedGradeIds);
	}

	// 처리 내용-수리 등급 매핑 정보 저장
	async function saveGradeMapping() {
		if (!selectedProcess) return;

		const difference = getIds(initialGradeIds, flippedGradeIds);

		try {
			// API: 처리 내용 ID와 연결할 수리 등급 ID 배열을 서버로 보냅니다.
			await authClient.post(`${apiUrl}/processes/${selectedProcess.id}/sync-grades`, {
				grade_ids: Array.from(difference)
			});
			await executeMessage('매핑 정보가 저장되었습니다.');
		} catch (e) {
			await handleCatch(e);
		} finally {
			initialGradeIds = difference;
			flippedGradeIds.clear();
		}
	}
	
	function getIds(initial: Set<number>, flipped: Set<number>) {
		return new Set(
			[...initial].filter(x => !flipped.has(x))
				.concat([...flipped].filter(x => !initial.has(x)))
		);
	}

	// CRUD 함수들은 여기에 구현 (addGrade, updateProcess, deleteSymptom 등)
	// 변수::Grade CRUD 함수에 사용
	let gradeModalOpen = $state(false);
	let currentGrade = $state<RepairGrade | null>(null);
	let newGradeName = $state('');
	let newGradeCode = $state('');

	function openAddGradeModal() {
		currentGrade = null;
		newGradeName = '';
		newGradeCode = '';
		gradeModalOpen = true;
	}

	function openEditGradeModal(grade: RepairGrade) {
		currentGrade = grade;
		newGradeName = grade.name;
		newGradeCode = grade.code;
		gradeModalOpen = true;
	}

	async function saveGrade() {
		try {
			if (!newGradeName || !newGradeCode) {
				await executeMessage('이름과 코드를 모두 입력해주세요.', 'error');
				return;
			}

			const payload = {
				name: newGradeName,
				code: newGradeCode
			};

			if (currentGrade) {
				// 수정
				await authClient.put(`${apiUrl}/grades/${currentGrade.id}`, payload);
			} else {
				// 추가
				await authClient.post(`${apiUrl}/grades`, payload);
			}

			gradeModalOpen = false;
			await loadAllData(); // 데이터 새로고침
		} catch (e) {
			await handleCatch(e);
		}
	}

	async function deleteGrade(id: number) {
		const ask = await executeAsk('정말 삭제하시겠습니까? 연결된 처리 내용이 있다면 삭제할 수 없습니다.', 'warning');
		if (!ask) return;

		try {
			await authClient.delete(`${apiUrl}/grades/${id}`);
			await loadAllData(); // 데이터 새로고침
		} catch (e) {
			await handleCatch(e);
		}
	}

	// 변수::Process CRUD 함수에 사용
	let processModalOpen = $state(false);
	let currentProcess = $state<RepairProcess | null>(null);
	let newProcessName = $state('');
	let newProcessCode = $state('');
	let selectedGradeId = $state<number | null>(null);

	function openAddProcessModal() {
		currentProcess = null;
		newProcessName = '';
		newProcessCode = '';
		selectedGradeId = grades.length > 0 ? grades[0].id : null;
		processModalOpen = true;
	}

	function openEditProcessModal(process: ProcessWithGrade) {
		currentProcess = process;
		newProcessName = process.name;
		newProcessCode = process.code;
		selectedGradeId = process.repair_grade_id;
		processModalOpen = true;
	}

	async function saveProcess() {
		try {
			if (!newProcessName || !newProcessCode) {
				await executeMessage('이름, 코드, 등급을 모두 입력/선택해주세요.', 'error');
				return;
			}

			const payload = {
				name: newProcessName,
				code: newProcessCode,
			};

			if (currentProcess) {
				// 수정
				await authClient.put(`${apiUrl}/processes/${currentProcess.id}`, payload);
			} else {
				// 추가
				await authClient.post(`${apiUrl}/processes`, payload);
			}

			processModalOpen = false;
			await loadAllData(); // 데이터 새로고침
		} catch (e) {
			await handleCatch(e);
		}
	}

	async function deleteProcess(id: number) {
		const ask = await executeAsk('정말 삭제하시겠습니까? 증상에 연결된 처리 내용이 있다면 삭제할 수 없습니다.', 'warning');
		if (!ask) return;

		try {
			await authClient.delete(`${apiUrl}/processes/${id}`);
			await loadAllData(); // 데이터 새로고침
		} catch (e) {
			await handleCatch(e);
		}
	}

	// 변수::Symptom CRUD 함수에 사용
	let symptomModalOpen = $state(false);
	let currentSymptom = $state<RepairSymptom | null>(null);
	let newSymptomName = $state('');
	let newSymptomCode = $state('');
	let newSymptomType = $state('general');
	let newSymptomDefaultProcessId = $state<number | null>(null);
	let newSymptomDefaultGradeId = $state<number | null>(null);

	function openAddSymptomModal() {
		currentSymptom = null;
		newSymptomName = '';
		newSymptomCode = '';
		newSymptomType = 'general'; // Reset to default
		newSymptomDefaultProcessId = null; // Reset default process
		newSymptomDefaultGradeId = null; // Reset default grade
		symptomModalOpen = true;
	}

	function openEditSymptomModal(symptom: RepairSymptom) {
		currentSymptom = symptom;
		newSymptomName = symptom.name;
		newSymptomCode = symptom.code;
		// If the symptom has a type property, use it; otherwise default to 'general'
		newSymptomType = (symptom as any).type || 'general';
		// Set the default process and grade IDs if they exist
		newSymptomDefaultProcessId = symptom.default_repair_process_id || null;
		newSymptomDefaultGradeId = symptom.default_repair_grade_id || null;
		symptomModalOpen = true;
	}

	async function saveSymptom() {
		try {
			if (!newSymptomName || !newSymptomCode) {
				await executeMessage('이름과 코드를 모두 입력해주세요.', 'error');
				return;
			}

			const payload = {
				name: newSymptomName,
				code: newSymptomCode,
				type: newSymptomType,
				default_repair_process_id: newSymptomDefaultProcessId,
				default_repair_grade_id: newSymptomDefaultGradeId
			};

			if (currentSymptom) {
				// 수정
				await authClient.put(`${apiUrl}/symptoms/${currentSymptom.id}`, payload);
			} else {
				// 추가
				await authClient.post(`${apiUrl}/symptoms`, payload);
			}

			symptomModalOpen = false;
			await loadAllData(); // 데이터 새로고침
		} catch (e) {
			await handleCatch(e);
		}
	}

	async function deleteSymptom(id: number) {
		const ask = await executeAsk('정말 삭제하시겠습니까? 처리 내용에 연결된 증상이 있다면 삭제할 수 없습니다.');
		if (!ask) return;

		try {
			await authClient.delete(`${apiUrl}/symptoms/${id}`);
			await loadAllData(); // 데이터 새로고침
		} catch (e) {
			await handleCatch(e);
		}
	}

	onMount(async () => {
		await loadAllData();
		activeTab = 0;
	});

	let breadcrumbs: Breadcrumb[] = [
		{ title: '설정', url: localUrl },
		{ title: '수리 정책 통합 관리', url: localUrl }
	];
</script>

<AppLayout {user}>
	{#snippet main ()}
		<div>
			<TitleBar {breadcrumbs} />
			
			<div class="p-4">
				<!-- 탭 네비게이션 -->
				<div class="tabs tabs-boxed mb-6">
					<button class="tab" class:tab-active={activeTab === 0} onclick={() => activeTab = 0}>
						1. 증상 내용 관리
					</button>
					<button class="tab" class:tab-active={activeTab === 1} onclick={() => activeTab = 1}>
						2. 처리 내용 관리
					</button>
					<button class="tab" class:tab-active={activeTab === 2} onclick={() => activeTab = 2}>
						3. 수리 등급 관리
					</button>
					<button class="tab" class:tab-active={activeTab === 3} onclick={() => activeTab = 3}>
						4. 증상 및 처리 내용 연결
					</button>
					<button class="tab" class:tab-active={activeTab === 4} onclick={() => activeTab = 4}>
						5. 처리 내용 및 수리 등급 연결
					</button>
				</div>
				
				<!-- 탭 콘텐츠 -->
				<div class="mt-4">
					<!-- 1. 증상 내용 관리 탭 -->
					{#if activeTab === 0}
						<section class="card bg-base-100 shadow-xl">
							<div class="card-body">
								<h2 class="card-title">1. 증상 내용 (Repair Symptoms)</h2>
								<p>수리가 필요한 증상 내용을 관리합니다.</p>
								<div class="flex justify-between mb-4">
									<div class="flex items-center gap-4">
										<span class="font-bold">구분 필터:</span>
										<div class="flex gap-4">
											<label class="label cursor-pointer">
												<span class="label-text mr-2">전체</span>
												<input type="radio" name="filter-type" class="radio radio-primary" value="all"
															 bind:group={symptomTypeFilter} checked={symptomTypeFilter === 'all'}
															 onchange={filterSymptomsByType} />
											</label>
											<label class="label cursor-pointer">
												<span class="label-text mr-2">일반</span>
												<input type="radio" name="filter-type" class="radio radio-primary" value="general"
															 bind:group={symptomTypeFilter} onchange={filterSymptomsByType} />
											</label>
											<label class="label cursor-pointer">
												<span class="label-text mr-2">애플</span>
												<input type="radio" name="filter-type" class="radio radio-primary" value="apple"
															 bind:group={symptomTypeFilter} onchange={filterSymptomsByType} />
											</label>
										</div>
									</div>
									<button class="btn btn-primary" onclick={openAddSymptomModal}>
										<Icon data={faPlus} />
										증상 추가
									</button>
								</div>
								
								<div class="overflow-x-auto">
									<table class="table table-xs table-zebra w-full">
										<thead>
										<tr>
											<th>ID</th>
											<th>구분</th>
											<th>이름</th>
											<th>코드</th>
											<th>기본 처리내용</th>
											<th>기본 등급</th>
											<th>작업</th>
										</tr>
										</thead>
										<tbody>
										{#each filteredSymptoms as symptom (symptom.id)}
											<tr>
												<td>{symptom.sequentialId}</td>
												<td>{symptom.type === 'apple' ? '애플' : '일반'}</td>
												<td>{symptom.name}</td>
												<td>{symptom.code}</td>
												<td>{symptom.default_repair_process_id ? processes.find(p => p.id === symptom.default_repair_process_id)?.name || '미지정' : '미지정'}</td>
												<td>{symptom.default_repair_grade_id ? grades.find(g => g.id === symptom.default_repair_grade_id)?.name || '미지정' : '미지정'}</td>
												<td class="flex gap-2">
													<button class="btn btn-sm btn-info" onclick={() => openEditSymptomModal(symptom)}>
														<Icon data={faPenToSquare} />
													</button>
													<button class="btn btn-sm btn-error" onclick={() => deleteSymptom(symptom.id)}>
														<Icon data={faTrashCan} />
													</button>
												</td>
											</tr>
										{:else}
											<tr>
												<td colspan="7" class="text-center py-4">등록된 증상이 없습니다.</td>
											</tr>
										{/each}
										</tbody>
									</table>
								</div>
								
								<!-- Symptom Modal -->
								{#if symptomModalOpen}
									<div class="modal modal-open">
										<div class="modal-box">
											<h3 class="font-bold text-lg">{currentSymptom ? '증상 수정' : '증상 추가'}</h3>
											<div class="form-control w-full">
												<label class="label" for="">
													<span class="label-text">구분(type)</span>
												</label>
												<div class="flex gap-4">
													<label class="label cursor-pointer">
														<span class="label-text mr-2">일반</span>
														<input type="radio" name="symptom-type" class="radio radio-primary" value="general"
																	 bind:group={newSymptomType} checked={newSymptomType === 'general'} />
													</label>
													<label class="label cursor-pointer">
														<span class="label-text mr-2">애플</span>
														<input type="radio" name="symptom-type" class="radio radio-primary" value="apple"
																	 bind:group={newSymptomType} />
													</label>
												</div>
											</div>
											<div class="form-control w-full">
												<label class="label" for="">
													<span class="label-text">이름</span>
												</label>
												<input type="text" bind:value={newSymptomName} placeholder="증상 이름"
															 class="input input-bordered w-full" />
											</div>
											<div class="form-control w-full">
												<label class="label" for="">
													<span class="label-text">코드</span>
												</label>
												<input type="text" bind:value={newSymptomCode} placeholder="증상 코드"
															 class="input input-bordered w-full" />
											</div>
											<div class="form-control w-full">
												<label class="label" for="">
													<span class="label-text">기본 처리내용 (Default Process)</span>
												</label>
												<select bind:value={newSymptomDefaultProcessId} class="select select-bordered w-full">
													<option value={null}>선택 안함</option>
													{#each processes as process (process.id)}
														<option value={process.id}>{process.name} ({process.code})</option>
													{/each}
												</select>
											</div>
											<div class="form-control w-full">
												<label class="label" for="">
													<span class="label-text">기본 등급 (Default Grade)</span>
												</label>
												<select bind:value={newSymptomDefaultGradeId} class="select select-bordered w-full">
													<option value={null}>선택 안함</option>
													{#each grades as grade (grade.id)}
														<option value={grade.id}>{grade.name} ({grade.code})</option>
													{/each}
												</select>
											</div>
											<div class="modal-action">
												<button class="btn" onclick={() => symptomModalOpen = false}>취소</button>
												<button class="btn btn-primary" onclick={saveSymptom}>저장</button>
											</div>
										</div>
									</div>
								{/if}
							</div>
						</section>
					{/if}
					
					<!-- 2. 처리 내용 관리 탭 -->
					{#if activeTab === 1}
						<section class="card bg-base-100 shadow-xl">
							<div class="card-body">
								<h2 class="card-title">2. 처리 내용 (Repair Processes)</h2>
								<p>각 처리 내용은 반드시 하나의 수리 등급에 속해야 합니다.</p>
								<div class="flex justify-end mb-4">
									<button class="btn btn-primary" onclick={openAddProcessModal}>
										<Icon data={faPlus} />
										처리 내용 추가
									</button>
								</div>
								
								<div class="overflow-x-auto">
									<table class="table table-xs table-zebra w-full">
										<thead>
										<tr>
											<th>ID</th>
											<th>이름</th>
											<th>코드</th>
											<th>작업</th>
										</tr>
										</thead>
										<tbody>
										{#each processes as process (process.id)}
											<tr>
												<td>{process.id}</td>
												<td>{process.name}</td>
												<td>{process.code}</td>
												<td class="flex gap-2">
													<button class="btn btn-sm btn-info" onclick={() => openEditProcessModal(process)}>
														<Icon data={faPenToSquare} />
													</button>
													<button class="btn btn-sm btn-error" onclick={() => deleteProcess(process.id)}>
														<Icon data={faTrashCan} />
													</button>
												</td>
											</tr>
										{:else}
											<tr>
												<td colspan="5" class="text-center py-4">등록된 처리 내용이 없습니다.</td>
											</tr>
										{/each}
										</tbody>
									</table>
								</div>
								
								<!-- Process Modal -->
								{#if processModalOpen}
									<div class="modal modal-open">
										<div class="modal-box">
											<h3 class="font-bold text-lg">{currentProcess ? '처리 내용 수정' : '처리 내용 추가'}</h3>
											<div class="form-control w-full">
												<label class="label" for="">
													<span class="label-text">이름</span>
												</label>
												<input type="text" bind:value={newProcessName} placeholder="처리 내용 이름"
															 class="input input-bordered w-full" />
											</div>
											<div class="form-control w-full">
												<label class="label" for="">
													<span class="label-text">코드</span>
												</label>
												<input type="text" bind:value={newProcessCode} placeholder="처리 내용 코드"
															 class="input input-bordered w-full" />
											</div>
											<div class="modal-action">
												<button class="btn" onclick={() => processModalOpen = false}>취소</button>
												<button class="btn btn-primary" onclick={saveProcess}>저장</button>
											</div>
										</div>
									</div>
								{/if}
							</div>
						</section>
					{/if}
					
					<!-- 3. 수리 등급 관리 탭 -->
					{#if activeTab === 2}
						<section class="card bg-base-100 shadow-xl">
							<div class="card-body">
								<h2 class="card-title">3. 수리 등급 (Repair Grades)</h2>
								<p>모든 처리 내용의 기준이 되는 등급입니다.</p>
								<div class="flex justify-end mb-4">
									<button class="btn btn-primary" onclick={openAddGradeModal}>
										<Icon data={faPlus} />
										등급 추가
									</button>
								</div>
								
								<div class="overflow-x-auto">
									<table class="table table-xs table-zebra w-full">
										<thead>
										<tr>
											<th>ID</th>
											<th>이름</th>
											<th>코드</th>
											<th>작업</th>
										</tr>
										</thead>
										<tbody>
										{#each grades as grade (grade.id)}
											<tr>
												<td>{grade.id}</td>
												<td>{grade.name}</td>
												<td>{grade.code}</td>
												<td class="flex gap-2">
													<button class="btn btn-sm btn-info" onclick={() => openEditGradeModal(grade)}>
														<Icon data={faPenToSquare} />
													</button>
													<button class="btn btn-sm btn-error" onclick={() => deleteGrade(grade.id)}>
														<Icon data={faTrashCan} />
													</button>
												</td>
											</tr>
										{:else}
											<tr>
												<td colspan="4" class="text-center py-4">등록된 등급이 없습니다.</td>
											</tr>
										{/each}
										</tbody>
									</table>
								</div>
								
								<!-- Grade Modal -->
								{#if gradeModalOpen}
									<div class="modal modal-open">
										<div class="modal-box">
											<h3 class="font-bold text-lg">{currentGrade ? '등급 수정' : '등급 추가'}</h3>
											<div class="form-control w-full">
												<label class="label" for="">
													<span class="label-text">이름</span>
												</label>
												<input type="text" bind:value={newGradeName} placeholder="등급 이름"
															 class="input input-bordered w-full" />
											</div>
											<div class="form-control w-full">
												<label class="label" for="">
													<span class="label-text">코드</span>
												</label>
												<input type="text" bind:value={newGradeCode} placeholder="등급 코드"
															 class="input input-bordered w-full" />
											</div>
											<div class="modal-action">
												<button class="btn" onclick={() => gradeModalOpen = false}>취소</button>
												<button class="btn btn-primary" onclick={saveGrade}>저장</button>
											</div>
										</div>
									</div>
								{/if}
							</div>
						</section>
					{/if}
					
					<!-- 4. 증상 및 처리 내용 매핑 탭 -->
					{#if activeTab === 3}
						<section class="card bg-base-100 shadow-xl">
							<div class="card-body">
								<h2 class="card-title">4. 증상 및 처리 내용 연결 (증상 ↔ 처리 연결)</h2>
								<p>좌측에서 증상을 선택한 후, 우측에서 해당 증상에 적용할 수 있는 처리 내용을 모두 체크하고 저장하세요.</p>
								<div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
									<!-- 4-1. 증상 목록 -->
									<div class="card bg-base-200 p-4">
										<h3 class="font-bold text-lg mb-2">증상 목록</h3>
										<div class="max-h-96 overflow-y-auto">
											<ul class="menu">
												{#each symptoms as symptom (symptom.id)}
													<li>
														<button class:active={selectedSymptom?.id === symptom.id}
																		onclick={() => selectSymptom(symptom)}
														>
															{symptom.name} ({symptom.code})
														</button>
													</li>
												{/each}
											</ul>
										</div>
									</div>
									
									<!-- 4-2. 연결 가능한 처리 내용 목록 -->
									<div class="card bg-base-200 p-4">
										<h3 class="font-bold text-lg mb-2">
											{#if selectedSymptom}
												<span class="text-primary">'{selectedSymptom.name}'</span> 에 연결할 처리 내용
											{:else}
												처리 내용 목록
											{/if}
										</h3>
										{#if selectedSymptom}
											<div class="space-y-2 max-h-96 overflow-y-auto">
												<div class="flex flex-wrap gap-2">
													{#each processes as process (process.id)}
														<button
															class="btn btn-sm justify-start overflow-hidden text-ellipsis"
															class:btn-primary={initialProcessIds.has(process.id) && !flippedProcessIds.has(process.id)}
															class:btn-secondary={isProcessSelected(process.id) && flippedProcessIds.has(process.id)}
															class:btn-outline={!isProcessSelected(process.id)}
															onclick={() => handleProcessClick(process.id)}
														>
															{process.name} <span class="badge badge-sm ml-1">{process.gradeName}</span>
														</button>
													{/each}
												</div>
											</div>
											<div class="card-actions justify-end mt-4">
												<button class="btn btn-primary" onclick={saveProcessMapping}>
													<Icon data={faLink} />
													저장
												</button>
											</div>
										{:else}
											<p class="text-center text-gray-500 mt-10">좌측에서 증상을 선택해주세요.</p>
										{/if}
									</div>
								</div>
							</div>
						</section>
					{/if}
					
					<!-- 5. 처리 내용 및 수리 등급 매핑 탭 -->
					{#if activeTab === 4}
						<section class="card bg-base-100 shadow-xl">
							<div class="card-body">
								<h2 class="card-title">5. 처리 내용 및 수리 등급 연결 (처리 ↔ 등급 연결)</h2>
								<p>좌측에서 처리 내용을 선택한 후, 우측에서 등급에 적용할 수 있는 내용을 모두 체크하고 저장하세요.</p>
								<div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
									<!-- 5-1. 처리 내용 목록 -->
									<div class="card bg-base-200 p-4">
										<h3 class="font-bold text-lg mb-2">처리 내용 목록</h3>
										<div class="max-h-96 overflow-y-auto">
											<ul class="menu">
												{#each processes as process (process.id)}
													<li>
														<button class:active={selectedProcess?.id === process.id}
																		onclick={() => selectProcess(process)}
														>
															{process.name} ({process.code})
														</button>
													</li>
												{/each}
											</ul>
										</div>
									</div>
									
									<!-- 5-2. 연결 가능한 수리 등급 목록 -->
									<div class="card bg-base-200 p-4">
										<h3 class="font-bold text-lg mb-2">
											{#if selectedProcess}
												<span class="text-primary">'{selectedProcess.name}'</span> 에 연결할 수리 등급
											{:else}
												수리 등급 목록
											{/if}
										</h3>
										{#if selectedProcess}
											<div class="space-y-2 max-h-96 overflow-y-auto">
												<div class="flex flex-wrap gap-2">
													{#each grades as grade (grade.id)}
														<button
															class="btn btn-sm justify-start overflow-hidden text-ellipsis"
															class:btn-primary={initialGradeIds.has(grade.id) && !flippedGradeIds.has(grade.id)}
															class:btn-secondary={isGradeSelected(grade.id) && flippedGradeIds.has(grade.id)}
															class:btn-outline={!isGradeSelected(grade.id)}
															onclick={() => handleGradeClick(grade.id)}
														>
															{grade.name} <span class="badge badge-sm ml-1">{grade.code}</span>
														</button>
													{/each}
												</div>
											</div>
											<div class="card-actions justify-end mt-4">
												<button class="btn btn-primary" onclick={saveGradeMapping}>
													<Icon data={faLink} />
													저장
												</button>
											</div>
										{:else}
											<p class="text-center text-gray-500 mt-10">좌측에서 처리 내용을 선택해주세요.</p>
										{/if}
									</div>
								</div>
							</div>
						</section>
					{/if}
				</div>
			</div>
		</div>
	{/snippet}
</AppLayout>
