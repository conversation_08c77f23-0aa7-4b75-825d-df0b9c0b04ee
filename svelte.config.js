import adapter from '@sveltejs/adapter-static';
import preprocess from 'svelte-preprocess';

/** @type {import('@sveltejs/kit').Config} */
const config = {
	// preprocessors에 관한 더 많은 정보를 위해
	// Consult https://kit.svelte.dev/docs/integrations#preprocessors
	preprocess: {
		postcss: true
	},

	kit: {
		adapter: adapter({
			fallback: '/login'
		}),
		alias: {
			$src: './src',
			$lib: './src/lib',
			$components: './src/lib/components',
			$stores: './src/lib/stores'
		}
	}
};

export default config;
