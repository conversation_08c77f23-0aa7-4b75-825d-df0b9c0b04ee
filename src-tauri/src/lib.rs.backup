// Learn more about <PERSON>ri commands at https://tauri.app/develop/calling-rust/
// 충돌이 나는 로그를 찍어볼 수 있다.
// Cargo.toml 의 [dependencies]에 아래 세 가지 플러그인 추가
// tauri-plugin-log = "2"
// log = "0.4"
// chrono = "0.4"
// 권한 파일(src-tauri/capabilities/default.json) 에 "log:default" 추가 할 것
#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
use std::panic;
use tauri_plugin_updater::UpdaterExt;
use tauri_plugin_printer;
use tauri_plugin_log;
use log;
use chrono;
use std::fs::{self, File, OpenOptions};
use std::io::Write;
use std::path::PathBuf;

pub fn run() {
    // 콘솔에 시작 메시지 출력 (디버깅용)
    println!("애플리케이션 시작 중...");

    // 실행 파일 위치 확인
    let exe_dir = match std::env::current_exe() {
        Ok(path) => {
            let parent = path.parent().unwrap_or(std::path::Path::new(".")).to_path_buf();
            println!("실행 파일 디렉토리: {:?}", parent);
            parent
        },
        Err(e) => {
            println!("실행 파일 경로를 가져올 수 없음: {}", e);
            PathBuf::from(".")
        }
    };

    // 로그 디렉토리 생성
    let log_dir = exe_dir.join("logs");
    println!("로그 디렉토리 경로: {:?}", log_dir);

    if !log_dir.exists() {
        println!("로그 디렉토리가 존재하지 않음, 생성 시도...");
        match fs::create_dir_all(&log_dir) {
            Ok(_) => println!("로그 디렉토리 생성 성공"),
            Err(e) => println!("로그 디렉토리 생성 실패: {}", e)
        }
    } else {
        println!("로그 디렉토리가 이미 존재함");
    }

    // 시작 로그 직접 기록
    let startup_log = log_dir.join("tauri_startup.log");
    println!("시작 로그 파일 경로: {:?}", startup_log);

    let result = OpenOptions::new()
        .create(true)
        .append(true)
        .open(&startup_log);

    match result {
        Ok(mut file) => {
            println!("시작 로그 파일 열기 성공");
            let timestamp = chrono::Local::now().format("%Y-%m-%d %H:%M:%S").to_string();
            match writeln!(file, "[{}] 애플리케이션 시작", timestamp) {
                Ok(_) => println!("시작 로그 기록 성공"),
                Err(e) => println!("시작 로그 기록 실패: {}", e)
            }
        },
        Err(e) => {
            println!("시작 로그 파일 열기 실패: {}", e);
            // 대체 로그 파일 시도
            let fallback_log = exe_dir.join("tauri_fallback.log");
            println!("대체 로그 파일 시도: {:?}", fallback_log);

            match File::create(&fallback_log) {
                Ok(mut file) => {
                    let timestamp = chrono::Local::now().format("%Y-%m-%d %H:%M:%S").to_string();
                    match writeln!(file, "[{}] 애플리케이션 시작 (대체 로그)", timestamp) {
                        Ok(_) => println!("대체 로그 기록 성공"),
                        Err(e) => println!("대체 로그 기록 실패: {}", e)
                    }
                },
                Err(e) => println!("대체 로그 파일 생성 실패: {}", e)
            }
        }
    };

    // 패닉 핸들러 설정
    let crash_log_path = log_dir.join("tauri_crash.log");
    println!("충돌 로그 파일 경로: {:?}", crash_log_path);

    let crash_log_path_clone = crash_log_path.clone();
    panic::set_hook(Box::new(move |panic_info| {
        println!("애플리케이션 충돌 발생!");
        let backtrace = std::backtrace::Backtrace::capture();
        let timestamp = chrono::Local::now().format("%Y-%m-%d %H:%M:%S").to_string();
        let panic_message = format!("[{}] 애플리케이션 충돌! 패닉 정보: {}\n백트레이스: {}",
                                   timestamp, panic_info, backtrace);

        println!("충돌 로그 기록 시도 중...");
        // 직접 파일에 기록
        match OpenOptions::new()
            .create(true)
            .append(true)
            .open(&crash_log_path_clone) {
                Ok(mut file) => {
                    match writeln!(file, "{}", panic_message) {
                        Ok(_) => println!("충돌 로그 기록 성공"),
                        Err(e) => println!("충돌 로그 기록 실패: {}", e)
                    }
                },
                Err(e) => {
                    println!("충돌 로그 파일 열기 실패: {}", e);
                    // 최후의 수단으로 현재 디렉토리에 기록
                    match File::create("./tauri_crash_emergency.log") {
                        Ok(mut file) => {
                            match writeln!(file, "{}", panic_message) {
                                Ok(_) => println!("긴급 충돌 로그 기록 성공"),
                                Err(e) => println!("긴급 충돌 로그 기록 실패: {}", e)
                            }
                        },
                        Err(e) => println!("긴급 로그 파일 생성 실패: {}", e)
                    }
                }
            }

        eprintln!("애플리케이션이 충돌했습니다! 로그가 {:?}에 저장되었습니다", crash_log_path_clone);
    }));

    // 표준 로그 플러그인 설정
    println!("Tauri 로그 플러그인 설정 중...");
    let log_plugin = tauri_plugin_log::Builder::new()
        .targets([
            tauri_plugin_log::Target::new(tauri_plugin_log::TargetKind::Stdout),
            tauri_plugin_log::Target::new(tauri_plugin_log::TargetKind::LogDir {
                file_name: Some("app_log".to_string()),
            }),
        ])
        .level(log::LevelFilter::Info)
        .build();

    println!("Tauri 빌더 설정 중...");
    tauri::Builder::default()
        .plugin(tauri_plugin_process::init())
        .plugin(tauri_plugin_updater::Builder::new().build())
        .plugin(tauri_plugin_printer::init())
        .plugin(tauri_plugin_fs::init())
        .plugin(tauri_plugin_dialog::init())
        .plugin(tauri_plugin_opener::init())
        .plugin(log_plugin)
        .invoke_handler(tauri::generate_handler![greet])
        .setup(|app| {
            println!("Tauri 앱 설정 시작...");
            // 앱 설정 로그 직접 기록
            let setup_log = log_dir.join("tauri_setup.log");
            println!("설정 로그 파일 경로: {:?}", setup_log);

            let result = OpenOptions::new()
                .create(true)
                .append(true)
                .open(setup_log);

            match result {
                Ok(mut setup_file) => {
                    println!("설정 로그 파일 열기 성공");
                    let timestamp = chrono::Local::now().format("%Y-%m-%d %H:%M:%S").to_string();
                    match writeln!(setup_file, "[{}] 앱 설정 시작", timestamp) {
                        Ok(_) => println!("설정 시작 로그 기록 성공"),
                        Err(e) => println!("설정 시작 로그 기록 실패: {}", e)
                    }

                    let handle = app.handle().clone();
                    println!("업데이트 체크 작업 시작...");
                    tauri::async_runtime::spawn(async move {
                        println!("업데이트 체크 중...");
                        if let Err(e) = update(handle).await {
                            println!("업데이트 오류 발생: {:?}", e);
                            // 업데이트 오류 직접 기록
                            let update_log = log_dir.join("tauri_update_error.log");
                            println!("업데이트 오류 로그 파일 경로: {:?}", update_log);

                            match OpenOptions::new()
                                .create(true)
                                .append(true)
                                .open(update_log) {
                                    Ok(mut update_file) => {
                                        let timestamp = chrono::Local::now().format("%Y-%m-%d %H:%M:%S").to_string();
                                        match writeln!(update_file, "[{}] 업데이트 오류: {:?}", timestamp, e) {
                                            Ok(_) => println!("업데이트 오류 로그 기록 성공"),
                                            Err(e) => println!("업데이트 오류 로그 기록 실패: {}", e)
                                        }
                                    },
                                    Err(e) => {
                                        println!("업데이트 오류 로그 파일 열기 실패: {}", e);
                                        // 대체 로그 파일 시도
                                        match File::create(exe_dir.join("tauri_update_fallback.log")) {
                                            Ok(mut file) => {
                                                let timestamp = chrono::Local::now().format("%Y-%m-%d %H:%M:%S").to_string();
                                                match writeln!(file, "[{}] 업데이트 오류: {:?}", timestamp, e) {
                                                    Ok(_) => println!("대체 업데이트 오류 로그 기록 성공"),
                                                    Err(e) => println!("대체 업데이트 오류 로그 기록 실패: {}", e)
                                                }
                                            },
                                            Err(e) => println!("대체 업데이트 오류 로그 파일 생성 실패: {}", e)
                                        }
                                    }
                                }
                        } else {
                            println!("업데이트 체크 완료");
                        }
                    });

                    match writeln!(setup_file, "[{}] 앱 설정 완료", chrono::Local::now().format("%Y-%m-%d %H:%M:%S")) {
                        Ok(_) => println!("설정 완료 로그 기록 성공"),
                        Err(e) => println!("설정 완료 로그 기록 실패: {}", e)
                    }
                },
                Err(e) => {
                    println!("설정 로그 파일 열기 실패: {}", e);
                    // 대체 로그 파일 시도
                    match File::create(exe_dir.join("tauri_setup_fallback.log")) {
                        Ok(mut file) => {
                            let timestamp = chrono::Local::now().format("%Y-%m-%d %H:%M:%S").to_string();
                            match writeln!(file, "[{}] 앱 설정 (대체 로그)", timestamp) {
                                Ok(_) => println!("대체 설정 로그 기록 성공"),
                                Err(e) => println!("대체 설정 로그 기록 실패: {}", e)
                            }
                        },
                        Err(e) => println!("대체 설정 로그 파일 생성 실패: {}", e)
                    }
                }
            };

            println!("Tauri 앱 설정 완료");
            Ok(())
        })
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}

async fn update(app: tauri::AppHandle) -> tauri_plugin_updater::Result<()> {
    println!("업데이트 함수 시작");
    if let Some(update) = app.updater()?.check().await? {
        println!("업데이트 발견!");
        let mut downloaded = 0;

        // alternatively, we could also call update.download() and update.install() separately
        update
        .download_and_install(
            |chunk_length, content_length| {
                downloaded += chunk_length;
                println!("downloaded {downloaded} from {content_length:?}");
            },
            || {
                println!("download finished");
            },
        )
        .await?;

        println!("update installed");
        app.restart();
    } else {
        println!("업데이트 없음");
    }

    println!("업데이트 함수 종료");
    Ok(())
}